# Server Configuration
NODE_ENV=development
PORT=8000
HOST=localhost

# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/unified_project_hub
DATABASE_POOL_SIZE=10

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# File Upload Configuration
FILE_UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,txt,md

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# AWS S3 Configuration (optional, for file storage)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
AWS_S3_BUCKET=unified-project-hub-files

# MinIO Configuration (S3-compatible, for local development)
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=project-files

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=your-session-secret-key
SESSION_COOKIE_MAX_AGE=86400000

# API Configuration
API_VERSION=v1
API_PREFIX=/api

# WebSocket Configuration
WS_CORS_ORIGIN=http://localhost:3000

# External Integrations
SLACK_WEBHOOK_URL=
GOOGLE_DRIVE_API_KEY=
GOOGLE_DRIVE_CLIENT_ID=

# Security
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=8

# Application Settings
APP_NAME=Unified Project Hub
APP_VERSION=1.0.0
APP_URL=http://localhost:3000

# Development Settings
DEBUG=true
ENABLE_SWAGGER=true
ENABLE_CORS=true

# Production Settings (override in production)
# NODE_ENV=production
# DEBUG=false
# ENABLE_SWAGGER=false
# DATABASE_SSL=true
