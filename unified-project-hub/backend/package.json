{"name": "unified-project-hub-backend", "version": "1.0.0", "description": "Backend API for Unified Project Hub", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "prisma db seed", "db:reset": "prisma migrate reset", "db:generate": "prisma generate", "db:studio": "prisma studio", "docs:generate": "swagger-jsdoc -d swaggerDef.js src/routes/*.ts -o docs/swagger.json", "docs:serve": "swagger-ui-serve docs/swagger.json"}, "dependencies": {"@prisma/client": "^5.6.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "redis": "^4.6.10", "nodemailer": "^6.9.7", "winston": "^3.11.0", "compression": "^1.7.4", "express-validator": "^7.0.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "date-fns": "^2.30.0", "lodash": "^4.17.21"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.9.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/nodemailer": "^6.4.14", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.2.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "prettier": "^3.1.0", "prisma": "^5.6.0"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/index.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "engines": {"node": ">=18.0.0"}}