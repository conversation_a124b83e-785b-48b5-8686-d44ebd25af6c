-- First, let's see the current constraint
SELECT conname, pg_get_constraintdef(oid) 
FROM pg_constraint 
WHERE conrelid = 'projects'::regclass AND contype = 'c';

-- Drop the existing status check constraint
ALTER TABLE projects DROP CONSTRAINT IF EXISTS projects_status_check;

-- Add the updated constraint that includes 'archived'
ALTER TABLE projects ADD CONSTRAINT projects_status_check 
CHECK (status IN ('not-started', 'in-progress', 'completed', 'backlog', 'archived'));