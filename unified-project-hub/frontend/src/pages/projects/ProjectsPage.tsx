import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { formatDate, getStatusColor } from '@/lib/utils'

interface ProjectsPageProps {
  filter?: 'my' | 'active' | 'archived'
}

export default function ProjectsPage({ filter }: ProjectsPageProps) {
  const { user } = useAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [isLoading, setIsLoading] = useState(false)

  // Mock data - replace with actual API call
  const mockProjects = [
    {
      id: '1',
      name: 'E-commerce Platform Redesign',
      description: 'Complete overhaul of the existing e-commerce platform with modern UI/UX',
      status: 'in-progress',
      company_name: 'Acme Corporation',
      progress_percentage: 65,
      priority_level: 'P1',
      start_date: '2024-01-15',
      end_date: '2024-06-30',
      project_lead: { name: '<PERSON>', avatar_url: null },
      team_members: [
        { user: { name: '<PERSON>', avatar_url: null } },
        { user: { name: 'Carol Davis', avatar_url: null } },
      ],
      tasks_completed: 12,
      tasks_total: 18,
    },
    {
      id: '2',
      name: 'Internal Analytics Dashboard',
      description: 'Build internal dashboard for team productivity analytics',
      status: 'in-progress',
      company_name: 'ProjectHub Internal',
      progress_percentage: 40,
      priority_level: 'P2',
      start_date: '2024-02-01',
      end_date: '2024-04-30',
      project_lead: { name: 'Bob Smith', avatar_url: null },
      team_members: [
        { user: { name: 'Carol Davis', avatar_url: null } },
      ],
      tasks_completed: 8,
      tasks_total: 20,
    },
    {
      id: '3',
      name: 'Mobile App Development',
      description: 'Native mobile application for iOS and Android',
      status: 'requirements',
      company_name: 'TechStartup Inc',
      progress_percentage: 15,
      priority_level: 'P1',
      start_date: '2024-03-01',
      end_date: '2024-09-30',
      project_lead: { name: 'Alice Johnson', avatar_url: null },
      team_members: [
        { user: { name: 'Bob Smith', avatar_url: null } },
        { user: { name: 'David Wilson', avatar_url: null } },
      ],
      tasks_completed: 3,
      tasks_total: 25,
    },
  ]

  const getPageTitle = () => {
    switch (filter) {
      case 'my':
        return 'My Projects'
      case 'active':
        return 'Active Projects'
      case 'archived':
        return 'Archived Projects'
      default:
        return 'All Projects'
    }
  }

  const getPageDescription = () => {
    switch (filter) {
      case 'my':
        return 'Projects you are leading or participating in'
      case 'active':
        return 'Currently active projects across the organization'
      case 'archived':
        return 'Completed and archived projects'
      default:
        return 'Manage and track all projects across the organization'
    }
  }

  const canCreateProject = user?.role === 'admin' || user?.role === 'project_manager'

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{getPageTitle()}</h1>
          <p className="text-gray-600 mt-1">{getPageDescription()}</p>
        </div>
        {canCreateProject && (
          <button className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/90 transition-colors">
            New Project
          </button>
        )}
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search projects..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <select
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="draft">Draft</option>
              <option value="requirements">Requirements</option>
              <option value="in-progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="on-hold">On Hold</option>
            </select>
          </div>
        </div>
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {mockProjects.map((project) => (
          <Link
            key={project.id}
            to={`/projects/${project.id}`}
            className="bg-white rounded-lg shadow hover:shadow-md transition-shadow"
          >
            <div className="p-6">
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {project.name}
                  </h3>
                  <p className="text-sm text-gray-600">{project.company_name}</p>
                </div>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                  {project.status.replace('-', ' ')}
                </span>
              </div>

              {/* Description */}
              <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                {project.description}
              </p>

              {/* Progress */}
              <div className="mb-4">
                <div className="flex justify-between text-sm text-gray-600 mb-1">
                  <span>Progress</span>
                  <span>{project.progress_percentage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{ width: `${project.progress_percentage}%` }}
                  />
                </div>
              </div>

              {/* Tasks */}
              <div className="flex justify-between text-sm text-gray-600 mb-4">
                <span>Tasks: {project.tasks_completed}/{project.tasks_total}</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(project.priority_level)}`}>
                  {project.priority_level}
                </span>
              </div>

              {/* Team */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-500">Lead:</span>
                  <div className="flex items-center space-x-1">
                    <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-xs font-medium text-gray-600">
                      {project.project_lead.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <span className="text-sm text-gray-700">{project.project_lead.name}</span>
                  </div>
                </div>
                <div className="flex -space-x-1">
                  {project.team_members.slice(0, 3).map((member, index) => (
                    <div
                      key={index}
                      className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-xs font-medium text-gray-600 border-2 border-white"
                      title={member.user.name}
                    >
                      {member.user.name.split(' ').map(n => n[0]).join('')}
                    </div>
                  ))}
                  {project.team_members.length > 3 && (
                    <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center text-xs font-medium text-gray-500 border-2 border-white">
                      +{project.team_members.length - 3}
                    </div>
                  )}
                </div>
              </div>

              {/* Dates */}
              <div className="mt-4 pt-4 border-t border-gray-100">
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Start: {formatDate(project.start_date)}</span>
                  <span>End: {formatDate(project.end_date)}</span>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Empty State */}
      {mockProjects.length === 0 && (
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No projects found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {filter === 'my' 
              ? "You're not assigned to any projects yet."
              : "No projects match your current filters."
            }
          </p>
          {canCreateProject && (
            <div className="mt-6">
              <button className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/90 transition-colors">
                Create your first project
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
