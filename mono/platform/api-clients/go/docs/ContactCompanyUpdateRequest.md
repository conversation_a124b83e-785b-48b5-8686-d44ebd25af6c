# ContactCompanyUpdateRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CompanyId** | **string** |  | 

## Methods

### NewContactCompanyUpdateRequest

`func NewContactCompanyUpdateRequest(companyId string, ) *ContactCompanyUpdateRequest`

NewContactCompanyUpdateRequest instantiates a new ContactCompanyUpdateRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewContactCompanyUpdateRequestWithDefaults

`func NewContactCompanyUpdateRequestWithDefaults() *ContactCompanyUpdateRequest`

NewContactCompanyUpdateRequestWithDefaults instantiates a new ContactCompanyUpdateRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetCompanyId

`func (o *ContactCompanyUpdateRequest) GetCompanyId() string`

GetCompanyId returns the CompanyId field if non-nil, zero value otherwise.

### GetCompanyIdOk

`func (o *ContactCompanyUpdateRequest) GetCompanyIdOk() (*string, bool)`

GetCompanyIdOk returns a tuple with the CompanyId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCompanyId

`func (o *ContactCompanyUpdateRequest) SetCompanyId(v string)`

SetCompanyId sets CompanyId field to given value.



[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


