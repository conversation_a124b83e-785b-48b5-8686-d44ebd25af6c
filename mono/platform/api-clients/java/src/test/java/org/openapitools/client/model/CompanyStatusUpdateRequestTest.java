/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.util.Arrays;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

/**
 * Model tests for CompanyStatusUpdateRequest
 */
public class CompanyStatusUpdateRequestTest {
    private final CompanyStatusUpdateRequest model = new CompanyStatusUpdateRequest();

    /**
     * Model tests for CompanyStatusUpdateRequest
     */
    @Test
    public void testCompanyStatusUpdateRequest() {
        // TODO: test CompanyStatusUpdateRequest
    }

    /**
     * Test the property 'companyStatusId'
     */
    @Test
    public void companyStatusIdTest() {
        // TODO: test companyStatusId
    }

}
