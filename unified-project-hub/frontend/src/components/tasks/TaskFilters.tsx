import React from 'react';
import { Search, X, Calendar, Filter, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';

interface TaskFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  statusFilter: string;
  onStatusFilterChange: (value: string) => void;
  projectFilter: string;
  onProjectFilterChange: (value: string) => void;
  assigneeFilter: string;
  onAssigneeFilterChange: (value: string) => void;
  priorityFilter: string;
  onPriorityFilterChange: (value: string) => void;
  effortFilter: string;
  onEffortFilterChange: (value: string) => void;
  showCompleted: boolean;
  onShowCompletedChange: (value: boolean) => void;
  showOverdue: boolean;
  onShowOverdueChange: (value: boolean) => void;
  dueDateFrom: string;
  onDueDateFromChange: (value: string) => void;
  dueDateTo: string;
  onDueDateToChange: (value: string) => void;
  sortBy: string;
  onSortByChange: (value: string) => void;
  sortOrder: string;
  onSortOrderChange: (value: string) => void;
  groupBy: string;
  onGroupByChange: (value: string) => void;
  onClearFilters: () => void;
  overdueTasksCount?: number;
  projects?: Array<{ id: string; name: string }>;
  teamMembers?: Array<{ id: string; name: string }>;
}

const TASK_STATUSES = [
  { value: 'all', label: 'All Statuses' },
  { value: 'to_do', label: 'To Do' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'in_review', label: 'In Review' },
  { value: 'done', label: 'Done' },
  { value: 'blocked', label: 'Blocked' }
];

const PRIORITY_LEVELS = [
  { value: 'all', label: 'All Priorities' },
  { value: 'P0', label: 'P0 (Critical)' },
  { value: 'P1', label: 'P1 (High)' },
  { value: 'P2', label: 'P2 (Medium)' },
  { value: 'P3', label: 'P3 (Low)' },
  { value: 'P4', label: 'P4 (Lowest)' }
];

const EFFORT_ESTIMATES = [
  { value: 'all', label: 'All Efforts' },
  { value: 'XS', label: 'XS (1-2h)' },
  { value: 'S', label: 'S (2-4h)' },
  { value: 'M', label: 'M (4-8h)' },
  { value: 'L', label: 'L (1-2d)' },
  { value: 'XL', label: 'XL (2-5d)' },
  { value: 'XXL', label: 'XXL (1w+)' }
];

const SORT_OPTIONS = [
  { value: 'name', label: 'Name' },
  { value: 'created_at', label: 'Created Date' },
  { value: 'updated_at', label: 'Updated Date' },
  { value: 'due_date', label: 'Due Date' },
  { value: 'priority_level', label: 'Priority' },
  { value: 'status', label: 'Status' },
  { value: 'effort_estimate', label: 'Effort' }
];

const GROUP_OPTIONS = [
  { value: 'none', label: 'No Grouping' },
  { value: 'status', label: 'Status' },
  { value: 'priority_level', label: 'Priority' },
  { value: 'assignee', label: 'Assignee' },
  { value: 'project', label: 'Project' },
  { value: 'due_date', label: 'Due Date' },
  { value: 'effort_estimate', label: 'Effort Estimate' }
];

export function TaskFilters({
  searchTerm,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  projectFilter,
  onProjectFilterChange,
  assigneeFilter,
  onAssigneeFilterChange,
  priorityFilter,
  onPriorityFilterChange,
  effortFilter,
  onEffortFilterChange,
  showCompleted,
  onShowCompletedChange,
  showOverdue,
  onShowOverdueChange,
  dueDateFrom,
  onDueDateFromChange,
  dueDateTo,
  onDueDateToChange,
  sortBy,
  onSortByChange,
  sortOrder,
  onSortOrderChange,
  groupBy,
  onGroupByChange,
  onClearFilters,
  overdueTasksCount = 0,
  projects = [],
  teamMembers = []
}: TaskFiltersProps) {
  const hasActiveFilters = 
    statusFilter !== 'all' || 
    projectFilter !== 'all' || 
    assigneeFilter !== 'all' || 
    priorityFilter !== 'all' || 
    effortFilter !== 'all' || 
    searchTerm || 
    dueDateFrom || 
    dueDateTo || 
    showOverdue || 
    groupBy !== 'none';

  return (
    <div className="space-y-4">
      {/* Overdue Tasks Banner */}
      {showOverdue && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-red-600" />
            <span className="text-sm font-medium text-red-800">Overdue Tasks</span>
            <Badge variant="destructive" className="ml-auto">
              {overdueTasksCount} overdue task{overdueTasksCount !== 1 ? 's' : ''}
            </Badge>
          </div>
          <p className="text-xs text-red-600 mt-1">
            Showing only tasks that are past their due date.
          </p>
        </div>
      )}

      {/* Search and Quick Toggles */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search tasks by name, description, or project..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-8"
          />
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="show-completed"
              checked={showCompleted}
              onCheckedChange={onShowCompletedChange}
            />
            <Label htmlFor="show-completed" className="text-sm whitespace-nowrap">
              Show completed
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="show-overdue"
              checked={showOverdue}
              onCheckedChange={onShowOverdueChange}
            />
            <Label htmlFor="show-overdue" className="text-sm whitespace-nowrap flex items-center gap-2">
              <Clock className="h-3 w-3" />
              Overdue only
              {overdueTasksCount > 0 && (
                <Badge variant="destructive" className="ml-1 text-xs">
                  {overdueTasksCount}
                </Badge>
              )}
            </Label>
          </div>
        </div>
      </div>
      
      {/* Main Filters Row */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Select value={statusFilter} onValueChange={onStatusFilterChange}>
          <SelectTrigger className="w-full sm:w-[140px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            {TASK_STATUSES.map((status) => (
              <SelectItem key={status.value} value={status.value}>
                {status.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={priorityFilter} onValueChange={onPriorityFilterChange}>
          <SelectTrigger className="w-full sm:w-[140px]">
            <SelectValue placeholder="Priority" />
          </SelectTrigger>
          <SelectContent>
            {PRIORITY_LEVELS.map((priority) => (
              <SelectItem key={priority.value} value={priority.value}>
                {priority.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={effortFilter} onValueChange={onEffortFilterChange}>
          <SelectTrigger className="w-full sm:w-[140px]">
            <SelectValue placeholder="Effort" />
          </SelectTrigger>
          <SelectContent>
            {EFFORT_ESTIMATES.map((effort) => (
              <SelectItem key={effort.value} value={effort.value}>
                {effort.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={projectFilter} onValueChange={onProjectFilterChange}>
          <SelectTrigger className="w-full sm:w-[160px]">
            <SelectValue placeholder="Project" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Projects</SelectItem>
            {projects.map((project) => (
              <SelectItem key={project.id} value={project.id}>
                {project.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={assigneeFilter} onValueChange={onAssigneeFilterChange}>
          <SelectTrigger className="w-full sm:w-[140px]">
            <SelectValue placeholder="Assignee" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Assignees</SelectItem>
            {teamMembers.map((member) => (
              <SelectItem key={member.id} value={member.id}>
                {member.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Date Range Filters */}
      <div className="flex flex-col sm:flex-row gap-4 items-center">
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <Label className="text-sm">Due Date Range:</Label>
        </div>
        <div className="flex gap-2 items-center">
          <Input
            type="date"
            value={dueDateFrom}
            onChange={(e) => onDueDateFromChange(e.target.value)}
            className="w-auto"
          />
          <span className="text-sm text-muted-foreground">to</span>
          <Input
            type="date"
            value={dueDateTo}
            onChange={(e) => onDueDateToChange(e.target.value)}
            className="w-auto"
          />
        </div>
      </div>

      {/* Sorting and Grouping */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Select value={sortBy} onValueChange={onSortByChange}>
          <SelectTrigger className="w-full sm:w-[140px]">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            {SORT_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={sortOrder} onValueChange={onSortOrderChange}>
          <SelectTrigger className="w-full sm:w-[100px]">
            <SelectValue placeholder="Order" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="asc">Ascending</SelectItem>
            <SelectItem value="desc">Descending</SelectItem>
          </SelectContent>
        </Select>

        <Select value={groupBy} onValueChange={onGroupByChange}>
          <SelectTrigger className="w-full sm:w-[160px]">
            <SelectValue placeholder="Group by" />
          </SelectTrigger>
          <SelectContent>
            {GROUP_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {hasActiveFilters && (
          <Button
            variant="outline"
            onClick={onClearFilters}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  );
}
