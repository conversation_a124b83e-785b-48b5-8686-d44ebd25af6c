# ArliDocumentsGet200Response

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Documents** | Pointer to [**[]Document**](Document.md) |  | [optional] 
**TotalCount** | Pointer to **int32** |  | [optional] 
**HasMore** | Pointer to **bool** |  | [optional] 

## Methods

### NewArliDocumentsGet200Response

`func NewArliDocumentsGet200Response() *ArliDocumentsGet200Response`

NewArliDocumentsGet200Response instantiates a new ArliDocumentsGet200Response object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewArliDocumentsGet200ResponseWithDefaults

`func NewArliDocumentsGet200ResponseWithDefaults() *ArliDocumentsGet200Response`

NewArliDocumentsGet200ResponseWithDefaults instantiates a new ArliDocumentsGet200Response object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetDocuments

`func (o *ArliDocumentsGet200Response) GetDocuments() []Document`

GetDocuments returns the Documents field if non-nil, zero value otherwise.

### GetDocumentsOk

`func (o *ArliDocumentsGet200Response) GetDocumentsOk() (*[]Document, bool)`

GetDocumentsOk returns a tuple with the Documents field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDocuments

`func (o *ArliDocumentsGet200Response) SetDocuments(v []Document)`

SetDocuments sets Documents field to given value.

### HasDocuments

`func (o *ArliDocumentsGet200Response) HasDocuments() bool`

HasDocuments returns a boolean if a field has been set.

### GetTotalCount

`func (o *ArliDocumentsGet200Response) GetTotalCount() int32`

GetTotalCount returns the TotalCount field if non-nil, zero value otherwise.

### GetTotalCountOk

`func (o *ArliDocumentsGet200Response) GetTotalCountOk() (*int32, bool)`

GetTotalCountOk returns a tuple with the TotalCount field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTotalCount

`func (o *ArliDocumentsGet200Response) SetTotalCount(v int32)`

SetTotalCount sets TotalCount field to given value.

### HasTotalCount

`func (o *ArliDocumentsGet200Response) HasTotalCount() bool`

HasTotalCount returns a boolean if a field has been set.

### GetHasMore

`func (o *ArliDocumentsGet200Response) GetHasMore() bool`

GetHasMore returns the HasMore field if non-nil, zero value otherwise.

### GetHasMoreOk

`func (o *ArliDocumentsGet200Response) GetHasMoreOk() (*bool, bool)`

GetHasMoreOk returns a tuple with the HasMore field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetHasMore

`func (o *ArliDocumentsGet200Response) SetHasMore(v bool)`

SetHasMore sets HasMore field to given value.

### HasHasMore

`func (o *ArliDocumentsGet200Response) HasHasMore() bool`

HasHasMore returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


