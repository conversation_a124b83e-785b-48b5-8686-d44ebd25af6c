/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"time"
	"bytes"
	"fmt"
)

// checks if the InteractionCreate type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &InteractionCreate{}

// InteractionCreate struct for InteractionCreate
type InteractionCreate struct {
	CompanyId *string `json:"company_id,omitempty"`
	ContactId *string `json:"contact_id,omitempty"`
	InteractionType string `json:"interaction_type"`
	Notes string `json:"notes"`
	InteractionDatetime time.Time `json:"interaction_datetime"`
}

type _InteractionCreate InteractionCreate

// NewInteractionCreate instantiates a new InteractionCreate object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewInteractionCreate(interactionType string, notes string, interactionDatetime time.Time) *InteractionCreate {
	this := InteractionCreate{}
	this.InteractionType = interactionType
	this.Notes = notes
	this.InteractionDatetime = interactionDatetime
	return &this
}

// NewInteractionCreateWithDefaults instantiates a new InteractionCreate object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewInteractionCreateWithDefaults() *InteractionCreate {
	this := InteractionCreate{}
	return &this
}

// GetCompanyId returns the CompanyId field value if set, zero value otherwise.
func (o *InteractionCreate) GetCompanyId() string {
	if o == nil || IsNil(o.CompanyId) {
		var ret string
		return ret
	}
	return *o.CompanyId
}

// GetCompanyIdOk returns a tuple with the CompanyId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionCreate) GetCompanyIdOk() (*string, bool) {
	if o == nil || IsNil(o.CompanyId) {
		return nil, false
	}
	return o.CompanyId, true
}

// HasCompanyId returns a boolean if a field has been set.
func (o *InteractionCreate) HasCompanyId() bool {
	if o != nil && !IsNil(o.CompanyId) {
		return true
	}

	return false
}

// SetCompanyId gets a reference to the given string and assigns it to the CompanyId field.
func (o *InteractionCreate) SetCompanyId(v string) {
	o.CompanyId = &v
}

// GetContactId returns the ContactId field value if set, zero value otherwise.
func (o *InteractionCreate) GetContactId() string {
	if o == nil || IsNil(o.ContactId) {
		var ret string
		return ret
	}
	return *o.ContactId
}

// GetContactIdOk returns a tuple with the ContactId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionCreate) GetContactIdOk() (*string, bool) {
	if o == nil || IsNil(o.ContactId) {
		return nil, false
	}
	return o.ContactId, true
}

// HasContactId returns a boolean if a field has been set.
func (o *InteractionCreate) HasContactId() bool {
	if o != nil && !IsNil(o.ContactId) {
		return true
	}

	return false
}

// SetContactId gets a reference to the given string and assigns it to the ContactId field.
func (o *InteractionCreate) SetContactId(v string) {
	o.ContactId = &v
}

// GetInteractionType returns the InteractionType field value
func (o *InteractionCreate) GetInteractionType() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.InteractionType
}

// GetInteractionTypeOk returns a tuple with the InteractionType field value
// and a boolean to check if the value has been set.
func (o *InteractionCreate) GetInteractionTypeOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.InteractionType, true
}

// SetInteractionType sets field value
func (o *InteractionCreate) SetInteractionType(v string) {
	o.InteractionType = v
}

// GetNotes returns the Notes field value
func (o *InteractionCreate) GetNotes() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Notes
}

// GetNotesOk returns a tuple with the Notes field value
// and a boolean to check if the value has been set.
func (o *InteractionCreate) GetNotesOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Notes, true
}

// SetNotes sets field value
func (o *InteractionCreate) SetNotes(v string) {
	o.Notes = v
}

// GetInteractionDatetime returns the InteractionDatetime field value
func (o *InteractionCreate) GetInteractionDatetime() time.Time {
	if o == nil {
		var ret time.Time
		return ret
	}

	return o.InteractionDatetime
}

// GetInteractionDatetimeOk returns a tuple with the InteractionDatetime field value
// and a boolean to check if the value has been set.
func (o *InteractionCreate) GetInteractionDatetimeOk() (*time.Time, bool) {
	if o == nil {
		return nil, false
	}
	return &o.InteractionDatetime, true
}

// SetInteractionDatetime sets field value
func (o *InteractionCreate) SetInteractionDatetime(v time.Time) {
	o.InteractionDatetime = v
}

func (o InteractionCreate) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o InteractionCreate) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.CompanyId) {
		toSerialize["company_id"] = o.CompanyId
	}
	if !IsNil(o.ContactId) {
		toSerialize["contact_id"] = o.ContactId
	}
	toSerialize["interaction_type"] = o.InteractionType
	toSerialize["notes"] = o.Notes
	toSerialize["interaction_datetime"] = o.InteractionDatetime
	return toSerialize, nil
}

func (o *InteractionCreate) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"interaction_type",
		"notes",
		"interaction_datetime",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varInteractionCreate := _InteractionCreate{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varInteractionCreate)

	if err != nil {
		return err
	}

	*o = InteractionCreate(varInteractionCreate)

	return err
}

type NullableInteractionCreate struct {
	value *InteractionCreate
	isSet bool
}

func (v NullableInteractionCreate) Get() *InteractionCreate {
	return v.value
}

func (v *NullableInteractionCreate) Set(val *InteractionCreate) {
	v.value = val
	v.isSet = true
}

func (v NullableInteractionCreate) IsSet() bool {
	return v.isSet
}

func (v *NullableInteractionCreate) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableInteractionCreate(val *InteractionCreate) *NullableInteractionCreate {
	return &NullableInteractionCreate{value: val, isSet: true}
}

func (v NullableInteractionCreate) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableInteractionCreate) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


