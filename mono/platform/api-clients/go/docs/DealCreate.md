# DealCreate

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Title** | **string** |  | 
**Description** | Pointer to **string** |  | [optional] 
**EstimatedValue** | Pointer to **float64** |  | [optional] 
**CompanyId** | **string** |  | 
**DealStageId** | **string** |  | 
**ExpectedCloseDate** | Pointer to **string** |  | [optional] 

## Methods

### NewDealCreate

`func NewDealCreate(title string, companyId string, dealStageId string, ) *DealCreate`

NewDealCreate instantiates a new DealCreate object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDealCreateWithDefaults

`func NewDealCreateWithDefaults() *DealCreate`

NewDealCreateWithDefaults instantiates a new DealCreate object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetTitle

`func (o *DealCreate) GetTitle() string`

GetTitle returns the Title field if non-nil, zero value otherwise.

### GetTitleOk

`func (o *DealCreate) GetTitleOk() (*string, bool)`

GetTitleOk returns a tuple with the Title field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTitle

`func (o *DealCreate) SetTitle(v string)`

SetTitle sets Title field to given value.


### GetDescription

`func (o *DealCreate) GetDescription() string`

GetDescription returns the Description field if non-nil, zero value otherwise.

### GetDescriptionOk

`func (o *DealCreate) GetDescriptionOk() (*string, bool)`

GetDescriptionOk returns a tuple with the Description field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDescription

`func (o *DealCreate) SetDescription(v string)`

SetDescription sets Description field to given value.

### HasDescription

`func (o *DealCreate) HasDescription() bool`

HasDescription returns a boolean if a field has been set.

### GetEstimatedValue

`func (o *DealCreate) GetEstimatedValue() float64`

GetEstimatedValue returns the EstimatedValue field if non-nil, zero value otherwise.

### GetEstimatedValueOk

`func (o *DealCreate) GetEstimatedValueOk() (*float64, bool)`

GetEstimatedValueOk returns a tuple with the EstimatedValue field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetEstimatedValue

`func (o *DealCreate) SetEstimatedValue(v float64)`

SetEstimatedValue sets EstimatedValue field to given value.

### HasEstimatedValue

`func (o *DealCreate) HasEstimatedValue() bool`

HasEstimatedValue returns a boolean if a field has been set.

### GetCompanyId

`func (o *DealCreate) GetCompanyId() string`

GetCompanyId returns the CompanyId field if non-nil, zero value otherwise.

### GetCompanyIdOk

`func (o *DealCreate) GetCompanyIdOk() (*string, bool)`

GetCompanyIdOk returns a tuple with the CompanyId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCompanyId

`func (o *DealCreate) SetCompanyId(v string)`

SetCompanyId sets CompanyId field to given value.


### GetDealStageId

`func (o *DealCreate) GetDealStageId() string`

GetDealStageId returns the DealStageId field if non-nil, zero value otherwise.

### GetDealStageIdOk

`func (o *DealCreate) GetDealStageIdOk() (*string, bool)`

GetDealStageIdOk returns a tuple with the DealStageId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDealStageId

`func (o *DealCreate) SetDealStageId(v string)`

SetDealStageId sets DealStageId field to given value.


### GetExpectedCloseDate

`func (o *DealCreate) GetExpectedCloseDate() string`

GetExpectedCloseDate returns the ExpectedCloseDate field if non-nil, zero value otherwise.

### GetExpectedCloseDateOk

`func (o *DealCreate) GetExpectedCloseDateOk() (*string, bool)`

GetExpectedCloseDateOk returns a tuple with the ExpectedCloseDate field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetExpectedCloseDate

`func (o *DealCreate) SetExpectedCloseDate(v string)`

SetExpectedCloseDate sets ExpectedCloseDate field to given value.

### HasExpectedCloseDate

`func (o *DealCreate) HasExpectedCloseDate() bool`

HasExpectedCloseDate returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


