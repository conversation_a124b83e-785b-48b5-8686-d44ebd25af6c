load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "handlers",
    srcs = ["auth.go", "auth_impl.go"],
    importpath = "github.com/TwoDotAi/mono/platform/auth/handlers",
    visibility = ["//visibility:public"],
    deps = [
        "//platform/auth/config:config",
        "//platform/auth/database:database",
        "//platform/auth/middleware:middleware",
        "//platform/crm_backend/database:database",
        "//platform/rest-api:openapi_server",
        "//lib/go_lib/logging:logging",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_golang_jwt_jwt_v5//:jwt",
        "@org_golang_x_crypto//bcrypt:bcrypt",
        "@org_uber_go_zap//:zap",
    ],
)

go_test(
    name = "handlers_test",
    srcs = ["auth_test.go"],
    embed = [":handlers"],
    deps = [
        "//platform/auth/middleware:middleware",
        "//platform/auth/types:types",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_golang_jwt_jwt_v5//:jwt",
    ],
)