-- Remove existing check constraint
ALTER TABLE projects DROP CONSTRAINT projects_status_check;

-- Update existing project statuses to new values
UPDATE projects 
SET status = CASE 
  WHEN status = 'active' THEN 'in-progress'
  WHEN status = 'on_hold' THEN 'not-started'  
  WHEN status = 'completed' THEN 'completed'
  ELSE 'not-started'
END;

-- Add new check constraint with updated values
ALTER TABLE projects ADD CONSTRAINT projects_status_check 
CHECK (status IN ('not-started', 'in-progress', 'completed'));