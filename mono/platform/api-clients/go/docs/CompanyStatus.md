# CompanyStatus

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Id** | Pointer to **string** |  | [optional] 
**Name** | Pointer to **string** |  | [optional] 
**PipelineOrder** | Pointer to **int32** |  | [optional] 
**CreatedAt** | Pointer to **time.Time** |  | [optional] 

## Methods

### NewCompanyStatus

`func NewCompanyStatus() *CompanyStatus`

NewCompanyStatus instantiates a new CompanyStatus object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewCompanyStatusWithDefaults

`func NewCompanyStatusWithDefaults() *CompanyStatus`

NewCompanyStatusWithDefaults instantiates a new CompanyStatus object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetId

`func (o *CompanyStatus) GetId() string`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *CompanyStatus) GetIdOk() (*string, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *CompanyStatus) SetId(v string)`

SetId sets Id field to given value.

### HasId

`func (o *CompanyStatus) HasId() bool`

HasId returns a boolean if a field has been set.

### GetName

`func (o *CompanyStatus) GetName() string`

GetName returns the Name field if non-nil, zero value otherwise.

### GetNameOk

`func (o *CompanyStatus) GetNameOk() (*string, bool)`

GetNameOk returns a tuple with the Name field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetName

`func (o *CompanyStatus) SetName(v string)`

SetName sets Name field to given value.

### HasName

`func (o *CompanyStatus) HasName() bool`

HasName returns a boolean if a field has been set.

### GetPipelineOrder

`func (o *CompanyStatus) GetPipelineOrder() int32`

GetPipelineOrder returns the PipelineOrder field if non-nil, zero value otherwise.

### GetPipelineOrderOk

`func (o *CompanyStatus) GetPipelineOrderOk() (*int32, bool)`

GetPipelineOrderOk returns a tuple with the PipelineOrder field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPipelineOrder

`func (o *CompanyStatus) SetPipelineOrder(v int32)`

SetPipelineOrder sets PipelineOrder field to given value.

### HasPipelineOrder

`func (o *CompanyStatus) HasPipelineOrder() bool`

HasPipelineOrder returns a boolean if a field has been set.

### GetCreatedAt

`func (o *CompanyStatus) GetCreatedAt() time.Time`

GetCreatedAt returns the CreatedAt field if non-nil, zero value otherwise.

### GetCreatedAtOk

`func (o *CompanyStatus) GetCreatedAtOk() (*time.Time, bool)`

GetCreatedAtOk returns a tuple with the CreatedAt field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreatedAt

`func (o *CompanyStatus) SetCreatedAt(v time.Time)`

SetCreatedAt sets CreatedAt field to given value.

### HasCreatedAt

`func (o *CompanyStatus) HasCreatedAt() bool`

HasCreatedAt returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


