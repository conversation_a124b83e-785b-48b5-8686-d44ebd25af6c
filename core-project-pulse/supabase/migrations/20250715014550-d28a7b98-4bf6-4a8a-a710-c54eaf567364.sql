-- Add priority_order column to projects table
ALTER TABLE public.projects 
ADD COLUMN priority_order INTEGER;

-- Set initial priority values based on creation order (newest first gets lowest priority number)
UPDATE public.projects 
SET priority_order = (
  SELECT ROW_NUMBER() OVER (ORDER BY created_at DESC)
  FROM public.projects p2 
  WHERE p2.id = projects.id
);

-- Set default for new projects to be at the end
ALTER TABLE public.projects 
ALTER COLUMN priority_order SET DEFAULT 1000;

-- Add index for performance
CREATE INDEX idx_projects_priority_order ON public.projects(priority_order);

-- Create function to reorder projects efficiently
CREATE OR REPLACE FUNCTION public.reorder_projects(project_ids uuid[], new_orders integer[])
RETURNS void
LANGUAGE plpgsql
AS $function$
BEGIN
  -- Update priorities for the given projects
  FOR i IN 1..array_length(project_ids, 1) LOOP
    UPDATE public.projects 
    SET priority_order = new_orders[i], updated_at = now()
    WHERE id = project_ids[i];
  END LOOP;
END;
$function$;