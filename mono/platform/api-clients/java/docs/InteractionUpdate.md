

# InteractionUpdate


## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**interactionType** | [**InteractionTypeEnum**](#InteractionTypeEnum) |  |  [optional] |
|**notes** | **String** |  |  [optional] |
|**interactionDatetime** | **OffsetDateTime** |  |  [optional] |
|**companyId** | **UUID** |  |  [optional] |
|**contactId** | **UUID** |  |  [optional] |



## Enum: InteractionTypeEnum

| Name | Value |
|---- | -----|
| EMAIL | &quot;email&quot; |
| PHONE | &quot;phone&quot; |
| MEETING | &quot;meeting&quot; |
| DEMO | &quot;demo&quot; |
| PROPOSAL | &quot;proposal&quot; |
| FOLLOW_UP | &quot;follow-up&quot; |
| OTHER | &quot;other&quot; |



