/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
)

// checks if the InteractionsGet200Response type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &InteractionsGet200Response{}

// InteractionsGet200Response struct for InteractionsGet200Response
type InteractionsGet200Response struct {
	Interactions []InteractionWithDetails `json:"interactions,omitempty"`
	TotalCount *int32 `json:"total_count,omitempty"`
	HasMore *bool `json:"has_more,omitempty"`
}

// NewInteractionsGet200Response instantiates a new InteractionsGet200Response object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewInteractionsGet200Response() *InteractionsGet200Response {
	this := InteractionsGet200Response{}
	return &this
}

// NewInteractionsGet200ResponseWithDefaults instantiates a new InteractionsGet200Response object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewInteractionsGet200ResponseWithDefaults() *InteractionsGet200Response {
	this := InteractionsGet200Response{}
	return &this
}

// GetInteractions returns the Interactions field value if set, zero value otherwise.
func (o *InteractionsGet200Response) GetInteractions() []InteractionWithDetails {
	if o == nil || IsNil(o.Interactions) {
		var ret []InteractionWithDetails
		return ret
	}
	return o.Interactions
}

// GetInteractionsOk returns a tuple with the Interactions field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionsGet200Response) GetInteractionsOk() ([]InteractionWithDetails, bool) {
	if o == nil || IsNil(o.Interactions) {
		return nil, false
	}
	return o.Interactions, true
}

// HasInteractions returns a boolean if a field has been set.
func (o *InteractionsGet200Response) HasInteractions() bool {
	if o != nil && !IsNil(o.Interactions) {
		return true
	}

	return false
}

// SetInteractions gets a reference to the given []InteractionWithDetails and assigns it to the Interactions field.
func (o *InteractionsGet200Response) SetInteractions(v []InteractionWithDetails) {
	o.Interactions = v
}

// GetTotalCount returns the TotalCount field value if set, zero value otherwise.
func (o *InteractionsGet200Response) GetTotalCount() int32 {
	if o == nil || IsNil(o.TotalCount) {
		var ret int32
		return ret
	}
	return *o.TotalCount
}

// GetTotalCountOk returns a tuple with the TotalCount field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionsGet200Response) GetTotalCountOk() (*int32, bool) {
	if o == nil || IsNil(o.TotalCount) {
		return nil, false
	}
	return o.TotalCount, true
}

// HasTotalCount returns a boolean if a field has been set.
func (o *InteractionsGet200Response) HasTotalCount() bool {
	if o != nil && !IsNil(o.TotalCount) {
		return true
	}

	return false
}

// SetTotalCount gets a reference to the given int32 and assigns it to the TotalCount field.
func (o *InteractionsGet200Response) SetTotalCount(v int32) {
	o.TotalCount = &v
}

// GetHasMore returns the HasMore field value if set, zero value otherwise.
func (o *InteractionsGet200Response) GetHasMore() bool {
	if o == nil || IsNil(o.HasMore) {
		var ret bool
		return ret
	}
	return *o.HasMore
}

// GetHasMoreOk returns a tuple with the HasMore field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionsGet200Response) GetHasMoreOk() (*bool, bool) {
	if o == nil || IsNil(o.HasMore) {
		return nil, false
	}
	return o.HasMore, true
}

// HasHasMore returns a boolean if a field has been set.
func (o *InteractionsGet200Response) HasHasMore() bool {
	if o != nil && !IsNil(o.HasMore) {
		return true
	}

	return false
}

// SetHasMore gets a reference to the given bool and assigns it to the HasMore field.
func (o *InteractionsGet200Response) SetHasMore(v bool) {
	o.HasMore = &v
}

func (o InteractionsGet200Response) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o InteractionsGet200Response) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Interactions) {
		toSerialize["interactions"] = o.Interactions
	}
	if !IsNil(o.TotalCount) {
		toSerialize["total_count"] = o.TotalCount
	}
	if !IsNil(o.HasMore) {
		toSerialize["has_more"] = o.HasMore
	}
	return toSerialize, nil
}

type NullableInteractionsGet200Response struct {
	value *InteractionsGet200Response
	isSet bool
}

func (v NullableInteractionsGet200Response) Get() *InteractionsGet200Response {
	return v.value
}

func (v *NullableInteractionsGet200Response) Set(val *InteractionsGet200Response) {
	v.value = val
	v.isSet = true
}

func (v NullableInteractionsGet200Response) IsSet() bool {
	return v.isSet
}

func (v *NullableInteractionsGet200Response) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableInteractionsGet200Response(val *InteractionsGet200Response) *NullableInteractionsGet200Response {
	return &NullableInteractionsGet200Response{value: val, isSet: true}
}

func (v NullableInteractionsGet200Response) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableInteractionsGet200Response) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


