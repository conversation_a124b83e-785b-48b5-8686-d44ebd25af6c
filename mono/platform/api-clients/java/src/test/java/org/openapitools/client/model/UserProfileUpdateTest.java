/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.net.URI;
import java.util.Arrays;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

/**
 * Model tests for UserProfileUpdate
 */
public class UserProfileUpdateTest {
    private final UserProfileUpdate model = new UserProfileUpdate();

    /**
     * Model tests for UserProfileUpdate
     */
    @Test
    public void testUserProfileUpdate() {
        // TODO: test UserProfileUpdate
    }

    /**
     * Test the property 'fullName'
     */
    @Test
    public void fullNameTest() {
        // TODO: test fullName
    }

    /**
     * Test the property 'avatarUrl'
     */
    @Test
    public void avatarUrlTest() {
        // TODO: test avatarUrl
    }

    /**
     * Test the property 'timezone'
     */
    @Test
    public void timezoneTest() {
        // TODO: test timezone
    }

}
