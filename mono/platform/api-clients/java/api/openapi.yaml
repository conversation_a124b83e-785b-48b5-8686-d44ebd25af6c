openapi: 3.0.3
info:
  contact:
    email: <EMAIL>
    name: CRM API Support
  description: Complete API specification for the CRM system
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  title: CRM API
  version: 1.0.0
servers:
- description: Production server
  url: https://api.crm.example.com/v1
- description: Staging server
  url: https://staging-api.crm.example.com/v1
- description: Development server
  url: http://localhost:8003/api/v1
security:
- bearerAuth: []
tags:
- description: User authentication and session management
  name: Authentication
- description: Company management operations
  name: Companies
- description: Contact management operations
  name: Contacts
- description: Deal pipeline management
  name: Deals
- description: Communication tracking
  name: Interactions
- description: AI-powered document processing
  name: Arli
- description: User profile and settings
  name: User Management
paths:
  /auth/session:
    get:
      description: Retrieve the current user session
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Session'
          description: Session information
        "401":
          description: Not authenticated
      security: []
      summary: Get current session
      tags:
      - Authentication
      x-accepts:
      - application/json
  /auth/signin:
    post:
      description: Authenticate user with email and password
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SignInRequest'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
          description: Authentication successful
        "401":
          description: Invalid credentials
      security: []
      summary: Sign in with email/password
      tags:
      - Authentication
      x-content-type: application/json
      x-accepts:
      - application/json
  /auth/signin/oauth:
    post:
      description: Authenticate user with OAuth provider (Google)
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OAuthSignInRequest'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OAuthRedirectResponse'
          description: OAuth redirect URL
      security: []
      summary: Sign in with OAuth
      tags:
      - Authentication
      x-content-type: application/json
      x-accepts:
      - application/json
  /auth/signout:
    post:
      description: End the current user session
      responses:
        "200":
          description: Successfully signed out
        "401":
          description: Not authenticated
      summary: Sign out
      tags:
      - Authentication
      x-accepts:
      - application/json
  /auth/user:
    get:
      description: Retrieve current authenticated user information
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: User information
        "401":
          description: Not authenticated
      summary: Get current user
      tags:
      - Authentication
      x-accepts:
      - application/json
  /companies:
    get:
      description: Retrieve a list of all active companies
      parameters:
      - description: Filter by company status
        explode: true
        in: query
        name: status_id
        required: false
        schema:
          format: uuid
          type: string
        style: form
      - description: Include soft-deleted companies
        explode: true
        in: query
        name: include_deleted
        required: false
        schema:
          default: false
          type: boolean
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Company'
                type: array
          description: List of companies
      summary: List companies
      tags:
      - Companies
      x-accepts:
      - application/json
    post:
      description: Create a new company
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyCreate'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Company'
          description: Company created successfully
        "400":
          description: Invalid input
      summary: Create company
      tags:
      - Companies
      x-content-type: application/json
      x-accepts:
      - application/json
  /companies/{id}:
    delete:
      description: Soft delete a company (sets is_deleted to true)
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      responses:
        "204":
          description: Company deleted successfully
        "404":
          description: Company not found
      summary: Delete company
      tags:
      - Companies
      x-accepts:
      - application/json
    get:
      description: Retrieve a specific company by ID
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanyDetails'
          description: Company details
        "404":
          description: Company not found
      summary: Get company
      tags:
      - Companies
      x-accepts:
      - application/json
    put:
      description: Update an existing company
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyUpdate'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Company'
          description: Company updated successfully
        "404":
          description: Company not found
      summary: Update company
      tags:
      - Companies
      x-content-type: application/json
      x-accepts:
      - application/json
  /companies/{id}/status:
    put:
      description: Update the status of a company
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanyStatusUpdateRequest'
        required: true
      responses:
        "200":
          description: Company status updated successfully
        "404":
          description: Company not found
      summary: Update company status
      tags:
      - Companies
      x-content-type: application/json
      x-accepts:
      - application/json
  /company-statuses:
    get:
      description: Retrieve all company statuses ordered by pipeline order
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/CompanyStatus'
                type: array
          description: List of company statuses
      summary: List company statuses
      tags:
      - Companies
      x-accepts:
      - application/json
  /contacts:
    get:
      description: Retrieve a list of all active contacts with their associated companies
      parameters:
      - description: Filter by company ID
        explode: true
        in: query
        name: company_id
        required: false
        schema:
          format: uuid
          type: string
        style: form
      - description: Only return contacts without a company
        explode: true
        in: query
        name: unlinked
        required: false
        schema:
          type: boolean
        style: form
      - description: Include soft-deleted contacts
        explode: true
        in: query
        name: include_deleted
        required: false
        schema:
          default: false
          type: boolean
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/ContactWithCompany'
                type: array
          description: List of contacts
      summary: List contacts
      tags:
      - Contacts
      x-accepts:
      - application/json
    post:
      description: Create a new contact
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactCreate'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Contact'
          description: Contact created successfully
        "400":
          description: Invalid input
      summary: Create contact
      tags:
      - Contacts
      x-content-type: application/json
      x-accepts:
      - application/json
  /contacts/{id}:
    delete:
      description: Soft delete a contact (sets is_deleted to true)
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      responses:
        "204":
          description: Contact deleted successfully
        "404":
          description: Contact not found
      summary: Delete contact
      tags:
      - Contacts
      x-accepts:
      - application/json
    get:
      description: Retrieve a specific contact by ID
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContactDetails'
          description: Contact details
        "404":
          description: Contact not found
      summary: Get contact
      tags:
      - Contacts
      x-accepts:
      - application/json
    put:
      description: Update an existing contact
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactUpdate'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Contact'
          description: Contact updated successfully
        "404":
          description: Contact not found
      summary: Update contact
      tags:
      - Contacts
      x-content-type: application/json
      x-accepts:
      - application/json
  /contacts/{id}/company:
    delete:
      description: Remove the association between a contact and company
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      responses:
        "204":
          description: Contact unlinked from company successfully
        "404":
          description: Contact not found
      summary: Unlink contact from company
      tags:
      - Contacts
      x-accepts:
      - application/json
    put:
      description: Associate a contact with a company
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactCompanyUpdateRequest'
        required: true
      responses:
        "200":
          description: Contact linked to company successfully
        "404":
          description: Contact or company not found
      summary: Link contact to company
      tags:
      - Contacts
      x-content-type: application/json
      x-accepts:
      - application/json
  /deals:
    get:
      description: Retrieve a list of all deals with company and stage information
      parameters:
      - description: Filter by deal stage ID
        explode: true
        in: query
        name: stage_id
        required: false
        schema:
          format: uuid
          type: string
        style: form
      - description: Filter by company ID
        explode: true
        in: query
        name: company_id
        required: false
        schema:
          format: uuid
          type: string
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DealWithDetails'
                type: array
          description: List of deals
      summary: List deals
      tags:
      - Deals
      x-accepts:
      - application/json
    post:
      description: Create a new deal
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DealCreate'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Deal'
          description: Deal created successfully
        "400":
          description: Invalid input
      summary: Create deal
      tags:
      - Deals
      x-content-type: application/json
      x-accepts:
      - application/json
  /deals/{id}:
    delete:
      description: Delete a deal
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      responses:
        "204":
          description: Deal deleted successfully
        "404":
          description: Deal not found
      summary: Delete deal
      tags:
      - Deals
      x-accepts:
      - application/json
    get:
      description: Retrieve a specific deal by ID
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DealDetails'
          description: Deal details
        "404":
          description: Deal not found
      summary: Get deal
      tags:
      - Deals
      x-accepts:
      - application/json
    put:
      description: Update an existing deal
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DealUpdate'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Deal'
          description: Deal updated successfully
        "404":
          description: Deal not found
      summary: Update deal
      tags:
      - Deals
      x-content-type: application/json
      x-accepts:
      - application/json
  /deal-stages:
    get:
      description: Retrieve all deal stages ordered by pipeline order
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/DealStage'
                type: array
          description: List of deal stages
      summary: List deal stages
      tags:
      - Deals
      x-accepts:
      - application/json
  /users/profile:
    get:
      description: Retrieve the current user's profile information
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
          description: User profile
        "401":
          description: Not authenticated
      summary: Get user profile
      tags:
      - User Management
      x-accepts:
      - application/json
    put:
      description: Update the current user's profile information
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserProfileUpdate'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
          description: Profile updated successfully
        "400":
          description: Invalid input
      summary: Update user profile
      tags:
      - User Management
      x-content-type: application/json
      x-accepts:
      - application/json
  /interactions:
    get:
      description: Retrieve a list of all interactions
      parameters:
      - description: Filter by company ID
        explode: true
        in: query
        name: company_id
        required: false
        schema:
          format: uuid
          type: string
        style: form
      - description: Filter by contact ID
        explode: true
        in: query
        name: contact_id
        required: false
        schema:
          format: uuid
          type: string
        style: form
      - description: Filter by interaction type
        explode: true
        in: query
        name: interaction_type
        required: false
        schema:
          type: string
        style: form
      - description: Filter interactions from this date
        explode: true
        in: query
        name: from_date
        required: false
        schema:
          format: date-time
          type: string
        style: form
      - description: Filter interactions to this date
        explode: true
        in: query
        name: to_date
        required: false
        schema:
          format: date-time
          type: string
        style: form
      - description: Number of interactions to return
        explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          maximum: 100
          minimum: 1
          type: integer
        style: form
      - description: Number of interactions to skip
        explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          minimum: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/_interactions_get_200_response'
          description: List of interactions
      summary: List interactions
      tags:
      - Interactions
      x-accepts:
      - application/json
    post:
      description: Create a new interaction record
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InteractionCreate'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Interaction'
          description: Interaction created successfully
        "400":
          description: Invalid input
      summary: Create interaction
      tags:
      - Interactions
      x-content-type: application/json
      x-accepts:
      - application/json
  /interactions/{id}:
    delete:
      description: Delete an interaction record
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      responses:
        "204":
          description: Interaction deleted successfully
        "404":
          description: Interaction not found
      summary: Delete interaction
      tags:
      - Interactions
      x-accepts:
      - application/json
    get:
      description: Retrieve a specific interaction by ID
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InteractionDetails'
          description: Interaction details
        "404":
          description: Interaction not found
      summary: Get interaction
      tags:
      - Interactions
      x-accepts:
      - application/json
    put:
      description: Update an existing interaction
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InteractionUpdate'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Interaction'
          description: Interaction updated successfully
        "404":
          description: Interaction not found
      summary: Update interaction
      tags:
      - Interactions
      x-content-type: application/json
      x-accepts:
      - application/json
  /arli/documents:
    get:
      description: Retrieve a list of processed documents
      parameters:
      - description: Filter by tags
        explode: true
        in: query
        name: filter_tags
        required: false
        schema:
          items:
            type: string
          type: array
        style: form
      - description: Filter by metadata properties
        explode: true
        in: query
        name: metadata_filter
        required: false
        schema:
          type: object
        style: form
      - explode: true
        in: query
        name: limit
        required: false
        schema:
          default: 20
          maximum: 100
          minimum: 1
          type: integer
        style: form
      - explode: true
        in: query
        name: offset
        required: false
        schema:
          default: 0
          minimum: 0
          type: integer
        style: form
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/_arli_documents_get_200_response'
          description: List of documents
      summary: List documents
      tags:
      - Arli
      x-accepts:
      - application/json
    post:
      description: Create a new document for processing
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentCreate'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: Document created successfully
        "400":
          description: Invalid input
      summary: Create document
      tags:
      - Arli
      x-content-type: application/json
      x-accepts:
      - application/json
  /arli/documents/{id}:
    delete:
      description: Delete a document
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      responses:
        "204":
          description: Document deleted successfully
        "404":
          description: Document not found
      summary: Delete document
      tags:
      - Arli
      x-accepts:
      - application/json
    get:
      description: Retrieve a specific document by ID
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentDetails'
          description: Document details
        "404":
          description: Document not found
      summary: Get document
      tags:
      - Arli
      x-accepts:
      - application/json
    put:
      description: Update an existing document
      parameters:
      - explode: false
        in: path
        name: id
        required: true
        schema:
          format: uuid
          type: string
        style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentUpdate'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: Document updated successfully
        "404":
          description: Document not found
      summary: Update document
      tags:
      - Arli
      x-content-type: application/json
      x-accepts:
      - application/json
  /arli/documents/vectorize:
    post:
      description: Process a document through the AI vectorization pipeline
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/_arli_documents_vectorize_post_request'
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/_arli_documents_vectorize_post_200_response'
          description: Document vectorized successfully
        "400":
          description: Invalid input
        "500":
          description: Vectorization failed
      summary: Vectorize document
      tags:
      - Arli
      x-content-type: application/json
      x-accepts:
      - application/json
  /arli/filter-tags:
    get:
      description: Retrieve all available filter tags
      responses:
        "200":
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/FilterTag'
                type: array
          description: List of filter tags
      summary: List filter tags
      tags:
      - Arli
      x-accepts:
      - application/json
    post:
      description: Create a new filter tag
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FilterTagCreate'
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FilterTag'
          description: Filter tag created successfully
      summary: Create filter tag
      tags:
      - Arli
      x-content-type: application/json
      x-accepts:
      - application/json
components:
  schemas:
    Session:
      example:
        access_token: access_token
        refresh_token: refresh_token
        token_type: token_type
        expires_in: 0
        user:
          email_confirmed_at: 2000-01-23T04:56:07.000+00:00
          updated_at: 2000-01-23T04:56:07.000+00:00
          created_at: 2000-01-23T04:56:07.000+00:00
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          email: email
      properties:
        access_token:
          type: string
        refresh_token:
          type: string
        expires_in:
          type: integer
        token_type:
          type: string
        user:
          $ref: '#/components/schemas/User'
      type: object
    AuthResponse:
      example:
        access_token: access_token
        refresh_token: refresh_token
        token_type: token_type
        expires_in: 0
        user:
          email_confirmed_at: 2000-01-23T04:56:07.000+00:00
          updated_at: 2000-01-23T04:56:07.000+00:00
          created_at: 2000-01-23T04:56:07.000+00:00
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          email: email
      properties:
        access_token:
          type: string
        refresh_token:
          type: string
        expires_in:
          type: integer
        token_type:
          type: string
        user:
          $ref: '#/components/schemas/User'
      type: object
    User:
      example:
        email_confirmed_at: 2000-01-23T04:56:07.000+00:00
        updated_at: 2000-01-23T04:56:07.000+00:00
        created_at: 2000-01-23T04:56:07.000+00:00
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        email: email
      properties:
        id:
          format: uuid
          type: string
        email:
          format: email
          type: string
        email_confirmed_at:
          format: date-time
          type: string
        created_at:
          format: date-time
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    UserProfile:
      example:
        full_name: full_name
        avatar_url: https://openapi-generator.tech
        updated_at: 2000-01-23T04:56:07.000+00:00
        timezone: timezone
        created_at: 2000-01-23T04:56:07.000+00:00
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
      properties:
        id:
          format: uuid
          type: string
        full_name:
          type: string
        avatar_url:
          format: uri
          type: string
        timezone:
          type: string
        created_at:
          format: date-time
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    UserProfileUpdate:
      example:
        full_name: full_name
        avatar_url: https://openapi-generator.tech
        timezone: timezone
      properties:
        full_name:
          type: string
        avatar_url:
          format: uri
          type: string
        timezone:
          type: string
      type: object
    Company:
      example:
        website: https://openapi-generator.tech
        address: address
        notes: notes
        is_deleted: false
        updated_at: 2000-01-23T04:56:07.000+00:00
        phone: phone
        company_status_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        name: name
        created_at: 2000-01-23T04:56:07.000+00:00
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
      properties:
        id:
          format: uuid
          type: string
        name:
          type: string
        website:
          format: uri
          type: string
        phone:
          type: string
        address:
          type: string
        notes:
          type: string
        company_status_id:
          format: uuid
          type: string
        is_deleted:
          default: false
          type: boolean
        created_at:
          format: date-time
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    CompanyDetails:
      allOf:
      - $ref: '#/components/schemas/Company'
      - properties:
          contacts:
            items:
              $ref: '#/components/schemas/Contact'
            type: array
        type: object
      example:
        website: https://openapi-generator.tech
        address: address
        notes: notes
        is_deleted: false
        updated_at: 2000-01-23T04:56:07.000+00:00
        phone: phone
        company_status_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        name: name
        created_at: 2000-01-23T04:56:07.000+00:00
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        contacts:
        - is_deleted: false
          company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          updated_at: 2000-01-23T04:56:07.000+00:00
          phone: phone
          last_name: last_name
          created_at: 2000-01-23T04:56:07.000+00:00
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          first_name: first_name
          job_title: job_title
          created_by: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          email: email
        - is_deleted: false
          company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          updated_at: 2000-01-23T04:56:07.000+00:00
          phone: phone
          last_name: last_name
          created_at: 2000-01-23T04:56:07.000+00:00
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          first_name: first_name
          job_title: job_title
          created_by: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          email: email
    CompanyCreate:
      example:
        website: https://openapi-generator.tech
        address: address
        notes: notes
        phone: phone
        company_status_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        name: name
      properties:
        name:
          minLength: 1
          type: string
        website:
          format: uri
          type: string
        phone:
          type: string
        address:
          type: string
        notes:
          type: string
        company_status_id:
          format: uuid
          type: string
      required:
      - name
      type: object
    CompanyUpdate:
      example:
        website: https://openapi-generator.tech
        address: address
        notes: notes
        phone: phone
        company_status_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        name: name
      properties:
        name:
          minLength: 1
          type: string
        website:
          format: uri
          type: string
        phone:
          type: string
        address:
          type: string
        notes:
          type: string
        company_status_id:
          format: uuid
          type: string
      type: object
    CompanyStatus:
      example:
        name: name
        created_at: 2000-01-23T04:56:07.000+00:00
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        pipeline_order: 0
      properties:
        id:
          format: uuid
          type: string
        name:
          type: string
        pipeline_order:
          type: integer
        created_at:
          format: date-time
          type: string
      type: object
    Contact:
      example:
        is_deleted: false
        company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        updated_at: 2000-01-23T04:56:07.000+00:00
        phone: phone
        last_name: last_name
        created_at: 2000-01-23T04:56:07.000+00:00
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        first_name: first_name
        job_title: job_title
        created_by: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        email: email
      properties:
        id:
          format: uuid
          type: string
        first_name:
          type: string
        last_name:
          type: string
        email:
          format: email
          type: string
        phone:
          type: string
        job_title:
          type: string
        company_id:
          format: uuid
          nullable: true
          type: string
        created_by:
          format: uuid
          type: string
        is_deleted:
          default: false
          type: boolean
        created_at:
          format: date-time
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    ContactWithCompany:
      allOf:
      - $ref: '#/components/schemas/Contact'
      - properties:
          company:
            $ref: '#/components/schemas/CompanyInfo'
        type: object
      example:
        is_deleted: false
        company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        updated_at: 2000-01-23T04:56:07.000+00:00
        phone: phone
        last_name: last_name
        created_at: 2000-01-23T04:56:07.000+00:00
        company:
          is_deleted: true
          name: name
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        first_name: first_name
        job_title: job_title
        created_by: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        email: email
    ContactDetails:
      allOf:
      - $ref: '#/components/schemas/Contact'
      - properties:
          company:
            $ref: '#/components/schemas/CompanyBasicInfo'
        type: object
      example:
        is_deleted: false
        company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        updated_at: 2000-01-23T04:56:07.000+00:00
        phone: phone
        last_name: last_name
        created_at: 2000-01-23T04:56:07.000+00:00
        company:
          name: name
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        first_name: first_name
        job_title: job_title
        created_by: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        email: email
    CompanyInfo:
      example:
        is_deleted: true
        name: name
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
      nullable: true
      properties:
        id:
          format: uuid
          type: string
        name:
          type: string
        is_deleted:
          type: boolean
      type: object
    CompanyBasicInfo:
      example:
        name: name
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
      nullable: true
      properties:
        id:
          format: uuid
          type: string
        name:
          type: string
      type: object
    ContactCreate:
      example:
        company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        phone: phone
        last_name: last_name
        first_name: first_name
        job_title: job_title
        email: email
      properties:
        first_name:
          minLength: 1
          type: string
        last_name:
          minLength: 1
          type: string
        email:
          format: email
          type: string
        phone:
          type: string
        job_title:
          type: string
        company_id:
          format: uuid
          type: string
      required:
      - first_name
      - last_name
      type: object
    ContactUpdate:
      example:
        company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        phone: phone
        last_name: last_name
        first_name: first_name
        job_title: job_title
        email: email
      properties:
        first_name:
          minLength: 1
          type: string
        last_name:
          minLength: 1
          type: string
        email:
          format: email
          type: string
        phone:
          type: string
        job_title:
          type: string
        company_id:
          format: uuid
          nullable: true
          type: string
      type: object
    Deal:
      example:
        expected_close_date: 2000-01-23
        company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        updated_at: 2000-01-23T04:56:07.000+00:00
        deal_stage_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        description: description
        created_at: 2000-01-23T04:56:07.000+00:00
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        title: title
        estimated_value: 0.8008281904610115
        created_by: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
      properties:
        id:
          format: uuid
          type: string
        title:
          type: string
        description:
          type: string
        estimated_value:
          format: double
          type: number
        company_id:
          format: uuid
          type: string
        deal_stage_id:
          format: uuid
          type: string
        expected_close_date:
          format: date
          type: string
        created_by:
          format: uuid
          type: string
        created_at:
          format: date-time
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    DealWithDetails:
      allOf:
      - $ref: '#/components/schemas/Deal'
      - properties:
          company:
            $ref: '#/components/schemas/DealCompanyInfo'
          deal_stage:
            $ref: '#/components/schemas/DealStageInfo'
        type: object
      example:
        expected_close_date: 2000-01-23
        deal_stage:
          name: name
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          pipeline_order: 6
        company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        updated_at: 2000-01-23T04:56:07.000+00:00
        deal_stage_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        description: description
        created_at: 2000-01-23T04:56:07.000+00:00
        company:
          name: name
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        title: title
        estimated_value: 0.8008281904610115
        created_by: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
    DealCompanyInfo:
      example:
        name: name
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
      properties:
        id:
          format: uuid
          type: string
        name:
          type: string
      type: object
    DealStageInfo:
      example:
        name: name
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        pipeline_order: 6
      properties:
        id:
          format: uuid
          type: string
        name:
          type: string
        pipeline_order:
          type: integer
      type: object
    DealDetails:
      allOf:
      - $ref: '#/components/schemas/Deal'
      - properties:
          company:
            $ref: '#/components/schemas/Company'
          deal_stage:
            $ref: '#/components/schemas/DealStage'
        type: object
      example:
        expected_close_date: 2000-01-23
        deal_stage:
          is_closed_lost: false
          name: name
          created_at: 2000-01-23T04:56:07.000+00:00
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          pipeline_order: 6
          is_closed_won: false
        company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        updated_at: 2000-01-23T04:56:07.000+00:00
        deal_stage_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        description: description
        created_at: 2000-01-23T04:56:07.000+00:00
        company:
          website: https://openapi-generator.tech
          address: address
          notes: notes
          is_deleted: false
          updated_at: 2000-01-23T04:56:07.000+00:00
          phone: phone
          company_status_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          name: name
          created_at: 2000-01-23T04:56:07.000+00:00
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        title: title
        estimated_value: 0.8008281904610115
        created_by: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
    DealCreate:
      example:
        expected_close_date: 2000-01-23
        company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        deal_stage_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        description: description
        title: title
        estimated_value: 0.08008281904610115
      properties:
        title:
          minLength: 1
          type: string
        description:
          type: string
        estimated_value:
          format: double
          minimum: 0
          type: number
        company_id:
          format: uuid
          type: string
        deal_stage_id:
          format: uuid
          type: string
        expected_close_date:
          format: date
          type: string
      required:
      - company_id
      - deal_stage_id
      - title
      type: object
    DealUpdate:
      example:
        expected_close_date: 2000-01-23
        company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        deal_stage_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        description: description
        title: title
        estimated_value: 0.08008281904610115
      properties:
        title:
          minLength: 1
          type: string
        description:
          type: string
        estimated_value:
          format: double
          minimum: 0
          type: number
        company_id:
          format: uuid
          type: string
        deal_stage_id:
          format: uuid
          type: string
        expected_close_date:
          format: date
          type: string
      type: object
    DealStage:
      example:
        is_closed_lost: false
        name: name
        created_at: 2000-01-23T04:56:07.000+00:00
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        pipeline_order: 6
        is_closed_won: false
      properties:
        id:
          format: uuid
          type: string
        name:
          type: string
        pipeline_order:
          type: integer
        is_closed_won:
          default: false
          type: boolean
        is_closed_lost:
          default: false
          type: boolean
        created_at:
          format: date-time
          type: string
      type: object
    Error:
      properties:
        code:
          type: string
        message:
          type: string
        details:
          type: object
      required:
      - code
      - message
      type: object
    ValidationError:
      properties:
        code:
          example: validation_error
          type: string
        message:
          example: Invalid input data
          type: string
        details:
          $ref: '#/components/schemas/ValidationErrorDetails'
      required:
      - code
      - message
      type: object
    Interaction:
      example:
        notes: notes
        company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        updated_at: 2000-01-23T04:56:07.000+00:00
        interaction_type: email
        created_at: 2000-01-23T04:56:07.000+00:00
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        contact_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        interaction_datetime: 2000-01-23T04:56:07.000+00:00
        created_by: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
      properties:
        id:
          format: uuid
          type: string
        company_id:
          format: uuid
          nullable: true
          type: string
        contact_id:
          format: uuid
          nullable: true
          type: string
        interaction_type:
          enum:
          - email
          - phone
          - meeting
          - demo
          - proposal
          - follow-up
          - other
          type: string
        notes:
          type: string
        interaction_datetime:
          format: date-time
          type: string
        created_by:
          format: uuid
          type: string
        created_at:
          format: date-time
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    InteractionWithDetails:
      allOf:
      - $ref: '#/components/schemas/Interaction'
      - properties:
          company:
            $ref: '#/components/schemas/InteractionWithDetails_allOf_company'
          contact:
            $ref: '#/components/schemas/InteractionWithDetails_allOf_contact'
        type: object
      example:
        notes: notes
        company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        updated_at: 2000-01-23T04:56:07.000+00:00
        contact:
          last_name: last_name
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          first_name: first_name
          email: email
        interaction_type: email
        created_at: 2000-01-23T04:56:07.000+00:00
        company:
          name: name
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        contact_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        interaction_datetime: 2000-01-23T04:56:07.000+00:00
        created_by: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
    InteractionDetails:
      allOf:
      - $ref: '#/components/schemas/Interaction'
      - properties:
          company:
            $ref: '#/components/schemas/InteractionDetails_allOf_company'
          contact:
            $ref: '#/components/schemas/InteractionDetails_allOf_contact'
        type: object
      example:
        notes: notes
        company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        updated_at: 2000-01-23T04:56:07.000+00:00
        contact:
          phone: phone
          last_name: last_name
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          first_name: first_name
          job_title: job_title
          email: email
        interaction_type: email
        created_at: 2000-01-23T04:56:07.000+00:00
        company:
          website: website
          phone: phone
          name: name
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        contact_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        interaction_datetime: 2000-01-23T04:56:07.000+00:00
        created_by: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
    InteractionCreate:
      example:
        notes: notes
        company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        interaction_type: email
        contact_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        interaction_datetime: 2000-01-23T04:56:07.000+00:00
      properties:
        company_id:
          format: uuid
          type: string
        contact_id:
          format: uuid
          type: string
        interaction_type:
          enum:
          - email
          - phone
          - meeting
          - demo
          - proposal
          - follow-up
          - other
          type: string
        notes:
          minLength: 1
          type: string
        interaction_datetime:
          format: date-time
          type: string
      required:
      - interaction_datetime
      - interaction_type
      - notes
      type: object
    InteractionUpdate:
      example:
        notes: notes
        company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        interaction_type: email
        contact_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        interaction_datetime: 2000-01-23T04:56:07.000+00:00
      properties:
        interaction_type:
          enum:
          - email
          - phone
          - meeting
          - demo
          - proposal
          - follow-up
          - other
          type: string
        notes:
          minLength: 1
          type: string
        interaction_datetime:
          format: date-time
          type: string
        company_id:
          format: uuid
          type: string
        contact_id:
          format: uuid
          type: string
      type: object
    Document:
      example:
        metadata: "{}"
        filter_tags:
        - filter_tags
        - filter_tags
        updated_at: 2000-01-23T04:56:07.000+00:00
        created_at: 2000-01-23T04:56:07.000+00:00
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        source_id: source_id
        content: content
      properties:
        id:
          format: uuid
          type: string
        content:
          type: string
        metadata:
          type: object
        filter_tags:
          items:
            type: string
          type: array
        source_id:
          type: string
        created_at:
          format: date-time
          type: string
        updated_at:
          format: date-time
          type: string
      type: object
    DocumentDetails:
      allOf:
      - $ref: '#/components/schemas/Document'
      - properties:
          vector_embeddings:
            items:
              type: number
            type: array
          processing_status:
            enum:
            - pending
            - processing
            - completed
            - failed
            type: string
        type: object
      example:
        vector_embeddings:
        - 0.8008281904610115
        - 0.8008281904610115
        metadata: "{}"
        filter_tags:
        - filter_tags
        - filter_tags
        updated_at: 2000-01-23T04:56:07.000+00:00
        created_at: 2000-01-23T04:56:07.000+00:00
        processing_status: pending
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        source_id: source_id
        content: content
    DocumentCreate:
      example:
        metadata: "{}"
        filter_tags:
        - filter_tags
        - filter_tags
        source_id: source_id
        content: content
      properties:
        content:
          minLength: 1
          type: string
        metadata:
          type: object
        filter_tags:
          items:
            type: string
          type: array
        source_id:
          type: string
      required:
      - content
      type: object
    DocumentUpdate:
      example:
        metadata: "{}"
        filter_tags:
        - filter_tags
        - filter_tags
        source_id: source_id
        content: content
      properties:
        content:
          minLength: 1
          type: string
        metadata:
          type: object
        filter_tags:
          items:
            type: string
          type: array
        source_id:
          type: string
      type: object
    FilterTag:
      example:
        color: color
        name: name
        created_at: 2000-01-23T04:56:07.000+00:00
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
      properties:
        id:
          format: uuid
          type: string
        name:
          type: string
        color:
          pattern: "^#[0-9a-fA-F]{6}$"
          type: string
        created_at:
          format: date-time
          type: string
      type: object
    FilterTagCreate:
      example:
        color: '#3b82f6'
        name: name
      properties:
        name:
          minLength: 1
          type: string
        color:
          default: '#3b82f6'
          pattern: "^#[0-9a-fA-F]{6}$"
          type: string
      required:
      - name
      type: object
    SignInRequest:
      example:
        password: password
        email: email
      properties:
        email:
          format: email
          type: string
        password:
          minLength: 6
          type: string
      required:
      - email
      - password
      title: SignInRequest
      type: object
    OAuthSignInRequest:
      example:
        provider: google
        redirectTo: https://openapi-generator.tech
      properties:
        provider:
          enum:
          - google
          type: string
        redirectTo:
          format: uri
          type: string
      required:
      - provider
      title: OAuthSignInRequest
      type: object
    OAuthRedirectResponse:
      example:
        url: https://openapi-generator.tech
      properties:
        url:
          format: uri
          type: string
      title: OAuthRedirectResponse
      type: object
    CompanyStatusUpdateRequest:
      example:
        company_status_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
      properties:
        company_status_id:
          format: uuid
          type: string
      required:
      - company_status_id
      title: CompanyStatusUpdateRequest
      type: object
    ContactCompanyUpdateRequest:
      example:
        company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
      properties:
        company_id:
          format: uuid
          type: string
      required:
      - company_id
      title: ContactCompanyUpdateRequest
      type: object
    _interactions_get_200_response:
      example:
        total_count: 0
        has_more: true
        interactions:
        - notes: notes
          company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          updated_at: 2000-01-23T04:56:07.000+00:00
          contact:
            last_name: last_name
            id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
            first_name: first_name
            email: email
          interaction_type: email
          created_at: 2000-01-23T04:56:07.000+00:00
          company:
            name: name
            id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          contact_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          interaction_datetime: 2000-01-23T04:56:07.000+00:00
          created_by: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        - notes: notes
          company_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          updated_at: 2000-01-23T04:56:07.000+00:00
          contact:
            last_name: last_name
            id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
            first_name: first_name
            email: email
          interaction_type: email
          created_at: 2000-01-23T04:56:07.000+00:00
          company:
            name: name
            id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          contact_id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          interaction_datetime: 2000-01-23T04:56:07.000+00:00
          created_by: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
      properties:
        interactions:
          items:
            $ref: '#/components/schemas/InteractionWithDetails'
          type: array
        total_count:
          type: integer
        has_more:
          type: boolean
      type: object
    _arli_documents_get_200_response:
      example:
        documents:
        - metadata: "{}"
          filter_tags:
          - filter_tags
          - filter_tags
          updated_at: 2000-01-23T04:56:07.000+00:00
          created_at: 2000-01-23T04:56:07.000+00:00
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          source_id: source_id
          content: content
        - metadata: "{}"
          filter_tags:
          - filter_tags
          - filter_tags
          updated_at: 2000-01-23T04:56:07.000+00:00
          created_at: 2000-01-23T04:56:07.000+00:00
          id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
          source_id: source_id
          content: content
        total_count: 0
        has_more: true
      properties:
        documents:
          items:
            $ref: '#/components/schemas/Document'
          type: array
        total_count:
          type: integer
        has_more:
          type: boolean
      type: object
    _arli_documents_vectorize_post_request:
      properties:
        document_id:
          format: uuid
          type: string
        content:
          type: string
        metadata:
          type: object
      required:
      - content
      - document_id
      type: object
    _arli_documents_vectorize_post_200_response:
      example:
        success: true
        vector_id: vector_id
        message: message
      properties:
        success:
          type: boolean
        message:
          type: string
        vector_id:
          type: string
      type: object
    ValidationErrorDetails:
      properties:
        field_errors:
          additionalProperties:
            items:
              type: string
            type: array
          type: object
      title: ValidationErrorDetails
      type: object
    InteractionWithDetails_allOf_company:
      example:
        name: name
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
      nullable: true
      properties:
        id:
          format: uuid
          type: string
        name:
          type: string
      type: object
    InteractionWithDetails_allOf_contact:
      example:
        last_name: last_name
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        first_name: first_name
        email: email
      nullable: true
      properties:
        id:
          format: uuid
          type: string
        first_name:
          type: string
        last_name:
          type: string
        email:
          type: string
      type: object
    InteractionDetails_allOf_company:
      example:
        website: website
        phone: phone
        name: name
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
      nullable: true
      properties:
        id:
          format: uuid
          type: string
        name:
          type: string
        website:
          type: string
        phone:
          type: string
      type: object
    InteractionDetails_allOf_contact:
      example:
        phone: phone
        last_name: last_name
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        first_name: first_name
        job_title: job_title
        email: email
      nullable: true
      properties:
        id:
          format: uuid
          type: string
        first_name:
          type: string
        last_name:
          type: string
        email:
          type: string
        phone:
          type: string
        job_title:
          type: string
      type: object
  securitySchemes:
    bearerAuth:
      bearerFormat: JWT
      scheme: bearer
      type: http

