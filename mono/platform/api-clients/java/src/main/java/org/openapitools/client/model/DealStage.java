/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import java.util.Objects;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.openapitools.client.JSON;

/**
 * DealStage
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-07-14T08:13:23.820009+10:00[Australia/Brisbane]", comments = "Generator version: 7.9.0")
public class DealStage {
  public static final String SERIALIZED_NAME_ID = "id";
  @SerializedName(SERIALIZED_NAME_ID)
  private UUID id;

  public static final String SERIALIZED_NAME_NAME = "name";
  @SerializedName(SERIALIZED_NAME_NAME)
  private String name;

  public static final String SERIALIZED_NAME_PIPELINE_ORDER = "pipeline_order";
  @SerializedName(SERIALIZED_NAME_PIPELINE_ORDER)
  private Integer pipelineOrder;

  public static final String SERIALIZED_NAME_IS_CLOSED_WON = "is_closed_won";
  @SerializedName(SERIALIZED_NAME_IS_CLOSED_WON)
  private Boolean isClosedWon = false;

  public static final String SERIALIZED_NAME_IS_CLOSED_LOST = "is_closed_lost";
  @SerializedName(SERIALIZED_NAME_IS_CLOSED_LOST)
  private Boolean isClosedLost = false;

  public static final String SERIALIZED_NAME_CREATED_AT = "created_at";
  @SerializedName(SERIALIZED_NAME_CREATED_AT)
  private OffsetDateTime createdAt;

  public DealStage() {
  }

  public DealStage id(UUID id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
   */
  @javax.annotation.Nullable
  public UUID getId() {
    return id;
  }

  public void setId(UUID id) {
    this.id = id;
  }


  public DealStage name(String name) {
    this.name = name;
    return this;
  }

  /**
   * Get name
   * @return name
   */
  @javax.annotation.Nullable
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }


  public DealStage pipelineOrder(Integer pipelineOrder) {
    this.pipelineOrder = pipelineOrder;
    return this;
  }

  /**
   * Get pipelineOrder
   * @return pipelineOrder
   */
  @javax.annotation.Nullable
  public Integer getPipelineOrder() {
    return pipelineOrder;
  }

  public void setPipelineOrder(Integer pipelineOrder) {
    this.pipelineOrder = pipelineOrder;
  }


  public DealStage isClosedWon(Boolean isClosedWon) {
    this.isClosedWon = isClosedWon;
    return this;
  }

  /**
   * Get isClosedWon
   * @return isClosedWon
   */
  @javax.annotation.Nullable
  public Boolean getIsClosedWon() {
    return isClosedWon;
  }

  public void setIsClosedWon(Boolean isClosedWon) {
    this.isClosedWon = isClosedWon;
  }


  public DealStage isClosedLost(Boolean isClosedLost) {
    this.isClosedLost = isClosedLost;
    return this;
  }

  /**
   * Get isClosedLost
   * @return isClosedLost
   */
  @javax.annotation.Nullable
  public Boolean getIsClosedLost() {
    return isClosedLost;
  }

  public void setIsClosedLost(Boolean isClosedLost) {
    this.isClosedLost = isClosedLost;
  }


  public DealStage createdAt(OffsetDateTime createdAt) {
    this.createdAt = createdAt;
    return this;
  }

  /**
   * Get createdAt
   * @return createdAt
   */
  @javax.annotation.Nullable
  public OffsetDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(OffsetDateTime createdAt) {
    this.createdAt = createdAt;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DealStage dealStage = (DealStage) o;
    return Objects.equals(this.id, dealStage.id) &&
        Objects.equals(this.name, dealStage.name) &&
        Objects.equals(this.pipelineOrder, dealStage.pipelineOrder) &&
        Objects.equals(this.isClosedWon, dealStage.isClosedWon) &&
        Objects.equals(this.isClosedLost, dealStage.isClosedLost) &&
        Objects.equals(this.createdAt, dealStage.createdAt);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, name, pipelineOrder, isClosedWon, isClosedLost, createdAt);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DealStage {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    pipelineOrder: ").append(toIndentedString(pipelineOrder)).append("\n");
    sb.append("    isClosedWon: ").append(toIndentedString(isClosedWon)).append("\n");
    sb.append("    isClosedLost: ").append(toIndentedString(isClosedLost)).append("\n");
    sb.append("    createdAt: ").append(toIndentedString(createdAt)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("id");
    openapiFields.add("name");
    openapiFields.add("pipeline_order");
    openapiFields.add("is_closed_won");
    openapiFields.add("is_closed_lost");
    openapiFields.add("created_at");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

  /**
   * Validates the JSON Element and throws an exception if issues found
   *
   * @param jsonElement JSON Element
   * @throws IOException if the JSON Element is invalid with respect to DealStage
   */
  public static void validateJsonElement(JsonElement jsonElement) throws IOException {
      if (jsonElement == null) {
        if (!DealStage.openapiRequiredFields.isEmpty()) { // has required fields but JSON element is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in DealStage is not found in the empty JSON string", DealStage.openapiRequiredFields.toString()));
        }
      }

      Set<Map.Entry<String, JsonElement>> entries = jsonElement.getAsJsonObject().entrySet();
      // check to see if the JSON string contains additional fields
      for (Map.Entry<String, JsonElement> entry : entries) {
        if (!DealStage.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `DealStage` properties. JSON: %s", entry.getKey(), jsonElement.toString()));
        }
      }
        JsonObject jsonObj = jsonElement.getAsJsonObject();
      if ((jsonObj.get("id") != null && !jsonObj.get("id").isJsonNull()) && !jsonObj.get("id").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `id` to be a primitive type in the JSON string but got `%s`", jsonObj.get("id").toString()));
      }
      if ((jsonObj.get("name") != null && !jsonObj.get("name").isJsonNull()) && !jsonObj.get("name").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `name` to be a primitive type in the JSON string but got `%s`", jsonObj.get("name").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!DealStage.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'DealStage' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<DealStage> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(DealStage.class));

       return (TypeAdapter<T>) new TypeAdapter<DealStage>() {
           @Override
           public void write(JsonWriter out, DealStage value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public DealStage read(JsonReader in) throws IOException {
             JsonElement jsonElement = elementAdapter.read(in);
             validateJsonElement(jsonElement);
             return thisAdapter.fromJsonTree(jsonElement);
           }

       }.nullSafe();
    }
  }

  /**
   * Create an instance of DealStage given an JSON string
   *
   * @param jsonString JSON string
   * @return An instance of DealStage
   * @throws IOException if the JSON string is invalid with respect to DealStage
   */
  public static DealStage fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, DealStage.class);
  }

  /**
   * Convert an instance of DealStage to an JSON string
   *
   * @return JSON string
   */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

