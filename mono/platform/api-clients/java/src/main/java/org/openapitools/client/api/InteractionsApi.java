/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.api;

import org.openapitools.client.ApiCallback;
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.ApiResponse;
import org.openapitools.client.Configuration;
import org.openapitools.client.Pair;
import org.openapitools.client.ProgressRequestBody;
import org.openapitools.client.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import org.openapitools.client.model.Interaction;
import org.openapitools.client.model.InteractionCreate;
import org.openapitools.client.model.InteractionDetails;
import org.openapitools.client.model.InteractionUpdate;
import org.openapitools.client.model.InteractionsGet200Response;
import java.time.OffsetDateTime;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class InteractionsApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public InteractionsApi() {
        this(Configuration.getDefaultApiClient());
    }

    public InteractionsApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for interactionsGet
     * @param companyId Filter by company ID (optional)
     * @param contactId Filter by contact ID (optional)
     * @param interactionType Filter by interaction type (optional)
     * @param fromDate Filter interactions from this date (optional)
     * @param toDate Filter interactions to this date (optional)
     * @param limit Number of interactions to return (optional, default to 20)
     * @param offset Number of interactions to skip (optional, default to 0)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of interactions </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call interactionsGetCall(UUID companyId, UUID contactId, String interactionType, OffsetDateTime fromDate, OffsetDateTime toDate, Integer limit, Integer offset, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/interactions";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (companyId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("company_id", companyId));
        }

        if (contactId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("contact_id", contactId));
        }

        if (interactionType != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("interaction_type", interactionType));
        }

        if (fromDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("from_date", fromDate));
        }

        if (toDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("to_date", toDate));
        }

        if (limit != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("limit", limit));
        }

        if (offset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("offset", offset));
        }

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call interactionsGetValidateBeforeCall(UUID companyId, UUID contactId, String interactionType, OffsetDateTime fromDate, OffsetDateTime toDate, Integer limit, Integer offset, final ApiCallback _callback) throws ApiException {
        return interactionsGetCall(companyId, contactId, interactionType, fromDate, toDate, limit, offset, _callback);

    }

    /**
     * List interactions
     * Retrieve a list of all interactions
     * @param companyId Filter by company ID (optional)
     * @param contactId Filter by contact ID (optional)
     * @param interactionType Filter by interaction type (optional)
     * @param fromDate Filter interactions from this date (optional)
     * @param toDate Filter interactions to this date (optional)
     * @param limit Number of interactions to return (optional, default to 20)
     * @param offset Number of interactions to skip (optional, default to 0)
     * @return InteractionsGet200Response
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of interactions </td><td>  -  </td></tr>
     </table>
     */
    public InteractionsGet200Response interactionsGet(UUID companyId, UUID contactId, String interactionType, OffsetDateTime fromDate, OffsetDateTime toDate, Integer limit, Integer offset) throws ApiException {
        ApiResponse<InteractionsGet200Response> localVarResp = interactionsGetWithHttpInfo(companyId, contactId, interactionType, fromDate, toDate, limit, offset);
        return localVarResp.getData();
    }

    /**
     * List interactions
     * Retrieve a list of all interactions
     * @param companyId Filter by company ID (optional)
     * @param contactId Filter by contact ID (optional)
     * @param interactionType Filter by interaction type (optional)
     * @param fromDate Filter interactions from this date (optional)
     * @param toDate Filter interactions to this date (optional)
     * @param limit Number of interactions to return (optional, default to 20)
     * @param offset Number of interactions to skip (optional, default to 0)
     * @return ApiResponse&lt;InteractionsGet200Response&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of interactions </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<InteractionsGet200Response> interactionsGetWithHttpInfo(UUID companyId, UUID contactId, String interactionType, OffsetDateTime fromDate, OffsetDateTime toDate, Integer limit, Integer offset) throws ApiException {
        okhttp3.Call localVarCall = interactionsGetValidateBeforeCall(companyId, contactId, interactionType, fromDate, toDate, limit, offset, null);
        Type localVarReturnType = new TypeToken<InteractionsGet200Response>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * List interactions (asynchronously)
     * Retrieve a list of all interactions
     * @param companyId Filter by company ID (optional)
     * @param contactId Filter by contact ID (optional)
     * @param interactionType Filter by interaction type (optional)
     * @param fromDate Filter interactions from this date (optional)
     * @param toDate Filter interactions to this date (optional)
     * @param limit Number of interactions to return (optional, default to 20)
     * @param offset Number of interactions to skip (optional, default to 0)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of interactions </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call interactionsGetAsync(UUID companyId, UUID contactId, String interactionType, OffsetDateTime fromDate, OffsetDateTime toDate, Integer limit, Integer offset, final ApiCallback<InteractionsGet200Response> _callback) throws ApiException {

        okhttp3.Call localVarCall = interactionsGetValidateBeforeCall(companyId, contactId, interactionType, fromDate, toDate, limit, offset, _callback);
        Type localVarReturnType = new TypeToken<InteractionsGet200Response>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for interactionsIdDelete
     * @param id  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Interaction deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Interaction not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call interactionsIdDeleteCall(UUID id, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/interactions/{id}"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call interactionsIdDeleteValidateBeforeCall(UUID id, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling interactionsIdDelete(Async)");
        }

        return interactionsIdDeleteCall(id, _callback);

    }

    /**
     * Delete interaction
     * Delete an interaction record
     * @param id  (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Interaction deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Interaction not found </td><td>  -  </td></tr>
     </table>
     */
    public void interactionsIdDelete(UUID id) throws ApiException {
        interactionsIdDeleteWithHttpInfo(id);
    }

    /**
     * Delete interaction
     * Delete an interaction record
     * @param id  (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Interaction deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Interaction not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> interactionsIdDeleteWithHttpInfo(UUID id) throws ApiException {
        okhttp3.Call localVarCall = interactionsIdDeleteValidateBeforeCall(id, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Delete interaction (asynchronously)
     * Delete an interaction record
     * @param id  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Interaction deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Interaction not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call interactionsIdDeleteAsync(UUID id, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = interactionsIdDeleteValidateBeforeCall(id, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for interactionsIdGet
     * @param id  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Interaction details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Interaction not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call interactionsIdGetCall(UUID id, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/interactions/{id}"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call interactionsIdGetValidateBeforeCall(UUID id, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling interactionsIdGet(Async)");
        }

        return interactionsIdGetCall(id, _callback);

    }

    /**
     * Get interaction
     * Retrieve a specific interaction by ID
     * @param id  (required)
     * @return InteractionDetails
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Interaction details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Interaction not found </td><td>  -  </td></tr>
     </table>
     */
    public InteractionDetails interactionsIdGet(UUID id) throws ApiException {
        ApiResponse<InteractionDetails> localVarResp = interactionsIdGetWithHttpInfo(id);
        return localVarResp.getData();
    }

    /**
     * Get interaction
     * Retrieve a specific interaction by ID
     * @param id  (required)
     * @return ApiResponse&lt;InteractionDetails&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Interaction details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Interaction not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<InteractionDetails> interactionsIdGetWithHttpInfo(UUID id) throws ApiException {
        okhttp3.Call localVarCall = interactionsIdGetValidateBeforeCall(id, null);
        Type localVarReturnType = new TypeToken<InteractionDetails>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get interaction (asynchronously)
     * Retrieve a specific interaction by ID
     * @param id  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Interaction details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Interaction not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call interactionsIdGetAsync(UUID id, final ApiCallback<InteractionDetails> _callback) throws ApiException {

        okhttp3.Call localVarCall = interactionsIdGetValidateBeforeCall(id, _callback);
        Type localVarReturnType = new TypeToken<InteractionDetails>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for interactionsIdPut
     * @param id  (required)
     * @param interactionUpdate  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Interaction updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Interaction not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call interactionsIdPutCall(UUID id, InteractionUpdate interactionUpdate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = interactionUpdate;

        // create path and map variables
        String localVarPath = "/interactions/{id}"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call interactionsIdPutValidateBeforeCall(UUID id, InteractionUpdate interactionUpdate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling interactionsIdPut(Async)");
        }

        // verify the required parameter 'interactionUpdate' is set
        if (interactionUpdate == null) {
            throw new ApiException("Missing the required parameter 'interactionUpdate' when calling interactionsIdPut(Async)");
        }

        return interactionsIdPutCall(id, interactionUpdate, _callback);

    }

    /**
     * Update interaction
     * Update an existing interaction
     * @param id  (required)
     * @param interactionUpdate  (required)
     * @return Interaction
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Interaction updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Interaction not found </td><td>  -  </td></tr>
     </table>
     */
    public Interaction interactionsIdPut(UUID id, InteractionUpdate interactionUpdate) throws ApiException {
        ApiResponse<Interaction> localVarResp = interactionsIdPutWithHttpInfo(id, interactionUpdate);
        return localVarResp.getData();
    }

    /**
     * Update interaction
     * Update an existing interaction
     * @param id  (required)
     * @param interactionUpdate  (required)
     * @return ApiResponse&lt;Interaction&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Interaction updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Interaction not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Interaction> interactionsIdPutWithHttpInfo(UUID id, InteractionUpdate interactionUpdate) throws ApiException {
        okhttp3.Call localVarCall = interactionsIdPutValidateBeforeCall(id, interactionUpdate, null);
        Type localVarReturnType = new TypeToken<Interaction>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Update interaction (asynchronously)
     * Update an existing interaction
     * @param id  (required)
     * @param interactionUpdate  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Interaction updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Interaction not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call interactionsIdPutAsync(UUID id, InteractionUpdate interactionUpdate, final ApiCallback<Interaction> _callback) throws ApiException {

        okhttp3.Call localVarCall = interactionsIdPutValidateBeforeCall(id, interactionUpdate, _callback);
        Type localVarReturnType = new TypeToken<Interaction>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for interactionsPost
     * @param interactionCreate  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Interaction created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call interactionsPostCall(InteractionCreate interactionCreate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = interactionCreate;

        // create path and map variables
        String localVarPath = "/interactions";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call interactionsPostValidateBeforeCall(InteractionCreate interactionCreate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'interactionCreate' is set
        if (interactionCreate == null) {
            throw new ApiException("Missing the required parameter 'interactionCreate' when calling interactionsPost(Async)");
        }

        return interactionsPostCall(interactionCreate, _callback);

    }

    /**
     * Create interaction
     * Create a new interaction record
     * @param interactionCreate  (required)
     * @return Interaction
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Interaction created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public Interaction interactionsPost(InteractionCreate interactionCreate) throws ApiException {
        ApiResponse<Interaction> localVarResp = interactionsPostWithHttpInfo(interactionCreate);
        return localVarResp.getData();
    }

    /**
     * Create interaction
     * Create a new interaction record
     * @param interactionCreate  (required)
     * @return ApiResponse&lt;Interaction&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Interaction created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Interaction> interactionsPostWithHttpInfo(InteractionCreate interactionCreate) throws ApiException {
        okhttp3.Call localVarCall = interactionsPostValidateBeforeCall(interactionCreate, null);
        Type localVarReturnType = new TypeToken<Interaction>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Create interaction (asynchronously)
     * Create a new interaction record
     * @param interactionCreate  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Interaction created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call interactionsPostAsync(InteractionCreate interactionCreate, final ApiCallback<Interaction> _callback) throws ApiException {

        okhttp3.Call localVarCall = interactionsPostValidateBeforeCall(interactionCreate, _callback);
        Type localVarReturnType = new TypeToken<Interaction>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
