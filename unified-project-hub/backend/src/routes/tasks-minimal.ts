import { Router, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';

const router = Router();
const prisma = new PrismaClient();

// GET /api/tasks - Get all tasks
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { project_id, status, assignee_id } = req.query;
    
    const where: any = {};
    
    if (project_id) {
      where.project_id = project_id as string;
    }
    
    if (status) {
      where.status = status as string;
    }
    
    if (assignee_id) {
      where.assignee_id = assignee_id as string;
    }

    const tasks = await prisma.task.findMany({
      where,
      include: {
        project: true,
        assignee: true,
        parent_task: true
      },
      orderBy: {
        created_at: 'desc'
      }
    });

    res.json({
      status: 'success',
      data: {
        tasks,
        pagination: {
          page: 1,
          limit: 20,
          total: tasks.length,
          pages: 1
        },
        filters: {},
        sorting: {},
        grouping: {}
      }
    });
  } catch (error) {
    console.error('Error fetching tasks:', error);
    res.status(500).json({ error: 'Failed to fetch tasks' });
  }
}));

// GET /api/tasks/:id - Get single task
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    
    const task = await prisma.task.findUnique({
      where: { id },
      include: {
        project: true,
        assignee: true,
        parent_task: true
      }
    });

    if (!task) {
      return res.status(404).json({ error: 'Task not found' });
    }

    res.json(task);
  } catch (error) {
    console.error('Error fetching task:', error);
    res.status(500).json({ error: 'Failed to fetch task' });
  }
}));

// POST /api/tasks - Create new task
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  try {
    const {
      name,
      description,
      project_id,
      assignee_id,
      parent_task_id,
      priority_level,
      effort_estimate,
      due_date,
      is_customer_visible,
      tags,
      external_links,
      estimated_hours
    } = req.body;

    const task = await prisma.task.create({
      data: {
        name,
        description: description || '',
        project_id,
        assignee_id: assignee_id || null,
        parent_task_id: parent_task_id || null,
        priority_level: priority_level || 'P3',
        effort_estimate: effort_estimate || 'M',
        due_date: due_date ? new Date(due_date) : new Date(),
        is_customer_visible: is_customer_visible || false,
        tags: tags || [],
        external_links: external_links || [],
        estimated_hours: estimated_hours || null,
        status: 'to_do'
      },
      include: {
        project: true,
        assignee: true,
        parent_task: true
      }
    });

    res.status(201).json(task);
  } catch (error) {
    console.error('Error creating task:', error);
    res.status(500).json({ error: 'Failed to create task' });
  }
}));

// PUT /api/tasks/:id - Update task
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      assignee_id,
      priority_level,
      effort_estimate,
      due_date,
      is_customer_visible,
      tags,
      external_links,
      estimated_hours,
      status
    } = req.body;

    const task = await prisma.task.update({
      where: { id },
      data: {
        name,
        description,
        assignee_id: assignee_id || null,
        priority_level,
        effort_estimate,
        due_date: due_date ? new Date(due_date) : null,
        is_customer_visible,
        tags: tags || [],
        external_links: external_links || [],
        estimated_hours,
        status
      },
      include: {
        project: true,
        assignee: true,
        parent_task: true
      }
    });

    res.json(task);
  } catch (error) {
    console.error('Error updating task:', error);
    res.status(500).json({ error: 'Failed to update task' });
  }
}));

// DELETE /api/tasks/:id - Delete task
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;

    await prisma.task.delete({
      where: { id }
    });

    res.status(204).send();
  } catch (error) {
    console.error('Error deleting task:', error);
    res.status(500).json({ error: 'Failed to delete task' });
  }
}));

// PATCH /api/tasks/:id/status - Update task status
router.patch('/:id/status', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const task = await prisma.task.update({
      where: { id },
      data: { status },
      include: {
        project: true,
        assignee: true,
        parent_task: true
      }
    });

    res.json(task);
  } catch (error) {
    console.error('Error updating task status:', error);
    res.status(500).json({ error: 'Failed to update task status' });
  }
}));

export default router;
