{"error": "Invalid token", "ip": "::1", "level": "error", "message": "Error occurred:", "method": "GET", "service": "Unified Project Hub", "stack": "Error: Invalid token\n    at authMiddleware (/Users/<USER>/workspaces/git/unified-project-hub/backend/src/middleware/auth.ts:71:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:328:13)", "timestamp": "2025-07-17 21:40:04", "url": "/api/projects", "userAgent": "curl/8.7.1", "version": "1.0.0"}