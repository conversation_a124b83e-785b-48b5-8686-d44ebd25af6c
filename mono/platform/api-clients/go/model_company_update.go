/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
)

// checks if the CompanyUpdate type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &CompanyUpdate{}

// CompanyUpdate struct for CompanyUpdate
type CompanyUpdate struct {
	Name *string `json:"name,omitempty"`
	Website *string `json:"website,omitempty"`
	Phone *string `json:"phone,omitempty"`
	Address *string `json:"address,omitempty"`
	Notes *string `json:"notes,omitempty"`
	CompanyStatusId *string `json:"company_status_id,omitempty"`
}

// NewCompanyUpdate instantiates a new CompanyUpdate object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewCompanyUpdate() *CompanyUpdate {
	this := CompanyUpdate{}
	return &this
}

// NewCompanyUpdateWithDefaults instantiates a new CompanyUpdate object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewCompanyUpdateWithDefaults() *CompanyUpdate {
	this := CompanyUpdate{}
	return &this
}

// GetName returns the Name field value if set, zero value otherwise.
func (o *CompanyUpdate) GetName() string {
	if o == nil || IsNil(o.Name) {
		var ret string
		return ret
	}
	return *o.Name
}

// GetNameOk returns a tuple with the Name field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyUpdate) GetNameOk() (*string, bool) {
	if o == nil || IsNil(o.Name) {
		return nil, false
	}
	return o.Name, true
}

// HasName returns a boolean if a field has been set.
func (o *CompanyUpdate) HasName() bool {
	if o != nil && !IsNil(o.Name) {
		return true
	}

	return false
}

// SetName gets a reference to the given string and assigns it to the Name field.
func (o *CompanyUpdate) SetName(v string) {
	o.Name = &v
}

// GetWebsite returns the Website field value if set, zero value otherwise.
func (o *CompanyUpdate) GetWebsite() string {
	if o == nil || IsNil(o.Website) {
		var ret string
		return ret
	}
	return *o.Website
}

// GetWebsiteOk returns a tuple with the Website field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyUpdate) GetWebsiteOk() (*string, bool) {
	if o == nil || IsNil(o.Website) {
		return nil, false
	}
	return o.Website, true
}

// HasWebsite returns a boolean if a field has been set.
func (o *CompanyUpdate) HasWebsite() bool {
	if o != nil && !IsNil(o.Website) {
		return true
	}

	return false
}

// SetWebsite gets a reference to the given string and assigns it to the Website field.
func (o *CompanyUpdate) SetWebsite(v string) {
	o.Website = &v
}

// GetPhone returns the Phone field value if set, zero value otherwise.
func (o *CompanyUpdate) GetPhone() string {
	if o == nil || IsNil(o.Phone) {
		var ret string
		return ret
	}
	return *o.Phone
}

// GetPhoneOk returns a tuple with the Phone field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyUpdate) GetPhoneOk() (*string, bool) {
	if o == nil || IsNil(o.Phone) {
		return nil, false
	}
	return o.Phone, true
}

// HasPhone returns a boolean if a field has been set.
func (o *CompanyUpdate) HasPhone() bool {
	if o != nil && !IsNil(o.Phone) {
		return true
	}

	return false
}

// SetPhone gets a reference to the given string and assigns it to the Phone field.
func (o *CompanyUpdate) SetPhone(v string) {
	o.Phone = &v
}

// GetAddress returns the Address field value if set, zero value otherwise.
func (o *CompanyUpdate) GetAddress() string {
	if o == nil || IsNil(o.Address) {
		var ret string
		return ret
	}
	return *o.Address
}

// GetAddressOk returns a tuple with the Address field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyUpdate) GetAddressOk() (*string, bool) {
	if o == nil || IsNil(o.Address) {
		return nil, false
	}
	return o.Address, true
}

// HasAddress returns a boolean if a field has been set.
func (o *CompanyUpdate) HasAddress() bool {
	if o != nil && !IsNil(o.Address) {
		return true
	}

	return false
}

// SetAddress gets a reference to the given string and assigns it to the Address field.
func (o *CompanyUpdate) SetAddress(v string) {
	o.Address = &v
}

// GetNotes returns the Notes field value if set, zero value otherwise.
func (o *CompanyUpdate) GetNotes() string {
	if o == nil || IsNil(o.Notes) {
		var ret string
		return ret
	}
	return *o.Notes
}

// GetNotesOk returns a tuple with the Notes field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyUpdate) GetNotesOk() (*string, bool) {
	if o == nil || IsNil(o.Notes) {
		return nil, false
	}
	return o.Notes, true
}

// HasNotes returns a boolean if a field has been set.
func (o *CompanyUpdate) HasNotes() bool {
	if o != nil && !IsNil(o.Notes) {
		return true
	}

	return false
}

// SetNotes gets a reference to the given string and assigns it to the Notes field.
func (o *CompanyUpdate) SetNotes(v string) {
	o.Notes = &v
}

// GetCompanyStatusId returns the CompanyStatusId field value if set, zero value otherwise.
func (o *CompanyUpdate) GetCompanyStatusId() string {
	if o == nil || IsNil(o.CompanyStatusId) {
		var ret string
		return ret
	}
	return *o.CompanyStatusId
}

// GetCompanyStatusIdOk returns a tuple with the CompanyStatusId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyUpdate) GetCompanyStatusIdOk() (*string, bool) {
	if o == nil || IsNil(o.CompanyStatusId) {
		return nil, false
	}
	return o.CompanyStatusId, true
}

// HasCompanyStatusId returns a boolean if a field has been set.
func (o *CompanyUpdate) HasCompanyStatusId() bool {
	if o != nil && !IsNil(o.CompanyStatusId) {
		return true
	}

	return false
}

// SetCompanyStatusId gets a reference to the given string and assigns it to the CompanyStatusId field.
func (o *CompanyUpdate) SetCompanyStatusId(v string) {
	o.CompanyStatusId = &v
}

func (o CompanyUpdate) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o CompanyUpdate) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Name) {
		toSerialize["name"] = o.Name
	}
	if !IsNil(o.Website) {
		toSerialize["website"] = o.Website
	}
	if !IsNil(o.Phone) {
		toSerialize["phone"] = o.Phone
	}
	if !IsNil(o.Address) {
		toSerialize["address"] = o.Address
	}
	if !IsNil(o.Notes) {
		toSerialize["notes"] = o.Notes
	}
	if !IsNil(o.CompanyStatusId) {
		toSerialize["company_status_id"] = o.CompanyStatusId
	}
	return toSerialize, nil
}

type NullableCompanyUpdate struct {
	value *CompanyUpdate
	isSet bool
}

func (v NullableCompanyUpdate) Get() *CompanyUpdate {
	return v.value
}

func (v *NullableCompanyUpdate) Set(val *CompanyUpdate) {
	v.value = val
	v.isSet = true
}

func (v NullableCompanyUpdate) IsSet() bool {
	return v.isSet
}

func (v *NullableCompanyUpdate) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableCompanyUpdate(val *CompanyUpdate) *NullableCompanyUpdate {
	return &NullableCompanyUpdate{value: val, isSet: true}
}

func (v NullableCompanyUpdate) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableCompanyUpdate) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


