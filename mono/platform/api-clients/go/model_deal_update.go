/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
)

// checks if the DealUpdate type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &DealUpdate{}

// DealUpdate struct for DealUpdate
type DealUpdate struct {
	Title *string `json:"title,omitempty"`
	Description *string `json:"description,omitempty"`
	EstimatedValue *float64 `json:"estimated_value,omitempty"`
	CompanyId *string `json:"company_id,omitempty"`
	DealStageId *string `json:"deal_stage_id,omitempty"`
	ExpectedCloseDate *string `json:"expected_close_date,omitempty"`
}

// NewDealUpdate instantiates a new DealUpdate object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDealUpdate() *DealUpdate {
	this := DealUpdate{}
	return &this
}

// NewDealUpdateWithDefaults instantiates a new DealUpdate object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDealUpdateWithDefaults() *DealUpdate {
	this := DealUpdate{}
	return &this
}

// GetTitle returns the Title field value if set, zero value otherwise.
func (o *DealUpdate) GetTitle() string {
	if o == nil || IsNil(o.Title) {
		var ret string
		return ret
	}
	return *o.Title
}

// GetTitleOk returns a tuple with the Title field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealUpdate) GetTitleOk() (*string, bool) {
	if o == nil || IsNil(o.Title) {
		return nil, false
	}
	return o.Title, true
}

// HasTitle returns a boolean if a field has been set.
func (o *DealUpdate) HasTitle() bool {
	if o != nil && !IsNil(o.Title) {
		return true
	}

	return false
}

// SetTitle gets a reference to the given string and assigns it to the Title field.
func (o *DealUpdate) SetTitle(v string) {
	o.Title = &v
}

// GetDescription returns the Description field value if set, zero value otherwise.
func (o *DealUpdate) GetDescription() string {
	if o == nil || IsNil(o.Description) {
		var ret string
		return ret
	}
	return *o.Description
}

// GetDescriptionOk returns a tuple with the Description field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealUpdate) GetDescriptionOk() (*string, bool) {
	if o == nil || IsNil(o.Description) {
		return nil, false
	}
	return o.Description, true
}

// HasDescription returns a boolean if a field has been set.
func (o *DealUpdate) HasDescription() bool {
	if o != nil && !IsNil(o.Description) {
		return true
	}

	return false
}

// SetDescription gets a reference to the given string and assigns it to the Description field.
func (o *DealUpdate) SetDescription(v string) {
	o.Description = &v
}

// GetEstimatedValue returns the EstimatedValue field value if set, zero value otherwise.
func (o *DealUpdate) GetEstimatedValue() float64 {
	if o == nil || IsNil(o.EstimatedValue) {
		var ret float64
		return ret
	}
	return *o.EstimatedValue
}

// GetEstimatedValueOk returns a tuple with the EstimatedValue field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealUpdate) GetEstimatedValueOk() (*float64, bool) {
	if o == nil || IsNil(o.EstimatedValue) {
		return nil, false
	}
	return o.EstimatedValue, true
}

// HasEstimatedValue returns a boolean if a field has been set.
func (o *DealUpdate) HasEstimatedValue() bool {
	if o != nil && !IsNil(o.EstimatedValue) {
		return true
	}

	return false
}

// SetEstimatedValue gets a reference to the given float64 and assigns it to the EstimatedValue field.
func (o *DealUpdate) SetEstimatedValue(v float64) {
	o.EstimatedValue = &v
}

// GetCompanyId returns the CompanyId field value if set, zero value otherwise.
func (o *DealUpdate) GetCompanyId() string {
	if o == nil || IsNil(o.CompanyId) {
		var ret string
		return ret
	}
	return *o.CompanyId
}

// GetCompanyIdOk returns a tuple with the CompanyId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealUpdate) GetCompanyIdOk() (*string, bool) {
	if o == nil || IsNil(o.CompanyId) {
		return nil, false
	}
	return o.CompanyId, true
}

// HasCompanyId returns a boolean if a field has been set.
func (o *DealUpdate) HasCompanyId() bool {
	if o != nil && !IsNil(o.CompanyId) {
		return true
	}

	return false
}

// SetCompanyId gets a reference to the given string and assigns it to the CompanyId field.
func (o *DealUpdate) SetCompanyId(v string) {
	o.CompanyId = &v
}

// GetDealStageId returns the DealStageId field value if set, zero value otherwise.
func (o *DealUpdate) GetDealStageId() string {
	if o == nil || IsNil(o.DealStageId) {
		var ret string
		return ret
	}
	return *o.DealStageId
}

// GetDealStageIdOk returns a tuple with the DealStageId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealUpdate) GetDealStageIdOk() (*string, bool) {
	if o == nil || IsNil(o.DealStageId) {
		return nil, false
	}
	return o.DealStageId, true
}

// HasDealStageId returns a boolean if a field has been set.
func (o *DealUpdate) HasDealStageId() bool {
	if o != nil && !IsNil(o.DealStageId) {
		return true
	}

	return false
}

// SetDealStageId gets a reference to the given string and assigns it to the DealStageId field.
func (o *DealUpdate) SetDealStageId(v string) {
	o.DealStageId = &v
}

// GetExpectedCloseDate returns the ExpectedCloseDate field value if set, zero value otherwise.
func (o *DealUpdate) GetExpectedCloseDate() string {
	if o == nil || IsNil(o.ExpectedCloseDate) {
		var ret string
		return ret
	}
	return *o.ExpectedCloseDate
}

// GetExpectedCloseDateOk returns a tuple with the ExpectedCloseDate field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealUpdate) GetExpectedCloseDateOk() (*string, bool) {
	if o == nil || IsNil(o.ExpectedCloseDate) {
		return nil, false
	}
	return o.ExpectedCloseDate, true
}

// HasExpectedCloseDate returns a boolean if a field has been set.
func (o *DealUpdate) HasExpectedCloseDate() bool {
	if o != nil && !IsNil(o.ExpectedCloseDate) {
		return true
	}

	return false
}

// SetExpectedCloseDate gets a reference to the given string and assigns it to the ExpectedCloseDate field.
func (o *DealUpdate) SetExpectedCloseDate(v string) {
	o.ExpectedCloseDate = &v
}

func (o DealUpdate) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o DealUpdate) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Title) {
		toSerialize["title"] = o.Title
	}
	if !IsNil(o.Description) {
		toSerialize["description"] = o.Description
	}
	if !IsNil(o.EstimatedValue) {
		toSerialize["estimated_value"] = o.EstimatedValue
	}
	if !IsNil(o.CompanyId) {
		toSerialize["company_id"] = o.CompanyId
	}
	if !IsNil(o.DealStageId) {
		toSerialize["deal_stage_id"] = o.DealStageId
	}
	if !IsNil(o.ExpectedCloseDate) {
		toSerialize["expected_close_date"] = o.ExpectedCloseDate
	}
	return toSerialize, nil
}

type NullableDealUpdate struct {
	value *DealUpdate
	isSet bool
}

func (v NullableDealUpdate) Get() *DealUpdate {
	return v.value
}

func (v *NullableDealUpdate) Set(val *DealUpdate) {
	v.value = val
	v.isSet = true
}

func (v NullableDealUpdate) IsSet() bool {
	return v.isSet
}

func (v *NullableDealUpdate) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDealUpdate(val *DealUpdate) *NullableDealUpdate {
	return &NullableDealUpdate{value: val, isSet: true}
}

func (v NullableDealUpdate) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDealUpdate) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


