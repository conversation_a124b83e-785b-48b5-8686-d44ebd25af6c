# InteractionUpdate

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**InteractionType** | Pointer to **string** |  | [optional] 
**Notes** | Pointer to **string** |  | [optional] 
**InteractionDatetime** | Pointer to **time.Time** |  | [optional] 
**CompanyId** | Pointer to **string** |  | [optional] 
**ContactId** | Pointer to **string** |  | [optional] 

## Methods

### NewInteractionUpdate

`func NewInteractionUpdate() *InteractionUpdate`

NewInteractionUpdate instantiates a new InteractionUpdate object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewInteractionUpdateWithDefaults

`func NewInteractionUpdateWithDefaults() *InteractionUpdate`

NewInteractionUpdateWithDefaults instantiates a new InteractionUpdate object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetInteractionType

`func (o *InteractionUpdate) GetInteractionType() string`

GetInteractionType returns the InteractionType field if non-nil, zero value otherwise.

### GetInteractionTypeOk

`func (o *InteractionUpdate) GetInteractionTypeOk() (*string, bool)`

GetInteractionTypeOk returns a tuple with the InteractionType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetInteractionType

`func (o *InteractionUpdate) SetInteractionType(v string)`

SetInteractionType sets InteractionType field to given value.

### HasInteractionType

`func (o *InteractionUpdate) HasInteractionType() bool`

HasInteractionType returns a boolean if a field has been set.

### GetNotes

`func (o *InteractionUpdate) GetNotes() string`

GetNotes returns the Notes field if non-nil, zero value otherwise.

### GetNotesOk

`func (o *InteractionUpdate) GetNotesOk() (*string, bool)`

GetNotesOk returns a tuple with the Notes field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetNotes

`func (o *InteractionUpdate) SetNotes(v string)`

SetNotes sets Notes field to given value.

### HasNotes

`func (o *InteractionUpdate) HasNotes() bool`

HasNotes returns a boolean if a field has been set.

### GetInteractionDatetime

`func (o *InteractionUpdate) GetInteractionDatetime() time.Time`

GetInteractionDatetime returns the InteractionDatetime field if non-nil, zero value otherwise.

### GetInteractionDatetimeOk

`func (o *InteractionUpdate) GetInteractionDatetimeOk() (*time.Time, bool)`

GetInteractionDatetimeOk returns a tuple with the InteractionDatetime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetInteractionDatetime

`func (o *InteractionUpdate) SetInteractionDatetime(v time.Time)`

SetInteractionDatetime sets InteractionDatetime field to given value.

### HasInteractionDatetime

`func (o *InteractionUpdate) HasInteractionDatetime() bool`

HasInteractionDatetime returns a boolean if a field has been set.

### GetCompanyId

`func (o *InteractionUpdate) GetCompanyId() string`

GetCompanyId returns the CompanyId field if non-nil, zero value otherwise.

### GetCompanyIdOk

`func (o *InteractionUpdate) GetCompanyIdOk() (*string, bool)`

GetCompanyIdOk returns a tuple with the CompanyId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCompanyId

`func (o *InteractionUpdate) SetCompanyId(v string)`

SetCompanyId sets CompanyId field to given value.

### HasCompanyId

`func (o *InteractionUpdate) HasCompanyId() bool`

HasCompanyId returns a boolean if a field has been set.

### GetContactId

`func (o *InteractionUpdate) GetContactId() string`

GetContactId returns the ContactId field if non-nil, zero value otherwise.

### GetContactIdOk

`func (o *InteractionUpdate) GetContactIdOk() (*string, bool)`

GetContactIdOk returns a tuple with the ContactId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetContactId

`func (o *InteractionUpdate) SetContactId(v string)`

SetContactId sets ContactId field to given value.

### HasContactId

`func (o *InteractionUpdate) HasContactId() bool`

HasContactId returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


