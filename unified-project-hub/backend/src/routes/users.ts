import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireRole } from '../middleware/auth';

const router = Router();
const prisma = new PrismaClient();

// Validation schemas
const updateUserSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  title: z.string().optional(),
  department: z.string().optional(),
  avatar_url: z.string().url().optional(),
  weekly_capacity_hours: z.number().positive().optional(),
  skills: z.array(z.string()).optional(),
});

const updateUserRoleSchema = z.object({
  role: z.enum(['admin', 'project_manager', 'team_member', 'customer', 'requirements_analyst']),
  is_active: z.boolean().optional(),
});

/**
 * @route GET /api/users
 * @desc Get all users (filtered by role)
 * @access Private (Admin, Project Manager)
 */
router.get('/', requireRole('admin', 'project_manager'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { page = '1', limit = '10', role, department, is_active, search } = req.query;
  const skip = (parseInt(page as string) - 1) * parseInt(limit as string);

  let whereClause: any = {};

  // Apply filters
  if (role) whereClause.role = role;
  if (department) whereClause.department = department;
  if (is_active !== undefined) whereClause.is_active = is_active === 'true';
  if (search) {
    whereClause.OR = [
      { name: { contains: search as string, mode: 'insensitive' } },
      { email: { contains: search as string, mode: 'insensitive' } },
      { title: { contains: search as string, mode: 'insensitive' } },
    ];
  }

  const [users, total] = await Promise.all([
    prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        title: true,
        department: true,
        avatar_url: true,
        is_active: true,
        weekly_capacity_hours: true,
        current_utilization_percentage: true,
        skills: true,
        created_at: true,
        updated_at: true,
        _count: {
          select: {
            led_projects: true,
            assigned_projects: true,
            assigned_tasks: true,
          },
        },
      },
      skip,
      take: parseInt(limit as string),
      orderBy: { name: 'asc' },
    }),
    prisma.user.count({ where: whereClause }),
  ]);

  res.json({
    status: 'success',
    data: {
      users,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        pages: Math.ceil(total / parseInt(limit as string)),
      },
    },
  });
}));

/**
 * @route GET /api/users/:id
 * @desc Get user by ID
 * @access Private (Admin, Project Manager, Self)
 */
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Users can view their own profile, admins and PMs can view all
  if (req.user?.id !== id && !['admin', 'project_manager'].includes(req.user?.role || '')) {
    return res.status(403).json({
      status: 'error',
      message: 'Access denied',
    });
  }

  const user = await prisma.user.findUnique({
    where: { id },
    select: {
      id: true,
      email: true,
      name: true,
      role: true,
      title: true,
      department: true,
      avatar_url: true,
      is_active: true,
      weekly_capacity_hours: true,
      current_utilization_percentage: true,
      skills: true,
      created_at: true,
      updated_at: true,
      led_projects: {
        select: {
          id: true,
          name: true,
          status: true,
          progress_percentage: true,
        },
        orderBy: { updated_at: 'desc' },
        take: 10,
      },
      assigned_projects: {
        include: {
          project: {
            select: {
              id: true,
              name: true,
              status: true,
              progress_percentage: true,
            },
          },
        },
        take: 10,
      },
      assigned_tasks: {
        select: {
          id: true,
          name: true,
          status: true,
          due_date: true,
          project: {
            select: { id: true, name: true },
          },
        },
        where: {
          status: { in: ['to_do', 'in_progress'] },
        },
        orderBy: { due_date: 'asc' },
        take: 10,
      },
    },
  });

  if (!user) {
    return res.status(404).json({
      status: 'error',
      message: 'User not found',
    });
  }

  res.json({
    status: 'success',
    data: { user },
  });
}));

/**
 * @route PUT /api/users/:id
 * @desc Update user profile
 * @access Private (Admin, Project Manager, Self)
 */
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const updateData = updateUserSchema.parse(req.body);

  // Users can update their own profile, admins and PMs can update all
  if (req.user?.id !== id && !['admin', 'project_manager'].includes(req.user?.role || '')) {
    return res.status(403).json({
      status: 'error',
      message: 'Access denied',
    });
  }

  const user = await prisma.user.update({
    where: { id },
    data: updateData,
    select: {
      id: true,
      email: true,
      name: true,
      role: true,
      title: true,
      department: true,
      avatar_url: true,
      is_active: true,
      weekly_capacity_hours: true,
      current_utilization_percentage: true,
      skills: true,
      updated_at: true,
    },
  });

  res.json({
    status: 'success',
    message: 'User updated successfully',
    data: { user },
  });
}));

/**
 * @route PUT /api/users/:id/role
 * @desc Update user role and status
 * @access Private (Admin only)
 */
router.put('/:id/role', requireRole('admin'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const { role, is_active } = updateUserRoleSchema.parse(req.body);

  // Prevent self-demotion from admin
  if (req.user?.id === id && req.user.role === 'admin' && role !== 'admin') {
    return res.status(400).json({
      status: 'error',
      message: 'Cannot change your own admin role',
    });
  }

  const user = await prisma.user.update({
    where: { id },
    data: {
      role,
      ...(is_active !== undefined && { is_active }),
    },
    select: {
      id: true,
      email: true,
      name: true,
      role: true,
      title: true,
      department: true,
      is_active: true,
      updated_at: true,
    },
  });

  res.json({
    status: 'success',
    message: 'User role updated successfully',
    data: { user },
  });
}));

/**
 * @route GET /api/users/team-members/available
 * @desc Get available team members for assignment
 * @access Private
 */
router.get('/team-members/available', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { project_id, exclude_customer } = req.query;

  let whereClause: any = {
    is_active: true,
    role: { in: ['admin', 'project_manager', 'team_member', 'requirements_analyst'] },
  };

  if (exclude_customer === 'true') {
    whereClause.role = { not: 'customer' };
  }

  const users = await prisma.user.findMany({
    where: whereClause,
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      title: true,
      department: true,
      avatar_url: true,
      weekly_capacity_hours: true,
      current_utilization_percentage: true,
      skills: true,
    },
    orderBy: [
      { role: 'asc' },
      { name: 'asc' },
    ],
  });

  res.json({
    status: 'success',
    data: { users },
  });
}));

/**
 * @route DELETE /api/users/:id
 * @desc Deactivate user (soft delete)
 * @access Private (Admin only)
 */
router.delete('/:id', requireRole('admin'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Prevent self-deletion
  if (req.user?.id === id) {
    return res.status(400).json({
      status: 'error',
      message: 'Cannot delete your own account',
    });
  }

  // Check if user has active assignments
  const activeAssignments = await prisma.user.findUnique({
    where: { id },
    select: {
      led_projects: {
        where: { status: { in: ['in_progress', 'not_started'] } },
        select: { id: true },
      },
      assigned_tasks: {
        where: { status: { in: ['to_do', 'in_progress'] } },
        select: { id: true },
      },
    },
  });

  if (activeAssignments?.led_projects.length || activeAssignments?.assigned_tasks.length) {
    return res.status(400).json({
      status: 'error',
      message: 'Cannot delete user with active project or task assignments',
    });
  }

  // Soft delete by deactivating
  await prisma.user.update({
    where: { id },
    data: { is_active: false },
  });

  res.json({
    status: 'success',
    message: 'User deactivated successfully',
  });
}));

export default router;
