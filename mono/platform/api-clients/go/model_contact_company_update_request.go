/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"bytes"
	"fmt"
)

// checks if the ContactCompanyUpdateRequest type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ContactCompanyUpdateRequest{}

// ContactCompanyUpdateRequest struct for ContactCompanyUpdateRequest
type ContactCompanyUpdateRequest struct {
	CompanyId string `json:"company_id"`
}

type _ContactCompanyUpdateRequest ContactCompanyUpdateRequest

// NewContactCompanyUpdateRequest instantiates a new ContactCompanyUpdateRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewContactCompanyUpdateRequest(companyId string) *ContactCompanyUpdateRequest {
	this := ContactCompanyUpdateRequest{}
	this.CompanyId = companyId
	return &this
}

// NewContactCompanyUpdateRequestWithDefaults instantiates a new ContactCompanyUpdateRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewContactCompanyUpdateRequestWithDefaults() *ContactCompanyUpdateRequest {
	this := ContactCompanyUpdateRequest{}
	return &this
}

// GetCompanyId returns the CompanyId field value
func (o *ContactCompanyUpdateRequest) GetCompanyId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.CompanyId
}

// GetCompanyIdOk returns a tuple with the CompanyId field value
// and a boolean to check if the value has been set.
func (o *ContactCompanyUpdateRequest) GetCompanyIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.CompanyId, true
}

// SetCompanyId sets field value
func (o *ContactCompanyUpdateRequest) SetCompanyId(v string) {
	o.CompanyId = v
}

func (o ContactCompanyUpdateRequest) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ContactCompanyUpdateRequest) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["company_id"] = o.CompanyId
	return toSerialize, nil
}

func (o *ContactCompanyUpdateRequest) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"company_id",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varContactCompanyUpdateRequest := _ContactCompanyUpdateRequest{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varContactCompanyUpdateRequest)

	if err != nil {
		return err
	}

	*o = ContactCompanyUpdateRequest(varContactCompanyUpdateRequest)

	return err
}

type NullableContactCompanyUpdateRequest struct {
	value *ContactCompanyUpdateRequest
	isSet bool
}

func (v NullableContactCompanyUpdateRequest) Get() *ContactCompanyUpdateRequest {
	return v.value
}

func (v *NullableContactCompanyUpdateRequest) Set(val *ContactCompanyUpdateRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableContactCompanyUpdateRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableContactCompanyUpdateRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableContactCompanyUpdateRequest(val *ContactCompanyUpdateRequest) *NullableContactCompanyUpdateRequest {
	return &NullableContactCompanyUpdateRequest{value: val, isSet: true}
}

func (v NullableContactCompanyUpdateRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableContactCompanyUpdateRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


