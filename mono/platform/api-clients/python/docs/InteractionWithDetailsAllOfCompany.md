# InteractionWithDetailsAllOfCompany


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **str** |  | [optional] 
**name** | **str** |  | [optional] 

## Example

```python
from crm_api_client.models.interaction_with_details_all_of_company import InteractionWithDetailsAllOfCompany

# TODO update the JSON string below
json = "{}"
# create an instance of InteractionWithDetailsAllOfCompany from a JSON string
interaction_with_details_all_of_company_instance = InteractionWithDetailsAllOfCompany.from_json(json)
# print the JSON string representation of the object
print(InteractionWithDetailsAllOfCompany.to_json())

# convert the object into a dict
interaction_with_details_all_of_company_dict = interaction_with_details_all_of_company_instance.to_dict()
# create an instance of InteractionWithDetailsAllOfCompany from a dict
interaction_with_details_all_of_company_from_dict = InteractionWithDetailsAllOfCompany.from_dict(interaction_with_details_all_of_company_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


