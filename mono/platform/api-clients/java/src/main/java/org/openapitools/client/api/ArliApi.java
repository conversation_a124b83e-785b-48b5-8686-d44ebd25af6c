/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.api;

import org.openapitools.client.ApiCallback;
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.ApiResponse;
import org.openapitools.client.Configuration;
import org.openapitools.client.Pair;
import org.openapitools.client.ProgressRequestBody;
import org.openapitools.client.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import org.openapitools.client.model.ArliDocumentsGet200Response;
import org.openapitools.client.model.ArliDocumentsVectorizePost200Response;
import org.openapitools.client.model.ArliDocumentsVectorizePostRequest;
import org.openapitools.client.model.Document;
import org.openapitools.client.model.DocumentCreate;
import org.openapitools.client.model.DocumentDetails;
import org.openapitools.client.model.DocumentUpdate;
import org.openapitools.client.model.FilterTag;
import org.openapitools.client.model.FilterTagCreate;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ArliApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public ArliApi() {
        this(Configuration.getDefaultApiClient());
    }

    public ArliApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for arliDocumentsGet
     * @param filterTags Filter by tags (optional)
     * @param metadataFilter Filter by metadata properties (optional)
     * @param limit  (optional, default to 20)
     * @param offset  (optional, default to 0)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of documents </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call arliDocumentsGetCall(List<String> filterTags, Object metadataFilter, Integer limit, Integer offset, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/arli/documents";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (filterTags != null) {
            localVarCollectionQueryParams.addAll(localVarApiClient.parameterToPairs("multi", "filter_tags", filterTags));
        }

        if (metadataFilter != null) {
            localVarQueryParams.addAll(localVarApiClient.freeFormParameterToPairs(metadataFilter));
        }

        if (limit != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("limit", limit));
        }

        if (offset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("offset", offset));
        }

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call arliDocumentsGetValidateBeforeCall(List<String> filterTags, Object metadataFilter, Integer limit, Integer offset, final ApiCallback _callback) throws ApiException {
        return arliDocumentsGetCall(filterTags, metadataFilter, limit, offset, _callback);

    }

    /**
     * List documents
     * Retrieve a list of processed documents
     * @param filterTags Filter by tags (optional)
     * @param metadataFilter Filter by metadata properties (optional)
     * @param limit  (optional, default to 20)
     * @param offset  (optional, default to 0)
     * @return ArliDocumentsGet200Response
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of documents </td><td>  -  </td></tr>
     </table>
     */
    public ArliDocumentsGet200Response arliDocumentsGet(List<String> filterTags, Object metadataFilter, Integer limit, Integer offset) throws ApiException {
        ApiResponse<ArliDocumentsGet200Response> localVarResp = arliDocumentsGetWithHttpInfo(filterTags, metadataFilter, limit, offset);
        return localVarResp.getData();
    }

    /**
     * List documents
     * Retrieve a list of processed documents
     * @param filterTags Filter by tags (optional)
     * @param metadataFilter Filter by metadata properties (optional)
     * @param limit  (optional, default to 20)
     * @param offset  (optional, default to 0)
     * @return ApiResponse&lt;ArliDocumentsGet200Response&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of documents </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ArliDocumentsGet200Response> arliDocumentsGetWithHttpInfo(List<String> filterTags, Object metadataFilter, Integer limit, Integer offset) throws ApiException {
        okhttp3.Call localVarCall = arliDocumentsGetValidateBeforeCall(filterTags, metadataFilter, limit, offset, null);
        Type localVarReturnType = new TypeToken<ArliDocumentsGet200Response>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * List documents (asynchronously)
     * Retrieve a list of processed documents
     * @param filterTags Filter by tags (optional)
     * @param metadataFilter Filter by metadata properties (optional)
     * @param limit  (optional, default to 20)
     * @param offset  (optional, default to 0)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of documents </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call arliDocumentsGetAsync(List<String> filterTags, Object metadataFilter, Integer limit, Integer offset, final ApiCallback<ArliDocumentsGet200Response> _callback) throws ApiException {

        okhttp3.Call localVarCall = arliDocumentsGetValidateBeforeCall(filterTags, metadataFilter, limit, offset, _callback);
        Type localVarReturnType = new TypeToken<ArliDocumentsGet200Response>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for arliDocumentsIdDelete
     * @param id  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Document deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Document not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call arliDocumentsIdDeleteCall(UUID id, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/arli/documents/{id}"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call arliDocumentsIdDeleteValidateBeforeCall(UUID id, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling arliDocumentsIdDelete(Async)");
        }

        return arliDocumentsIdDeleteCall(id, _callback);

    }

    /**
     * Delete document
     * Delete a document
     * @param id  (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Document deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Document not found </td><td>  -  </td></tr>
     </table>
     */
    public void arliDocumentsIdDelete(UUID id) throws ApiException {
        arliDocumentsIdDeleteWithHttpInfo(id);
    }

    /**
     * Delete document
     * Delete a document
     * @param id  (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Document deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Document not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> arliDocumentsIdDeleteWithHttpInfo(UUID id) throws ApiException {
        okhttp3.Call localVarCall = arliDocumentsIdDeleteValidateBeforeCall(id, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Delete document (asynchronously)
     * Delete a document
     * @param id  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Document deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Document not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call arliDocumentsIdDeleteAsync(UUID id, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = arliDocumentsIdDeleteValidateBeforeCall(id, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for arliDocumentsIdGet
     * @param id  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Document details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Document not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call arliDocumentsIdGetCall(UUID id, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/arli/documents/{id}"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call arliDocumentsIdGetValidateBeforeCall(UUID id, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling arliDocumentsIdGet(Async)");
        }

        return arliDocumentsIdGetCall(id, _callback);

    }

    /**
     * Get document
     * Retrieve a specific document by ID
     * @param id  (required)
     * @return DocumentDetails
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Document details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Document not found </td><td>  -  </td></tr>
     </table>
     */
    public DocumentDetails arliDocumentsIdGet(UUID id) throws ApiException {
        ApiResponse<DocumentDetails> localVarResp = arliDocumentsIdGetWithHttpInfo(id);
        return localVarResp.getData();
    }

    /**
     * Get document
     * Retrieve a specific document by ID
     * @param id  (required)
     * @return ApiResponse&lt;DocumentDetails&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Document details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Document not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<DocumentDetails> arliDocumentsIdGetWithHttpInfo(UUID id) throws ApiException {
        okhttp3.Call localVarCall = arliDocumentsIdGetValidateBeforeCall(id, null);
        Type localVarReturnType = new TypeToken<DocumentDetails>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get document (asynchronously)
     * Retrieve a specific document by ID
     * @param id  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Document details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Document not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call arliDocumentsIdGetAsync(UUID id, final ApiCallback<DocumentDetails> _callback) throws ApiException {

        okhttp3.Call localVarCall = arliDocumentsIdGetValidateBeforeCall(id, _callback);
        Type localVarReturnType = new TypeToken<DocumentDetails>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for arliDocumentsIdPut
     * @param id  (required)
     * @param documentUpdate  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Document updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Document not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call arliDocumentsIdPutCall(UUID id, DocumentUpdate documentUpdate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = documentUpdate;

        // create path and map variables
        String localVarPath = "/arli/documents/{id}"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call arliDocumentsIdPutValidateBeforeCall(UUID id, DocumentUpdate documentUpdate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling arliDocumentsIdPut(Async)");
        }

        // verify the required parameter 'documentUpdate' is set
        if (documentUpdate == null) {
            throw new ApiException("Missing the required parameter 'documentUpdate' when calling arliDocumentsIdPut(Async)");
        }

        return arliDocumentsIdPutCall(id, documentUpdate, _callback);

    }

    /**
     * Update document
     * Update an existing document
     * @param id  (required)
     * @param documentUpdate  (required)
     * @return Document
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Document updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Document not found </td><td>  -  </td></tr>
     </table>
     */
    public Document arliDocumentsIdPut(UUID id, DocumentUpdate documentUpdate) throws ApiException {
        ApiResponse<Document> localVarResp = arliDocumentsIdPutWithHttpInfo(id, documentUpdate);
        return localVarResp.getData();
    }

    /**
     * Update document
     * Update an existing document
     * @param id  (required)
     * @param documentUpdate  (required)
     * @return ApiResponse&lt;Document&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Document updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Document not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Document> arliDocumentsIdPutWithHttpInfo(UUID id, DocumentUpdate documentUpdate) throws ApiException {
        okhttp3.Call localVarCall = arliDocumentsIdPutValidateBeforeCall(id, documentUpdate, null);
        Type localVarReturnType = new TypeToken<Document>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Update document (asynchronously)
     * Update an existing document
     * @param id  (required)
     * @param documentUpdate  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Document updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Document not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call arliDocumentsIdPutAsync(UUID id, DocumentUpdate documentUpdate, final ApiCallback<Document> _callback) throws ApiException {

        okhttp3.Call localVarCall = arliDocumentsIdPutValidateBeforeCall(id, documentUpdate, _callback);
        Type localVarReturnType = new TypeToken<Document>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for arliDocumentsPost
     * @param documentCreate  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Document created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call arliDocumentsPostCall(DocumentCreate documentCreate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = documentCreate;

        // create path and map variables
        String localVarPath = "/arli/documents";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call arliDocumentsPostValidateBeforeCall(DocumentCreate documentCreate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'documentCreate' is set
        if (documentCreate == null) {
            throw new ApiException("Missing the required parameter 'documentCreate' when calling arliDocumentsPost(Async)");
        }

        return arliDocumentsPostCall(documentCreate, _callback);

    }

    /**
     * Create document
     * Create a new document for processing
     * @param documentCreate  (required)
     * @return Document
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Document created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public Document arliDocumentsPost(DocumentCreate documentCreate) throws ApiException {
        ApiResponse<Document> localVarResp = arliDocumentsPostWithHttpInfo(documentCreate);
        return localVarResp.getData();
    }

    /**
     * Create document
     * Create a new document for processing
     * @param documentCreate  (required)
     * @return ApiResponse&lt;Document&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Document created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Document> arliDocumentsPostWithHttpInfo(DocumentCreate documentCreate) throws ApiException {
        okhttp3.Call localVarCall = arliDocumentsPostValidateBeforeCall(documentCreate, null);
        Type localVarReturnType = new TypeToken<Document>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Create document (asynchronously)
     * Create a new document for processing
     * @param documentCreate  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Document created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call arliDocumentsPostAsync(DocumentCreate documentCreate, final ApiCallback<Document> _callback) throws ApiException {

        okhttp3.Call localVarCall = arliDocumentsPostValidateBeforeCall(documentCreate, _callback);
        Type localVarReturnType = new TypeToken<Document>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for arliDocumentsVectorizePost
     * @param arliDocumentsVectorizePostRequest  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Document vectorized successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Vectorization failed </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call arliDocumentsVectorizePostCall(ArliDocumentsVectorizePostRequest arliDocumentsVectorizePostRequest, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = arliDocumentsVectorizePostRequest;

        // create path and map variables
        String localVarPath = "/arli/documents/vectorize";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call arliDocumentsVectorizePostValidateBeforeCall(ArliDocumentsVectorizePostRequest arliDocumentsVectorizePostRequest, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'arliDocumentsVectorizePostRequest' is set
        if (arliDocumentsVectorizePostRequest == null) {
            throw new ApiException("Missing the required parameter 'arliDocumentsVectorizePostRequest' when calling arliDocumentsVectorizePost(Async)");
        }

        return arliDocumentsVectorizePostCall(arliDocumentsVectorizePostRequest, _callback);

    }

    /**
     * Vectorize document
     * Process a document through the AI vectorization pipeline
     * @param arliDocumentsVectorizePostRequest  (required)
     * @return ArliDocumentsVectorizePost200Response
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Document vectorized successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Vectorization failed </td><td>  -  </td></tr>
     </table>
     */
    public ArliDocumentsVectorizePost200Response arliDocumentsVectorizePost(ArliDocumentsVectorizePostRequest arliDocumentsVectorizePostRequest) throws ApiException {
        ApiResponse<ArliDocumentsVectorizePost200Response> localVarResp = arliDocumentsVectorizePostWithHttpInfo(arliDocumentsVectorizePostRequest);
        return localVarResp.getData();
    }

    /**
     * Vectorize document
     * Process a document through the AI vectorization pipeline
     * @param arliDocumentsVectorizePostRequest  (required)
     * @return ApiResponse&lt;ArliDocumentsVectorizePost200Response&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Document vectorized successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Vectorization failed </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ArliDocumentsVectorizePost200Response> arliDocumentsVectorizePostWithHttpInfo(ArliDocumentsVectorizePostRequest arliDocumentsVectorizePostRequest) throws ApiException {
        okhttp3.Call localVarCall = arliDocumentsVectorizePostValidateBeforeCall(arliDocumentsVectorizePostRequest, null);
        Type localVarReturnType = new TypeToken<ArliDocumentsVectorizePost200Response>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Vectorize document (asynchronously)
     * Process a document through the AI vectorization pipeline
     * @param arliDocumentsVectorizePostRequest  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Document vectorized successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Vectorization failed </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call arliDocumentsVectorizePostAsync(ArliDocumentsVectorizePostRequest arliDocumentsVectorizePostRequest, final ApiCallback<ArliDocumentsVectorizePost200Response> _callback) throws ApiException {

        okhttp3.Call localVarCall = arliDocumentsVectorizePostValidateBeforeCall(arliDocumentsVectorizePostRequest, _callback);
        Type localVarReturnType = new TypeToken<ArliDocumentsVectorizePost200Response>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for arliFilterTagsGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of filter tags </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call arliFilterTagsGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/arli/filter-tags";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call arliFilterTagsGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return arliFilterTagsGetCall(_callback);

    }

    /**
     * List filter tags
     * Retrieve all available filter tags
     * @return List&lt;FilterTag&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of filter tags </td><td>  -  </td></tr>
     </table>
     */
    public List<FilterTag> arliFilterTagsGet() throws ApiException {
        ApiResponse<List<FilterTag>> localVarResp = arliFilterTagsGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * List filter tags
     * Retrieve all available filter tags
     * @return ApiResponse&lt;List&lt;FilterTag&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of filter tags </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<FilterTag>> arliFilterTagsGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = arliFilterTagsGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<List<FilterTag>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * List filter tags (asynchronously)
     * Retrieve all available filter tags
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of filter tags </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call arliFilterTagsGetAsync(final ApiCallback<List<FilterTag>> _callback) throws ApiException {

        okhttp3.Call localVarCall = arliFilterTagsGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<List<FilterTag>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for arliFilterTagsPost
     * @param filterTagCreate  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Filter tag created successfully </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call arliFilterTagsPostCall(FilterTagCreate filterTagCreate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = filterTagCreate;

        // create path and map variables
        String localVarPath = "/arli/filter-tags";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call arliFilterTagsPostValidateBeforeCall(FilterTagCreate filterTagCreate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'filterTagCreate' is set
        if (filterTagCreate == null) {
            throw new ApiException("Missing the required parameter 'filterTagCreate' when calling arliFilterTagsPost(Async)");
        }

        return arliFilterTagsPostCall(filterTagCreate, _callback);

    }

    /**
     * Create filter tag
     * Create a new filter tag
     * @param filterTagCreate  (required)
     * @return FilterTag
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Filter tag created successfully </td><td>  -  </td></tr>
     </table>
     */
    public FilterTag arliFilterTagsPost(FilterTagCreate filterTagCreate) throws ApiException {
        ApiResponse<FilterTag> localVarResp = arliFilterTagsPostWithHttpInfo(filterTagCreate);
        return localVarResp.getData();
    }

    /**
     * Create filter tag
     * Create a new filter tag
     * @param filterTagCreate  (required)
     * @return ApiResponse&lt;FilterTag&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Filter tag created successfully </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<FilterTag> arliFilterTagsPostWithHttpInfo(FilterTagCreate filterTagCreate) throws ApiException {
        okhttp3.Call localVarCall = arliFilterTagsPostValidateBeforeCall(filterTagCreate, null);
        Type localVarReturnType = new TypeToken<FilterTag>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Create filter tag (asynchronously)
     * Create a new filter tag
     * @param filterTagCreate  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Filter tag created successfully </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call arliFilterTagsPostAsync(FilterTagCreate filterTagCreate, final ApiCallback<FilterTag> _callback) throws ApiException {

        okhttp3.Call localVarCall = arliFilterTagsPostValidateBeforeCall(filterTagCreate, _callback);
        Type localVarReturnType = new TypeToken<FilterTag>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
