import { Router, Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { z } from 'zod';
import { PrismaClient } from '@prisma/client';
import { CustomError, asyncHandler } from '../middleware/errorHandler';
import { getEnv } from '../config/env';

const router = Router();
const prisma = new PrismaClient();
const env = getEnv();

// Validation schemas
const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
});

const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(env.PASSWORD_MIN_LENGTH, `Password must be at least ${env.PASSWORD_MIN_LENGTH} characters`),
  name: z.string().min(1, 'Name is required'),
  role: z.enum(['team_member', 'requirements_analyst']).default('team_member'),
  title: z.string().optional(),
  department: z.string().optional(),
});

const refreshTokenSchema = z.object({
  refreshToken: z.string().min(1, 'Refresh token is required'),
});

// Helper function to generate tokens
const generateTokens = (userId: string, email: string, role: string) => {
  const jwtSecret = env.JWT_SECRET || 'default-jwt-secret-for-development';
  const accessTokenExpiry = env.JWT_EXPIRES_IN || '7d';
  const refreshTokenExpiry = env.JWT_REFRESH_EXPIRES_IN || '30d';

  const accessToken = jwt.sign(
    { userId, email, role },
    jwtSecret,
    { expiresIn: accessTokenExpiry }
  );

  const refreshToken = jwt.sign(
    { userId, email, role, type: 'refresh' },
    jwtSecret,
    { expiresIn: refreshTokenExpiry }
  );

  return { accessToken, refreshToken };
};

/**
 * @route POST /api/auth/login
 * @desc Login user
 * @access Public
 */
router.post('/login', asyncHandler(async (req: Request, res: Response) => {
  const { email, password } = loginSchema.parse(req.body);

  // Find user by email
  const user = await prisma.user.findUnique({
    where: { email: email.toLowerCase() },
    select: {
      id: true,
      email: true,
      password_hash: true,
      name: true,
      role: true,
      title: true,
      department: true,
      avatar_url: true,
      is_active: true,
    },
  });

  if (!user) {
    throw new CustomError('Invalid email or password', 401);
  }

  if (!user.is_active) {
    throw new CustomError('Account is deactivated', 401);
  }

  // Check password
  const isPasswordValid = await bcrypt.compare(password, user.password_hash);
  if (!isPasswordValid) {
    throw new CustomError('Invalid email or password', 401);
  }

  // Generate tokens
  const { accessToken, refreshToken } = generateTokens(user.id, user.email, user.role);

  // Remove password from response
  const { password_hash, ...userWithoutPassword } = user;

  res.status(200).json({
    status: 'success',
    message: 'Login successful',
    data: {
      user: userWithoutPassword,
      accessToken,
      refreshToken,
    },
  });
}));

/**
 * @route POST /api/auth/register
 * @desc Register new user
 * @access Public (in development) / Admin only (in production)
 */
router.post('/register', asyncHandler(async (req: Request, res: Response) => {
  const { email, password, name, role, title, department } = registerSchema.parse(req.body);

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email: email.toLowerCase() },
  });

  if (existingUser) {
    throw new CustomError('User with this email already exists', 409);
  }

  // Hash password
  const hashedPassword = await bcrypt.hash(password, env.BCRYPT_ROUNDS);

  // Create user
  const user = await prisma.user.create({
    data: {
      email: email.toLowerCase(),
      password_hash: hashedPassword,
      name,
      role,
      title: title || null,
      department: department || null,
      is_active: true,
    },
    select: {
      id: true,
      email: true,
      name: true,
      role: true,
      title: true,
      department: true,
      avatar_url: true,
      is_active: true,
      created_at: true,
    },
  });

  // Generate tokens
  const { accessToken, refreshToken } = generateTokens(user.id, user.email, user.role);

  res.status(201).json({
    status: 'success',
    message: 'User registered successfully',
    data: {
      user,
      accessToken,
      refreshToken,
    },
  });
}));

/**
 * @route POST /api/auth/refresh
 * @desc Refresh access token
 * @access Public
 */
router.post('/refresh', asyncHandler(async (req: Request, res: Response) => {
  const { refreshToken } = refreshTokenSchema.parse(req.body);

  try {
    // Verify refresh token
    const decoded = jwt.verify(refreshToken, env.JWT_SECRET) as any;

    if (decoded.type !== 'refresh') {
      throw new CustomError('Invalid refresh token', 401);
    }

    // Check if user still exists and is active
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        is_active: true,
      },
    });

    if (!user || !user.is_active) {
      throw new CustomError('User not found or deactivated', 401);
    }

    // Generate new tokens
    const tokens = generateTokens(user.id, user.email, user.role);

    res.status(200).json({
      status: 'success',
      message: 'Token refreshed successfully',
      data: {
        user,
        ...tokens,
      },
    });
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      throw new CustomError('Invalid refresh token', 401);
    }
    throw error;
  }
}));

/**
 * @route POST /api/auth/logout
 * @desc Logout user (client-side token removal)
 * @access Public
 */
router.post('/logout', (_req: Request, res: Response) => {
  // In a stateless JWT setup, logout is handled client-side
  // In a more complex setup, you might maintain a blacklist of tokens
  res.status(200).json({
    status: 'success',
    message: 'Logout successful',
  });
});

/**
 * @route GET /api/auth/me
 * @desc Get current user profile
 * @access Private
 */
router.get('/me', asyncHandler(async (req: Request, res: Response) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new CustomError('Access token is required', 401);
  }

  const token = authHeader.substring(7);
  const decoded = jwt.verify(token, env.JWT_SECRET) as any;

  const user = await prisma.user.findUnique({
    where: { id: decoded.userId },
    select: {
      id: true,
      email: true,
      name: true,
      role: true,
      title: true,
      department: true,
      avatar_url: true,
      is_active: true,
      weekly_capacity_hours: true,
      current_utilization_percentage: true,
      skills: true,
      created_at: true,
      updated_at: true,
    },
  });

  if (!user || !user.is_active) {
    throw new CustomError('User not found or deactivated', 401);
  }

  res.status(200).json({
    status: 'success',
    data: { user },
  });
}));

export default router;
