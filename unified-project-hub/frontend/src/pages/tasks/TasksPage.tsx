import React, { useState, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { Plus, Grid, List, Kanban, BarChart3, Clock, CheckCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { TaskFilters } from '@/components/tasks/TaskFilters';
import { TaskGridView } from '@/components/tasks/TaskGridView';
import { TaskListView } from '@/components/tasks/TaskListView';
import { TaskKanbanView } from '@/components/tasks/TaskKanbanView';
import { useTasks, useTaskActions } from '@/hooks/useTasks';

interface TasksPageProps {
  filter?: 'my' | 'overdue';
}

export default function TasksPage({ filter }: TasksPageProps) {
  const { user } = useAuth();
  const { toast } = useToast();

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [projectFilter, setProjectFilter] = useState('all');
  const [assigneeFilter, setAssigneeFilter] = useState(filter === 'my' ? user?.id || 'all' : 'all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [effortFilter, setEffortFilter] = useState('all');
  const [showCompleted, setShowCompleted] = useState(true);
  const [showOverdue, setShowOverdue] = useState(filter === 'overdue');
  const [dueDateFrom, setDueDateFrom] = useState('');
  const [dueDateTo, setDueDateTo] = useState('');
  const [sortBy, setSortBy] = useState('updated_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [groupBy, setGroupBy] = useState('none');
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'kanban'>('grid');
  const [currentPage, setCurrentPage] = useState(1);

  // Fetch tasks data
  const { data: tasksData, loading, error, refetch } = useTasks({
    page: currentPage,
    limit: 20,
    status: statusFilter,
    project_id: projectFilter,
    assignee_id: assigneeFilter,
    priority_level: priorityFilter,
    effort_estimate: effortFilter,
    search: searchTerm,
    due_date_from: dueDateFrom,
    due_date_to: dueDateTo,
    is_overdue: showOverdue,
    show_completed: showCompleted,
    sort_by: sortBy,
    sort_order: sortOrder,
    group_by: groupBy,
  });

  const { updateTaskStatus, updateTaskPriority, assignTask, deleteTask } = useTaskActions();

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    setSearchTerm('');
    setStatusFilter('all');
    setProjectFilter('all');
    setAssigneeFilter(filter === 'my' ? user?.id || 'all' : 'all');
    setPriorityFilter('all');
    setEffortFilter('all');
    setShowCompleted(true);
    setShowOverdue(filter === 'overdue');
    setDueDateFrom('');
    setDueDateTo('');
    setSortBy('updated_at');
    setSortOrder('desc');
    setGroupBy('none');
    setCurrentPage(1);
  }, [filter, user?.id]);

  // Task actions
  const handleEditTask = useCallback((task: any) => {
    // TODO: Open edit dialog or navigate to edit page
    console.log('Edit task:', task);
  }, []);

  const handleDeleteTask = useCallback(async (task: any) => {
    if (!confirm(`Are you sure you want to delete "${task.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteTask(task.id);

      toast({
        title: 'Success',
        description: 'Task deleted successfully',
      });

      refetch();
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete task',
        variant: 'destructive',
      });
    }
  }, [deleteTask, refetch, toast]);

  const handleAssignTask = useCallback(async (task: any) => {
    // TODO: Open assignment dialog
    console.log('Assign task:', task);
  }, []);

  const handleUpdateStatus = useCallback(async (task: any, status: string) => {
    try {
      await updateTaskStatus(task.id, status);

      toast({
        title: 'Success',
        description: `Task status updated to ${status}`,
      });

      refetch();
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update task status',
        variant: 'destructive',
      });
    }
  }, [updateTaskStatus, refetch, toast]);

  const handleUpdatePriority = useCallback(async (task: any, priority: string) => {
    try {
      await updateTaskPriority(task.id, priority);

      toast({
        title: 'Success',
        description: `Task priority updated to ${priority}`,
      });

      refetch();
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update task priority',
        variant: 'destructive',
      });
    }
  }, [updateTaskPriority, refetch, toast]);

  const getPageTitle = () => {
    switch (filter) {
      case 'my':
        return 'My Tasks';
      case 'overdue':
        return 'Overdue Tasks';
      default:
        return 'All Tasks';
    }
  };

  const getPageDescription = () => {
    switch (filter) {
      case 'my':
        return 'Tasks assigned to you across all projects';
      case 'overdue':
        return 'Tasks that are past their due date';
      default:
        return 'Manage and track tasks across all projects';
    }
  };

  const canCreateTask = user?.role === 'admin' || user?.role === 'project_manager' || user?.role === 'team_member';

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading tasks: {error}</p>
          <Button onClick={refetch}>Try Again</Button>
        </div>
      </div>
    );
  }

  const tasks = tasksData?.tasks || [];
  const groupedTasks = tasksData?.grouped_tasks;
  const pagination = tasksData?.pagination;

  // Calculate stats
  const totalTasks = tasks.length;
  const completedTasks = tasks.filter(task => task.status === 'done').length;
  const overdueTasks = tasks.filter(task => task.is_overdue && task.status !== 'done').length;
  const inProgressTasks = tasks.filter(task => task.status === 'in_progress').length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{getPageTitle()}</h1>
          <p className="text-gray-600 mt-1">{getPageDescription()}</p>
          <div className="flex gap-4 mt-2 text-sm text-muted-foreground">
            <span className="flex items-center gap-1">
              <BarChart3 className="h-3 w-3" />
              {totalTasks} total
            </span>
            <span className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3" />
              {completedTasks} completed
            </span>
            <span className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {inProgressTasks} in progress
            </span>
            {overdueTasks > 0 && (
              <span className="text-red-600 flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {overdueTasks} overdue
              </span>
            )}
          </div>
        </div>
        {canCreateTask && (
          <Button asChild>
            <Link to="/tasks/new">
              <Plus className="mr-2 h-4 w-4" />
              New Task
            </Link>
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <TaskFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            statusFilter={statusFilter}
            onStatusFilterChange={setStatusFilter}
            projectFilter={projectFilter}
            onProjectFilterChange={setProjectFilter}
            assigneeFilter={assigneeFilter}
            onAssigneeFilterChange={setAssigneeFilter}
            priorityFilter={priorityFilter}
            onPriorityFilterChange={setPriorityFilter}
            effortFilter={effortFilter}
            onEffortFilterChange={setEffortFilter}
            showCompleted={showCompleted}
            onShowCompletedChange={setShowCompleted}
            showOverdue={showOverdue}
            onShowOverdueChange={setShowOverdue}
            dueDateFrom={dueDateFrom}
            onDueDateFromChange={setDueDateFrom}
            dueDateTo={dueDateTo}
            onDueDateToChange={setDueDateTo}
            sortBy={sortBy}
            onSortByChange={setSortBy}
            sortOrder={sortOrder}
            onSortOrderChange={setSortOrder}
            groupBy={groupBy}
            onGroupByChange={setGroupBy}
            onClearFilters={handleClearFilters}
            overdueTasksCount={overdueTasks}
            projects={[]} // TODO: Fetch projects
            teamMembers={[]} // TODO: Fetch team members
          />
        </CardContent>
      </Card>

      {/* View Mode Tabs */}
      <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as any)}>
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="grid" className="flex items-center gap-2">
              <Grid className="h-4 w-4" />
              Grid
            </TabsTrigger>
            <TabsTrigger value="list" className="flex items-center gap-2">
              <List className="h-4 w-4" />
              List
            </TabsTrigger>
            <TabsTrigger value="kanban" className="flex items-center gap-2">
              <Kanban className="h-4 w-4" />
              Kanban
            </TabsTrigger>
          </TabsList>

          {pagination && (
            <div className="text-sm text-muted-foreground">
              Showing {tasks.length} of {pagination.total} tasks
            </div>
          )}
        </div>

        <TabsContent value="grid" className="mt-6">
          <TaskGridView
            tasks={tasks}
            groupedTasks={groupedTasks}
            groupBy={groupBy}
            onEdit={handleEditTask}
            onDelete={handleDeleteTask}
            onAssign={handleAssignTask}
            onUpdateStatus={handleUpdateStatus}
            onUpdatePriority={handleUpdatePriority}
          />
        </TabsContent>

        <TabsContent value="list" className="mt-6">
          <TaskListView
            tasks={tasks}
            onEdit={handleEditTask}
            onDelete={handleDeleteTask}
            onAssign={handleAssignTask}
            onUpdateStatus={handleUpdateStatus}
            onUpdatePriority={handleUpdatePriority}
          />
        </TabsContent>

        <TabsContent value="kanban" className="mt-6">
          <TaskKanbanView
            tasks={tasks}
            onEdit={handleEditTask}
            onDelete={handleDeleteTask}
            onAssign={handleAssignTask}
            onUpdateStatus={handleUpdateStatus}
            onUpdatePriority={handleUpdatePriority}
          />
        </TabsContent>
      </Tabs>

      {/* Empty State */}
      {tasks.length === 0 && !loading && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">No tasks found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || statusFilter !== 'all' || projectFilter !== 'all'
                  ? 'Try adjusting your filters to see more tasks.'
                  : 'Get started by creating your first task.'
                }
              </p>
              {canCreateTask && !searchTerm && statusFilter === 'all' && projectFilter === 'all' && (
                <Button asChild>
                  <Link to="/tasks/new">
                    <Plus className="mr-2 h-4 w-4" />
                    Create Task
                  </Link>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
