# coding: utf-8

"""
    CRM API

    Complete API specification for the CRM system

    The version of the OpenAPI document: 1.0.0
    Contact: <EMAIL>
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from crm_api_client.models.auth_response import AuthResponse

class TestAuthResponse(unittest.TestCase):
    """AuthResponse unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> AuthResponse:
        """Test AuthResponse
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `AuthResponse`
        """
        model = AuthResponse()
        if include_optional:
            return AuthResponse(
                access_token = '',
                refresh_token = '',
                expires_in = 56,
                token_type = '',
                user = crm_api_client.models.user.User(
                    id = '', 
                    email = '', 
                    email_confirmed_at = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'), 
                    created_at = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'), 
                    updated_at = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f'), )
            )
        else:
            return AuthResponse(
        )
        """

    def testAuthResponse(self):
        """Test AuthResponse"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
