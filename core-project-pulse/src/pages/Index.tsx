import { useState, useMemo } from "react";
import { ProjectCard } from "@/components/projects/ProjectCard";
import { ProjectListItem } from "@/components/projects/ProjectListItem";
import { DraggableProjectCard } from "@/components/projects/DraggableProjectCard";
import { ProjectFilters } from "@/components/projects/ProjectFilters";
import { BulkArchiveButton } from "@/components/projects/BulkArchiveButton";
import { KanbanBoard } from "@/components/projects/KanbanBoard";
import { useProjects, useReorderProjects } from "@/hooks/useProjects";
import { useUpdateProjectStatus } from "@/hooks/useUpdateProjectStatus";
import { Loader2, AlertCircle, Grid3X3, List, LayoutGrid, ChevronDown, Trophy, ChevronUp } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
const Index = () => {
  const {
    data: projects,
    isLoading,
    error
  } = useProjects();
  const reorderProjects = useReorderProjects();
  const updateProjectStatus = useUpdateProjectStatus();
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [leadFilter, setLeadFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");
  const [groupBy, setGroupBy] = useState<"none" | "start_date" | "end_date" | "status" | "lead">("none");
  const [showArchivedProjects, setShowArchivedProjects] = useState(false);
  // Default to list view as primary viewing mode, grid as secondary
  const [viewMode, setViewMode] = useState<"grid" | "list">("list");
  
  // Sorting state - default sort by end_date ascending
  const [sortBy, setSortBy] = useState<"name" | "project_lead" | "customer_name" | "start_date" | "end_date" | "status" | "priority_level">("priority_level");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const filteredProjects = useMemo(() => {
    if (!projects) return [];
    
    const filtered = projects.filter(project => {
      const matchesSearch = !searchTerm || 
        project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.customer_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.company_name?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesType = typeFilter === "all" || project.type === typeFilter;
      const matchesStatus = statusFilter === "all" || project.status === statusFilter;
      const matchesLead = leadFilter === "all" || project.project_lead === leadFilter;
      const matchesPriority = priorityFilter === "all" || project.priority_level === priorityFilter;
      const matchesArchived = showArchivedProjects ? project.status === 'archived' : project.status !== 'archived';
      
      return matchesSearch && matchesType && matchesStatus && matchesLead && matchesPriority && matchesArchived;
    });

    // Apply sorting
    return filtered.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortBy) {
        case "name":
          aValue = a.name?.toLowerCase() || "";
          bValue = b.name?.toLowerCase() || "";
          break;
        case "project_lead":
          aValue = a.project_lead?.toLowerCase() || "";
          bValue = b.project_lead?.toLowerCase() || "";
          break;
        case "customer_name":
          aValue = a.customer_name?.toLowerCase() || "";
          bValue = b.customer_name?.toLowerCase() || "";
          break;
        case "start_date":
          aValue = a.start_date ? new Date(a.start_date).getTime() : 0;
          bValue = b.start_date ? new Date(b.start_date).getTime() : 0;
          break;
        case "end_date":
          aValue = a.end_date ? new Date(a.end_date).getTime() : 0;
          bValue = b.end_date ? new Date(b.end_date).getTime() : 0;
          break;
        case "status":
          aValue = a.status?.toLowerCase() || "";
          bValue = b.status?.toLowerCase() || "";
          break;
        case "priority_level":
          const priorityOrder = { 'P0': 0, 'P1': 1, 'P2': 2, 'P3': 3, 'P4': 4 };
          const aPriority = priorityOrder[a.priority_level || 'P3'];
          const bPriority = priorityOrder[b.priority_level || 'P3'];
          
          // If priorities are different, sort by priority
          if (aPriority !== bPriority) {
            aValue = aPriority;
            bValue = bPriority;
          } else {
            // If priorities are the same, sort by days in status as secondary criteria
            const aDays = a.status_changed_at ? Math.floor((new Date().getTime() - new Date(a.status_changed_at).getTime()) / (1000 * 60 * 60 * 24)) : 0;
            const bDays = b.status_changed_at ? Math.floor((new Date().getTime() - new Date(b.status_changed_at).getTime()) / (1000 * 60 * 60 * 24)) : 0;
            aValue = aDays;
            bValue = bDays;
          }
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
      if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
      return 0;
    });
  }, [projects, searchTerm, typeFilter, statusFilter, leadFilter, priorityFilter, showArchivedProjects, sortBy, sortDirection]);
  const groupedProjects = useMemo(() => {
    if (groupBy === "none") {
      return {
        "All Projects": filteredProjects
      };
    }
    const groups: Record<string, typeof filteredProjects> = {};
    filteredProjects.forEach(project => {
      let groupKey: string;
      switch (groupBy) {
        case "status":
          groupKey = project.status.charAt(0).toUpperCase() + project.status.slice(1).replace('-', ' ');
          break;
        case "start_date":
          if (!project.start_date) {
            groupKey = "No Start Date";
          } else {
            const date = new Date(project.start_date);
            groupKey = date.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long'
            });
          }
          break;
        case "end_date":
          if (!project.end_date) {
            groupKey = "No End Date";
          } else {
            const date = new Date(project.end_date);
            groupKey = date.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long'
            });
          }
          break;
        case "lead":
          groupKey = project.project_lead || "No Lead";
          break;
        default:
          groupKey = "All Projects";
      }
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(project);
    });
    return groups;
  }, [filteredProjects, groupBy]);
  const clearFilters = () => {
    setSearchTerm("");
    setTypeFilter("all");
    setStatusFilter("all");
    setLeadFilter("all");
    setPriorityFilter("all");
    setGroupBy("none");
    setShowArchivedProjects(false);
    setSortBy("priority_level");
    setSortDirection("asc");
  };

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new field with ascending direction
      setSortBy(field);
      setSortDirection("asc");
    }
  };

  const getSortIcon = (field: typeof sortBy) => {
    if (sortBy !== field) return null;
    return sortDirection === "asc" ? 
      <ChevronUp className="w-3 h-3 inline ml-1" /> : 
      <ChevronDown className="w-3 h-3 inline ml-1" />;
  };

  // Get completed projects for bulk archive and archived count
  const completedProjects = useMemo(() => {
    return projects?.filter(project => project.status === 'completed') || [];
  }, [projects]);

  const archivedProjectsCount = useMemo(() => {
    return projects?.filter(project => project.status === 'archived').length || 0;
  }, [projects]);

  const handleStatusChange = async (projectId: string, newStatus: string) => {
    return updateProjectStatus.mutateAsync({ projectId, status: newStatus });
  };
  if (isLoading) {
    return <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>;
  }
  if (error) {
    return <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load projects. Please try again later.
        </AlertDescription>
      </Alert>;
  }
  return <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Project Dashboard</h1>
          <p className="text-muted-foreground">
            Manage and track all your projects in one place
          </p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex border rounded-lg p-1 bg-muted/50">
            <Button variant={viewMode === "grid" ? "default" : "ghost"} size="sm" onClick={() => setViewMode("grid")} className="px-3">
              <LayoutGrid className="w-4 h-4" />
            </Button>
            <Button variant={viewMode === "list" ? "default" : "ghost"} size="sm" onClick={() => setViewMode("list")} className="px-3">
              <List className="w-4 h-4" />
            </Button>
          </div>
          <div className="text-sm text-muted-foreground">
            {filteredProjects.length} project{filteredProjects.length !== 1 ? 's' : ''} found
          </div>
        </div>
      </div>

      <ProjectFilters 
        searchTerm={searchTerm} 
        onSearchChange={setSearchTerm} 
        typeFilter={typeFilter} 
        onTypeFilterChange={setTypeFilter} 
        statusFilter={statusFilter} 
        onStatusFilterChange={setStatusFilter} 
        leadFilter={leadFilter} 
        onLeadFilterChange={setLeadFilter} 
        priorityFilter={priorityFilter} 
        onPriorityFilterChange={setPriorityFilter} 
        groupBy={groupBy} 
        onGroupByChange={setGroupBy} 
        showArchivedProjects={showArchivedProjects}
        onShowArchivedProjectsChange={setShowArchivedProjects}
        onClearFilters={clearFilters} 
        archivedProjectsCount={archivedProjectsCount}
      />

      <BulkArchiveButton 
        completedProjects={completedProjects} 
        className="mb-4"
      />

      {filteredProjects.length === 0 ? <div className="text-center py-12">
          <p className="text-muted-foreground mb-4">
            {projects?.length === 0 ? "No projects created yet." : "No projects match your filters."}
          </p>
          {projects?.length === 0 && <p className="text-sm text-muted-foreground">
              Get started by creating your first project.
            </p>}
        </div> : <div className="space-y-6">
          {Object.entries(groupedProjects).map(([groupName, groupProjects]) => <div key={groupName} className="space-y-4">
              {groupBy !== "none" && <div className="border-b pb-2">
                  <h2 className="text-lg font-semibold text-foreground">{groupName}</h2>
                  <p className="text-sm text-muted-foreground">
                    {groupProjects.length} project{groupProjects.length !== 1 ? 's' : ''}
                  </p>
                </div>}
              
              {viewMode === "grid" ? (
                <KanbanBoard 
                  projects={groupProjects}
                  onReorder={(projectIds, newOrders) => reorderProjects.mutate({ projectIds, newOrders })}
                  onStatusChange={handleStatusChange}
                />
              ) : (
                <div className="space-y-3">
                  {groupBy === "none" && <>
                      
                      <div className="grid grid-cols-[2fr_0.8fr_0.8fr_1fr_1fr_1fr_1fr] gap-4 items-center px-4 py-2 text-sm font-medium text-muted-foreground border-b">
                        <button 
                          onClick={() => handleSort("name")}
                          className="text-left hover:text-foreground transition-colors cursor-pointer flex items-center"
                        >
                          Project{getSortIcon("name")}
                        </button>
                        <button 
                          onClick={() => handleSort("project_lead")}
                          className="text-left hover:text-foreground transition-colors cursor-pointer flex items-center"
                        >
                          Lead{getSortIcon("project_lead")}
                        </button>
                        <button 
                          onClick={() => handleSort("customer_name")}
                          className="text-left hover:text-foreground transition-colors cursor-pointer flex items-center"
                        >
                          Company{getSortIcon("customer_name")}
                        </button>
                        <button 
                          onClick={() => handleSort("start_date")}
                          className="text-left hover:text-foreground transition-colors cursor-pointer flex items-center"
                        >
                          Start Date{getSortIcon("start_date")}
                        </button>
                        <button 
                          onClick={() => handleSort("end_date")}
                          className="text-left hover:text-foreground transition-colors cursor-pointer flex items-center"
                        >
                          End Date{getSortIcon("end_date")}
                        </button>
                        <button 
                          onClick={() => handleSort("priority_level")}
                          className="text-left hover:text-foreground transition-colors cursor-pointer flex items-center"
                        >
                          Priority{getSortIcon("priority_level")}
                        </button>
                        <button 
                          onClick={() => handleSort("status")}
                          className="text-right hover:text-foreground transition-colors cursor-pointer flex items-center justify-end"
                        >
                          Status{getSortIcon("status")}
                        </button>
                      </div>
                    </>}
                  {groupProjects.map(project => <ProjectListItem key={project.id} project={project} tasks={project.tasks || []} />)}
                </div>
              )}
            </div>)}
        </div>}
    </div>;
};
export default Index;