import React from 'react';
import { TaskCard } from './TaskCard';
import { Badge } from '@/components/ui/badge';

interface TaskKanbanViewProps {
  tasks: any[];
  onEdit?: (task: any) => void;
  onDelete?: (task: any) => void;
  onAssign?: (task: any) => void;
  onUpdateStatus?: (task: any, status: string) => void;
  onUpdatePriority?: (task: any, priority: string) => void;
  showActions?: boolean;
  showProject?: boolean;
}

const KANBAN_COLUMNS = [
  { 
    id: 'to_do', 
    title: 'To Do', 
    statuses: ['to_do'],
    color: 'bg-gray-100'
  },
  { 
    id: 'in_progress', 
    title: 'In Progress', 
    statuses: ['in_progress'],
    color: 'bg-blue-100'
  },
  { 
    id: 'in_review', 
    title: 'In Review', 
    statuses: ['in_review'],
    color: 'bg-yellow-100'
  },
  { 
    id: 'done', 
    title: 'Done', 
    statuses: ['done'],
    color: 'bg-green-100'
  },
  { 
    id: 'blocked', 
    title: 'Blocked', 
    statuses: ['blocked'],
    color: 'bg-red-100'
  }
];

export function TaskKanbanView({
  tasks,
  onEdit,
  onDelete,
  onAssign,
  onUpdateStatus,
  onUpdatePriority,
  showActions = true,
  showProject = true
}: TaskKanbanViewProps) {
  // Group tasks by status
  const groupedTasks = KANBAN_COLUMNS.reduce((acc, column) => {
    acc[column.id] = tasks.filter(task => 
      column.statuses.includes(task.status)
    );
    return acc;
  }, {} as Record<string, any[]>);

  return (
    <div className="flex gap-6 overflow-x-auto pb-4">
      {KANBAN_COLUMNS.map((column) => {
        const columnTasks = groupedTasks[column.id] || [];
        
        return (
          <div key={column.id} className="flex-shrink-0 w-80">
            <div className={`rounded-lg p-4 ${column.color}`}>
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900">{column.title}</h3>
                <Badge variant="secondary" className="bg-white">
                  {columnTasks.length}
                </Badge>
              </div>
              
              <div className="space-y-4 max-h-[calc(100vh-300px)] overflow-y-auto">
                {columnTasks.map((task) => (
                  <div key={task.id} className="bg-white rounded-lg shadow-sm">
                    <TaskCard
                      task={task}
                      onEdit={onEdit}
                      onDelete={onDelete}
                      onAssign={onAssign}
                      onUpdateStatus={onUpdateStatus}
                      onUpdatePriority={onUpdatePriority}
                      showActions={showActions}
                      showProject={showProject}
                    />
                  </div>
                ))}
                
                {columnTasks.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <p className="text-sm">No tasks in {column.title.toLowerCase()}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
