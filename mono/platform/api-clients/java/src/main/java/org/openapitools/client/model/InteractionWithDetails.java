/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import java.util.Objects;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.UUID;
import org.openapitools.client.model.InteractionWithDetailsAllOfCompany;
import org.openapitools.client.model.InteractionWithDetailsAllOfContact;
import org.openapitools.jackson.nullable.JsonNullable;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.openapitools.client.JSON;

/**
 * InteractionWithDetails
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-07-14T08:13:23.820009+10:00[Australia/Brisbane]", comments = "Generator version: 7.9.0")
public class InteractionWithDetails {
  public static final String SERIALIZED_NAME_ID = "id";
  @SerializedName(SERIALIZED_NAME_ID)
  private UUID id;

  public static final String SERIALIZED_NAME_COMPANY_ID = "company_id";
  @SerializedName(SERIALIZED_NAME_COMPANY_ID)
  private UUID companyId;

  public static final String SERIALIZED_NAME_CONTACT_ID = "contact_id";
  @SerializedName(SERIALIZED_NAME_CONTACT_ID)
  private UUID contactId;

  /**
   * Gets or Sets interactionType
   */
  @JsonAdapter(InteractionTypeEnum.Adapter.class)
  public enum InteractionTypeEnum {
    EMAIL("email"),
    
    PHONE("phone"),
    
    MEETING("meeting"),
    
    DEMO("demo"),
    
    PROPOSAL("proposal"),
    
    FOLLOW_UP("follow-up"),
    
    OTHER("other");

    private String value;

    InteractionTypeEnum(String value) {
      this.value = value;
    }

    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    public static InteractionTypeEnum fromValue(String value) {
      for (InteractionTypeEnum b : InteractionTypeEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }

    public static class Adapter extends TypeAdapter<InteractionTypeEnum> {
      @Override
      public void write(final JsonWriter jsonWriter, final InteractionTypeEnum enumeration) throws IOException {
        jsonWriter.value(enumeration.getValue());
      }

      @Override
      public InteractionTypeEnum read(final JsonReader jsonReader) throws IOException {
        String value =  jsonReader.nextString();
        return InteractionTypeEnum.fromValue(value);
      }
    }

    public static void validateJsonElement(JsonElement jsonElement) throws IOException {
      String value = jsonElement.getAsString();
      InteractionTypeEnum.fromValue(value);
    }
  }

  public static final String SERIALIZED_NAME_INTERACTION_TYPE = "interaction_type";
  @SerializedName(SERIALIZED_NAME_INTERACTION_TYPE)
  private InteractionTypeEnum interactionType;

  public static final String SERIALIZED_NAME_NOTES = "notes";
  @SerializedName(SERIALIZED_NAME_NOTES)
  private String notes;

  public static final String SERIALIZED_NAME_INTERACTION_DATETIME = "interaction_datetime";
  @SerializedName(SERIALIZED_NAME_INTERACTION_DATETIME)
  private OffsetDateTime interactionDatetime;

  public static final String SERIALIZED_NAME_CREATED_BY = "created_by";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private UUID createdBy;

  public static final String SERIALIZED_NAME_CREATED_AT = "created_at";
  @SerializedName(SERIALIZED_NAME_CREATED_AT)
  private OffsetDateTime createdAt;

  public static final String SERIALIZED_NAME_UPDATED_AT = "updated_at";
  @SerializedName(SERIALIZED_NAME_UPDATED_AT)
  private OffsetDateTime updatedAt;

  public static final String SERIALIZED_NAME_COMPANY = "company";
  @SerializedName(SERIALIZED_NAME_COMPANY)
  private InteractionWithDetailsAllOfCompany company;

  public static final String SERIALIZED_NAME_CONTACT = "contact";
  @SerializedName(SERIALIZED_NAME_CONTACT)
  private InteractionWithDetailsAllOfContact contact;

  public InteractionWithDetails() {
  }

  public InteractionWithDetails id(UUID id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
   */
  @javax.annotation.Nullable
  public UUID getId() {
    return id;
  }

  public void setId(UUID id) {
    this.id = id;
  }


  public InteractionWithDetails companyId(UUID companyId) {
    this.companyId = companyId;
    return this;
  }

  /**
   * Get companyId
   * @return companyId
   */
  @javax.annotation.Nullable
  public UUID getCompanyId() {
    return companyId;
  }

  public void setCompanyId(UUID companyId) {
    this.companyId = companyId;
  }


  public InteractionWithDetails contactId(UUID contactId) {
    this.contactId = contactId;
    return this;
  }

  /**
   * Get contactId
   * @return contactId
   */
  @javax.annotation.Nullable
  public UUID getContactId() {
    return contactId;
  }

  public void setContactId(UUID contactId) {
    this.contactId = contactId;
  }


  public InteractionWithDetails interactionType(InteractionTypeEnum interactionType) {
    this.interactionType = interactionType;
    return this;
  }

  /**
   * Get interactionType
   * @return interactionType
   */
  @javax.annotation.Nullable
  public InteractionTypeEnum getInteractionType() {
    return interactionType;
  }

  public void setInteractionType(InteractionTypeEnum interactionType) {
    this.interactionType = interactionType;
  }


  public InteractionWithDetails notes(String notes) {
    this.notes = notes;
    return this;
  }

  /**
   * Get notes
   * @return notes
   */
  @javax.annotation.Nullable
  public String getNotes() {
    return notes;
  }

  public void setNotes(String notes) {
    this.notes = notes;
  }


  public InteractionWithDetails interactionDatetime(OffsetDateTime interactionDatetime) {
    this.interactionDatetime = interactionDatetime;
    return this;
  }

  /**
   * Get interactionDatetime
   * @return interactionDatetime
   */
  @javax.annotation.Nullable
  public OffsetDateTime getInteractionDatetime() {
    return interactionDatetime;
  }

  public void setInteractionDatetime(OffsetDateTime interactionDatetime) {
    this.interactionDatetime = interactionDatetime;
  }


  public InteractionWithDetails createdBy(UUID createdBy) {
    this.createdBy = createdBy;
    return this;
  }

  /**
   * Get createdBy
   * @return createdBy
   */
  @javax.annotation.Nullable
  public UUID getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(UUID createdBy) {
    this.createdBy = createdBy;
  }


  public InteractionWithDetails createdAt(OffsetDateTime createdAt) {
    this.createdAt = createdAt;
    return this;
  }

  /**
   * Get createdAt
   * @return createdAt
   */
  @javax.annotation.Nullable
  public OffsetDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(OffsetDateTime createdAt) {
    this.createdAt = createdAt;
  }


  public InteractionWithDetails updatedAt(OffsetDateTime updatedAt) {
    this.updatedAt = updatedAt;
    return this;
  }

  /**
   * Get updatedAt
   * @return updatedAt
   */
  @javax.annotation.Nullable
  public OffsetDateTime getUpdatedAt() {
    return updatedAt;
  }

  public void setUpdatedAt(OffsetDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }


  public InteractionWithDetails company(InteractionWithDetailsAllOfCompany company) {
    this.company = company;
    return this;
  }

  /**
   * Get company
   * @return company
   */
  @javax.annotation.Nullable
  public InteractionWithDetailsAllOfCompany getCompany() {
    return company;
  }

  public void setCompany(InteractionWithDetailsAllOfCompany company) {
    this.company = company;
  }


  public InteractionWithDetails contact(InteractionWithDetailsAllOfContact contact) {
    this.contact = contact;
    return this;
  }

  /**
   * Get contact
   * @return contact
   */
  @javax.annotation.Nullable
  public InteractionWithDetailsAllOfContact getContact() {
    return contact;
  }

  public void setContact(InteractionWithDetailsAllOfContact contact) {
    this.contact = contact;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InteractionWithDetails interactionWithDetails = (InteractionWithDetails) o;
    return Objects.equals(this.id, interactionWithDetails.id) &&
        Objects.equals(this.companyId, interactionWithDetails.companyId) &&
        Objects.equals(this.contactId, interactionWithDetails.contactId) &&
        Objects.equals(this.interactionType, interactionWithDetails.interactionType) &&
        Objects.equals(this.notes, interactionWithDetails.notes) &&
        Objects.equals(this.interactionDatetime, interactionWithDetails.interactionDatetime) &&
        Objects.equals(this.createdBy, interactionWithDetails.createdBy) &&
        Objects.equals(this.createdAt, interactionWithDetails.createdAt) &&
        Objects.equals(this.updatedAt, interactionWithDetails.updatedAt) &&
        Objects.equals(this.company, interactionWithDetails.company) &&
        Objects.equals(this.contact, interactionWithDetails.contact);
  }

  private static <T> boolean equalsNullable(JsonNullable<T> a, JsonNullable<T> b) {
    return a == b || (a != null && b != null && a.isPresent() && b.isPresent() && Objects.deepEquals(a.get(), b.get()));
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, companyId, contactId, interactionType, notes, interactionDatetime, createdBy, createdAt, updatedAt, company, contact);
  }

  private static <T> int hashCodeNullable(JsonNullable<T> a) {
    if (a == null) {
      return 1;
    }
    return a.isPresent() ? Arrays.deepHashCode(new Object[]{a.get()}) : 31;
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InteractionWithDetails {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    companyId: ").append(toIndentedString(companyId)).append("\n");
    sb.append("    contactId: ").append(toIndentedString(contactId)).append("\n");
    sb.append("    interactionType: ").append(toIndentedString(interactionType)).append("\n");
    sb.append("    notes: ").append(toIndentedString(notes)).append("\n");
    sb.append("    interactionDatetime: ").append(toIndentedString(interactionDatetime)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdAt: ").append(toIndentedString(createdAt)).append("\n");
    sb.append("    updatedAt: ").append(toIndentedString(updatedAt)).append("\n");
    sb.append("    company: ").append(toIndentedString(company)).append("\n");
    sb.append("    contact: ").append(toIndentedString(contact)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("id");
    openapiFields.add("company_id");
    openapiFields.add("contact_id");
    openapiFields.add("interaction_type");
    openapiFields.add("notes");
    openapiFields.add("interaction_datetime");
    openapiFields.add("created_by");
    openapiFields.add("created_at");
    openapiFields.add("updated_at");
    openapiFields.add("company");
    openapiFields.add("contact");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

  /**
   * Validates the JSON Element and throws an exception if issues found
   *
   * @param jsonElement JSON Element
   * @throws IOException if the JSON Element is invalid with respect to InteractionWithDetails
   */
  public static void validateJsonElement(JsonElement jsonElement) throws IOException {
      if (jsonElement == null) {
        if (!InteractionWithDetails.openapiRequiredFields.isEmpty()) { // has required fields but JSON element is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in InteractionWithDetails is not found in the empty JSON string", InteractionWithDetails.openapiRequiredFields.toString()));
        }
      }

      Set<Map.Entry<String, JsonElement>> entries = jsonElement.getAsJsonObject().entrySet();
      // check to see if the JSON string contains additional fields
      for (Map.Entry<String, JsonElement> entry : entries) {
        if (!InteractionWithDetails.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `InteractionWithDetails` properties. JSON: %s", entry.getKey(), jsonElement.toString()));
        }
      }
        JsonObject jsonObj = jsonElement.getAsJsonObject();
      if ((jsonObj.get("id") != null && !jsonObj.get("id").isJsonNull()) && !jsonObj.get("id").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `id` to be a primitive type in the JSON string but got `%s`", jsonObj.get("id").toString()));
      }
      if ((jsonObj.get("company_id") != null && !jsonObj.get("company_id").isJsonNull()) && !jsonObj.get("company_id").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `company_id` to be a primitive type in the JSON string but got `%s`", jsonObj.get("company_id").toString()));
      }
      if ((jsonObj.get("contact_id") != null && !jsonObj.get("contact_id").isJsonNull()) && !jsonObj.get("contact_id").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `contact_id` to be a primitive type in the JSON string but got `%s`", jsonObj.get("contact_id").toString()));
      }
      if ((jsonObj.get("interaction_type") != null && !jsonObj.get("interaction_type").isJsonNull()) && !jsonObj.get("interaction_type").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `interaction_type` to be a primitive type in the JSON string but got `%s`", jsonObj.get("interaction_type").toString()));
      }
      // validate the optional field `interaction_type`
      if (jsonObj.get("interaction_type") != null && !jsonObj.get("interaction_type").isJsonNull()) {
        InteractionTypeEnum.validateJsonElement(jsonObj.get("interaction_type"));
      }
      if ((jsonObj.get("notes") != null && !jsonObj.get("notes").isJsonNull()) && !jsonObj.get("notes").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `notes` to be a primitive type in the JSON string but got `%s`", jsonObj.get("notes").toString()));
      }
      if ((jsonObj.get("created_by") != null && !jsonObj.get("created_by").isJsonNull()) && !jsonObj.get("created_by").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `created_by` to be a primitive type in the JSON string but got `%s`", jsonObj.get("created_by").toString()));
      }
      // validate the optional field `company`
      if (jsonObj.get("company") != null && !jsonObj.get("company").isJsonNull()) {
        InteractionWithDetailsAllOfCompany.validateJsonElement(jsonObj.get("company"));
      }
      // validate the optional field `contact`
      if (jsonObj.get("contact") != null && !jsonObj.get("contact").isJsonNull()) {
        InteractionWithDetailsAllOfContact.validateJsonElement(jsonObj.get("contact"));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!InteractionWithDetails.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'InteractionWithDetails' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<InteractionWithDetails> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(InteractionWithDetails.class));

       return (TypeAdapter<T>) new TypeAdapter<InteractionWithDetails>() {
           @Override
           public void write(JsonWriter out, InteractionWithDetails value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public InteractionWithDetails read(JsonReader in) throws IOException {
             JsonElement jsonElement = elementAdapter.read(in);
             validateJsonElement(jsonElement);
             return thisAdapter.fromJsonTree(jsonElement);
           }

       }.nullSafe();
    }
  }

  /**
   * Create an instance of InteractionWithDetails given an JSON string
   *
   * @param jsonString JSON string
   * @return An instance of InteractionWithDetails
   * @throws IOException if the JSON string is invalid with respect to InteractionWithDetails
   */
  public static InteractionWithDetails fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, InteractionWithDetails.class);
  }

  /**
   * Convert an instance of InteractionWithDetails to an JSON string
   *
   * @return JSON string
   */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

