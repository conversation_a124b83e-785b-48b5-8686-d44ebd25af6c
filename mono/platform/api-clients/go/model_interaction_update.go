/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"time"
)

// checks if the InteractionUpdate type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &InteractionUpdate{}

// InteractionUpdate struct for InteractionUpdate
type InteractionUpdate struct {
	InteractionType *string `json:"interaction_type,omitempty"`
	Notes *string `json:"notes,omitempty"`
	InteractionDatetime *time.Time `json:"interaction_datetime,omitempty"`
	CompanyId *string `json:"company_id,omitempty"`
	ContactId *string `json:"contact_id,omitempty"`
}

// NewInteractionUpdate instantiates a new InteractionUpdate object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewInteractionUpdate() *InteractionUpdate {
	this := InteractionUpdate{}
	return &this
}

// NewInteractionUpdateWithDefaults instantiates a new InteractionUpdate object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewInteractionUpdateWithDefaults() *InteractionUpdate {
	this := InteractionUpdate{}
	return &this
}

// GetInteractionType returns the InteractionType field value if set, zero value otherwise.
func (o *InteractionUpdate) GetInteractionType() string {
	if o == nil || IsNil(o.InteractionType) {
		var ret string
		return ret
	}
	return *o.InteractionType
}

// GetInteractionTypeOk returns a tuple with the InteractionType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionUpdate) GetInteractionTypeOk() (*string, bool) {
	if o == nil || IsNil(o.InteractionType) {
		return nil, false
	}
	return o.InteractionType, true
}

// HasInteractionType returns a boolean if a field has been set.
func (o *InteractionUpdate) HasInteractionType() bool {
	if o != nil && !IsNil(o.InteractionType) {
		return true
	}

	return false
}

// SetInteractionType gets a reference to the given string and assigns it to the InteractionType field.
func (o *InteractionUpdate) SetInteractionType(v string) {
	o.InteractionType = &v
}

// GetNotes returns the Notes field value if set, zero value otherwise.
func (o *InteractionUpdate) GetNotes() string {
	if o == nil || IsNil(o.Notes) {
		var ret string
		return ret
	}
	return *o.Notes
}

// GetNotesOk returns a tuple with the Notes field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionUpdate) GetNotesOk() (*string, bool) {
	if o == nil || IsNil(o.Notes) {
		return nil, false
	}
	return o.Notes, true
}

// HasNotes returns a boolean if a field has been set.
func (o *InteractionUpdate) HasNotes() bool {
	if o != nil && !IsNil(o.Notes) {
		return true
	}

	return false
}

// SetNotes gets a reference to the given string and assigns it to the Notes field.
func (o *InteractionUpdate) SetNotes(v string) {
	o.Notes = &v
}

// GetInteractionDatetime returns the InteractionDatetime field value if set, zero value otherwise.
func (o *InteractionUpdate) GetInteractionDatetime() time.Time {
	if o == nil || IsNil(o.InteractionDatetime) {
		var ret time.Time
		return ret
	}
	return *o.InteractionDatetime
}

// GetInteractionDatetimeOk returns a tuple with the InteractionDatetime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionUpdate) GetInteractionDatetimeOk() (*time.Time, bool) {
	if o == nil || IsNil(o.InteractionDatetime) {
		return nil, false
	}
	return o.InteractionDatetime, true
}

// HasInteractionDatetime returns a boolean if a field has been set.
func (o *InteractionUpdate) HasInteractionDatetime() bool {
	if o != nil && !IsNil(o.InteractionDatetime) {
		return true
	}

	return false
}

// SetInteractionDatetime gets a reference to the given time.Time and assigns it to the InteractionDatetime field.
func (o *InteractionUpdate) SetInteractionDatetime(v time.Time) {
	o.InteractionDatetime = &v
}

// GetCompanyId returns the CompanyId field value if set, zero value otherwise.
func (o *InteractionUpdate) GetCompanyId() string {
	if o == nil || IsNil(o.CompanyId) {
		var ret string
		return ret
	}
	return *o.CompanyId
}

// GetCompanyIdOk returns a tuple with the CompanyId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionUpdate) GetCompanyIdOk() (*string, bool) {
	if o == nil || IsNil(o.CompanyId) {
		return nil, false
	}
	return o.CompanyId, true
}

// HasCompanyId returns a boolean if a field has been set.
func (o *InteractionUpdate) HasCompanyId() bool {
	if o != nil && !IsNil(o.CompanyId) {
		return true
	}

	return false
}

// SetCompanyId gets a reference to the given string and assigns it to the CompanyId field.
func (o *InteractionUpdate) SetCompanyId(v string) {
	o.CompanyId = &v
}

// GetContactId returns the ContactId field value if set, zero value otherwise.
func (o *InteractionUpdate) GetContactId() string {
	if o == nil || IsNil(o.ContactId) {
		var ret string
		return ret
	}
	return *o.ContactId
}

// GetContactIdOk returns a tuple with the ContactId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionUpdate) GetContactIdOk() (*string, bool) {
	if o == nil || IsNil(o.ContactId) {
		return nil, false
	}
	return o.ContactId, true
}

// HasContactId returns a boolean if a field has been set.
func (o *InteractionUpdate) HasContactId() bool {
	if o != nil && !IsNil(o.ContactId) {
		return true
	}

	return false
}

// SetContactId gets a reference to the given string and assigns it to the ContactId field.
func (o *InteractionUpdate) SetContactId(v string) {
	o.ContactId = &v
}

func (o InteractionUpdate) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o InteractionUpdate) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.InteractionType) {
		toSerialize["interaction_type"] = o.InteractionType
	}
	if !IsNil(o.Notes) {
		toSerialize["notes"] = o.Notes
	}
	if !IsNil(o.InteractionDatetime) {
		toSerialize["interaction_datetime"] = o.InteractionDatetime
	}
	if !IsNil(o.CompanyId) {
		toSerialize["company_id"] = o.CompanyId
	}
	if !IsNil(o.ContactId) {
		toSerialize["contact_id"] = o.ContactId
	}
	return toSerialize, nil
}

type NullableInteractionUpdate struct {
	value *InteractionUpdate
	isSet bool
}

func (v NullableInteractionUpdate) Get() *InteractionUpdate {
	return v.value
}

func (v *NullableInteractionUpdate) Set(val *InteractionUpdate) {
	v.value = val
	v.isSet = true
}

func (v NullableInteractionUpdate) IsSet() bool {
	return v.isSet
}

func (v *NullableInteractionUpdate) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableInteractionUpdate(val *InteractionUpdate) *NullableInteractionUpdate {
	return &NullableInteractionUpdate{value: val, isSet: true}
}

func (v NullableInteractionUpdate) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableInteractionUpdate) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


