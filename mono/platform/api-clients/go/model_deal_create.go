/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"bytes"
	"fmt"
)

// checks if the DealCreate type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &DealCreate{}

// DealCreate struct for DealCreate
type DealCreate struct {
	Title string `json:"title"`
	Description *string `json:"description,omitempty"`
	EstimatedValue *float64 `json:"estimated_value,omitempty"`
	CompanyId string `json:"company_id"`
	DealStageId string `json:"deal_stage_id"`
	ExpectedCloseDate *string `json:"expected_close_date,omitempty"`
}

type _DealCreate DealCreate

// NewDealCreate instantiates a new DealCreate object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDealCreate(title string, companyId string, dealStageId string) *DealCreate {
	this := DealCreate{}
	this.Title = title
	this.CompanyId = companyId
	this.DealStageId = dealStageId
	return &this
}

// NewDealCreateWithDefaults instantiates a new DealCreate object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDealCreateWithDefaults() *DealCreate {
	this := DealCreate{}
	return &this
}

// GetTitle returns the Title field value
func (o *DealCreate) GetTitle() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Title
}

// GetTitleOk returns a tuple with the Title field value
// and a boolean to check if the value has been set.
func (o *DealCreate) GetTitleOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Title, true
}

// SetTitle sets field value
func (o *DealCreate) SetTitle(v string) {
	o.Title = v
}

// GetDescription returns the Description field value if set, zero value otherwise.
func (o *DealCreate) GetDescription() string {
	if o == nil || IsNil(o.Description) {
		var ret string
		return ret
	}
	return *o.Description
}

// GetDescriptionOk returns a tuple with the Description field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealCreate) GetDescriptionOk() (*string, bool) {
	if o == nil || IsNil(o.Description) {
		return nil, false
	}
	return o.Description, true
}

// HasDescription returns a boolean if a field has been set.
func (o *DealCreate) HasDescription() bool {
	if o != nil && !IsNil(o.Description) {
		return true
	}

	return false
}

// SetDescription gets a reference to the given string and assigns it to the Description field.
func (o *DealCreate) SetDescription(v string) {
	o.Description = &v
}

// GetEstimatedValue returns the EstimatedValue field value if set, zero value otherwise.
func (o *DealCreate) GetEstimatedValue() float64 {
	if o == nil || IsNil(o.EstimatedValue) {
		var ret float64
		return ret
	}
	return *o.EstimatedValue
}

// GetEstimatedValueOk returns a tuple with the EstimatedValue field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealCreate) GetEstimatedValueOk() (*float64, bool) {
	if o == nil || IsNil(o.EstimatedValue) {
		return nil, false
	}
	return o.EstimatedValue, true
}

// HasEstimatedValue returns a boolean if a field has been set.
func (o *DealCreate) HasEstimatedValue() bool {
	if o != nil && !IsNil(o.EstimatedValue) {
		return true
	}

	return false
}

// SetEstimatedValue gets a reference to the given float64 and assigns it to the EstimatedValue field.
func (o *DealCreate) SetEstimatedValue(v float64) {
	o.EstimatedValue = &v
}

// GetCompanyId returns the CompanyId field value
func (o *DealCreate) GetCompanyId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.CompanyId
}

// GetCompanyIdOk returns a tuple with the CompanyId field value
// and a boolean to check if the value has been set.
func (o *DealCreate) GetCompanyIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.CompanyId, true
}

// SetCompanyId sets field value
func (o *DealCreate) SetCompanyId(v string) {
	o.CompanyId = v
}

// GetDealStageId returns the DealStageId field value
func (o *DealCreate) GetDealStageId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.DealStageId
}

// GetDealStageIdOk returns a tuple with the DealStageId field value
// and a boolean to check if the value has been set.
func (o *DealCreate) GetDealStageIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.DealStageId, true
}

// SetDealStageId sets field value
func (o *DealCreate) SetDealStageId(v string) {
	o.DealStageId = v
}

// GetExpectedCloseDate returns the ExpectedCloseDate field value if set, zero value otherwise.
func (o *DealCreate) GetExpectedCloseDate() string {
	if o == nil || IsNil(o.ExpectedCloseDate) {
		var ret string
		return ret
	}
	return *o.ExpectedCloseDate
}

// GetExpectedCloseDateOk returns a tuple with the ExpectedCloseDate field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealCreate) GetExpectedCloseDateOk() (*string, bool) {
	if o == nil || IsNil(o.ExpectedCloseDate) {
		return nil, false
	}
	return o.ExpectedCloseDate, true
}

// HasExpectedCloseDate returns a boolean if a field has been set.
func (o *DealCreate) HasExpectedCloseDate() bool {
	if o != nil && !IsNil(o.ExpectedCloseDate) {
		return true
	}

	return false
}

// SetExpectedCloseDate gets a reference to the given string and assigns it to the ExpectedCloseDate field.
func (o *DealCreate) SetExpectedCloseDate(v string) {
	o.ExpectedCloseDate = &v
}

func (o DealCreate) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o DealCreate) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["title"] = o.Title
	if !IsNil(o.Description) {
		toSerialize["description"] = o.Description
	}
	if !IsNil(o.EstimatedValue) {
		toSerialize["estimated_value"] = o.EstimatedValue
	}
	toSerialize["company_id"] = o.CompanyId
	toSerialize["deal_stage_id"] = o.DealStageId
	if !IsNil(o.ExpectedCloseDate) {
		toSerialize["expected_close_date"] = o.ExpectedCloseDate
	}
	return toSerialize, nil
}

func (o *DealCreate) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"title",
		"company_id",
		"deal_stage_id",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varDealCreate := _DealCreate{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varDealCreate)

	if err != nil {
		return err
	}

	*o = DealCreate(varDealCreate)

	return err
}

type NullableDealCreate struct {
	value *DealCreate
	isSet bool
}

func (v NullableDealCreate) Get() *DealCreate {
	return v.value
}

func (v *NullableDealCreate) Set(val *DealCreate) {
	v.value = val
	v.isSet = true
}

func (v NullableDealCreate) IsSet() bool {
	return v.isSet
}

func (v *NullableDealCreate) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDealCreate(val *DealCreate) *NullableDealCreate {
	return &NullableDealCreate{value: val, isSet: true}
}

func (v NullableDealCreate) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDealCreate) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


