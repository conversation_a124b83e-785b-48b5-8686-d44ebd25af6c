-- First, drop the existing check constraint on status
ALTER TABLE projects DROP CONSTRAINT IF EXISTS projects_status_check;

-- Add new check constraint with the updated status values
ALTER TABLE projects ADD CONSTRAINT projects_status_check 
CHECK (status IN ('not-started', 'in-progress', 'completed'));

-- Now update existing project statuses to new values
UPDATE projects 
SET status = CASE 
  WHEN status = 'active' THEN 'in-progress'
  WHEN status = 'on_hold' THEN 'not-started'
  WHEN status = 'completed' THEN 'completed'
  ELSE 'not-started'
END;