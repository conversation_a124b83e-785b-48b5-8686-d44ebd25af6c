
import { FormEvent, useRef, useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { 
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useAuth } from "@/context/AuthContext";

interface InteractionFormProps {
  companyId: string;
  interactionType: string;
  setInteractionType: (type: string) => void;
  onCancel: () => void;
  onSuccess: () => void;
}

export function InteractionForm({
  companyId,
  interactionType,
  setInteractionType,
  onCancel,
  onSuccess
}: InteractionFormProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const [notes, setNotes] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const notesTextareaRef = useRef<HTMLTextAreaElement>(null);
  
  useEffect(() => {
    if (notesTextareaRef.current) {
      setTimeout(() => {
        notesTextareaRef.current?.focus();
      }, 50);
    }
  }, []);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    if (!notes.trim()) {
      toast({
        title: "Error",
        description: "Please enter notes for this interaction",
        variant: "destructive",
      });
      return;
    }

    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to add interactions",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      const { error } = await supabase
        .from("interactions")
        .insert({
          company_id: companyId,
          interaction_type: interactionType,
          notes: notes,
          interaction_datetime: new Date().toISOString(),
          created_by: user.id
        });

      if (error) throw error;
      
      toast({
        title: "Success",
        description: "Interaction added successfully",
      });
      
      setNotes("");
      onSuccess();
    } catch (error) {
      console.error("Error adding interaction:", error);
      toast({
        title: "Error",
        description: "Failed to add interaction. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-base">New Interaction</CardTitle>
        <CardDescription>Add details about your interaction with this company</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="interaction-type" className="text-sm font-medium">
              Interaction Type
            </label>
            <Select
              value={interactionType}
              onValueChange={setInteractionType}
            >
              <SelectTrigger id="interaction-type">
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="note">Note</SelectItem>
                  <SelectItem value="call">Call</SelectItem>
                  <SelectItem value="meeting">Meeting</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <label htmlFor="notes" className="text-sm font-medium">
              Notes
            </label>
            <Textarea
              id="notes"
              ref={notesTextareaRef}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Enter details about this interaction"
              rows={4}
              className="resize-none"
            />
          </div>
          
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button 
              type="submit"
              size="sm"
              disabled={isSubmitting}
            >
              Save
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
