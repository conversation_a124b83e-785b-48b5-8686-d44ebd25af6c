# OAuthSignInRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Provider** | **string** |  | 
**RedirectTo** | Pointer to **string** |  | [optional] 

## Methods

### NewOAuthSignInRequest

`func NewOAuthSignInRequest(provider string, ) *OAuthSignInRequest`

NewOAuthSignInRequest instantiates a new OAuthSignInRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewOAuthSignInRequestWithDefaults

`func NewOAuthSignInRequestWithDefaults() *OAuthSignInRequest`

NewOAuthSignInRequestWithDefaults instantiates a new OAuthSignInRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetProvider

`func (o *OAuthSignInRequest) GetProvider() string`

Get<PERSON>rovider returns the Provider field if non-nil, zero value otherwise.

### GetProviderOk

`func (o *OAuthSignInRequest) GetProviderOk() (*string, bool)`

GetProviderOk returns a tuple with the Provider field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetProvider

`func (o *OAuthSignInRequest) SetProvider(v string)`

SetProvider sets Provider field to given value.


### GetRedirectTo

`func (o *OAuthSignInRequest) GetRedirectTo() string`

GetRedirectTo returns the RedirectTo field if non-nil, zero value otherwise.

### GetRedirectToOk

`func (o *OAuthSignInRequest) GetRedirectToOk() (*string, bool)`

GetRedirectToOk returns a tuple with the RedirectTo field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetRedirectTo

`func (o *OAuthSignInRequest) SetRedirectTo(v string)`

SetRedirectTo sets RedirectTo field to given value.

### HasRedirectTo

`func (o *OAuthSignInRequest) HasRedirectTo() bool`

HasRedirectTo returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


