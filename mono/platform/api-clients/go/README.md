# Go API client for crmclient

Complete API specification for the CRM system

## Overview
This API client was generated by the [OpenAPI Generator](https://openapi-generator.tech) project.  By using the [OpenAPI-spec](https://www.openapis.org/) from a remote server, you can easily generate an API client.

- API version: 1.0.0
- Package version: 1.0.0
- Generator version: 7.9.0
- Build package: org.openapitools.codegen.languages.GoClientCodegen

## Installation

Install the following dependencies:

```sh
go get github.com/stretchr/testify/assert
go get golang.org/x/net/context
```

Put the package under your project folder and add the following in import:

```go
import crmclient "github.com/GIT_USER_ID/GIT_REPO_ID"
```

To use a proxy, set the environment variable `HTTP_PROXY`:

```go
os.Setenv("HTTP_PROXY", "http://proxy_name:proxy_port")
```

## Configuration of Server URL

Default configuration comes with `Servers` field that contains server objects as defined in the OpenAPI specification.

### Select Server Configuration

For using other server than the one defined on index 0 set context value `crmclient.ContextServerIndex` of type `int`.

```go
ctx := context.WithValue(context.Background(), crmclient.ContextServerIndex, 1)
```

### Templated Server URL

Templated server URL is formatted using default variables from configuration or from context value `crmclient.ContextServerVariables` of type `map[string]string`.

```go
ctx := context.WithValue(context.Background(), crmclient.ContextServerVariables, map[string]string{
	"basePath": "v2",
})
```

Note, enum values are always validated and all unused variables are silently ignored.

### URLs Configuration per Operation

Each operation can use different server URL defined using `OperationServers` map in the `Configuration`.
An operation is uniquely identified by `"{classname}Service.{nickname}"` string.
Similar rules for overriding default operation server index and variables applies by using `crmclient.ContextOperationServerIndices` and `crmclient.ContextOperationServerVariables` context maps.

```go
ctx := context.WithValue(context.Background(), crmclient.ContextOperationServerIndices, map[string]int{
	"{classname}Service.{nickname}": 2,
})
ctx = context.WithValue(context.Background(), crmclient.ContextOperationServerVariables, map[string]map[string]string{
	"{classname}Service.{nickname}": {
		"port": "8443",
	},
})
```

## Documentation for API Endpoints

All URIs are relative to *https://api.crm.example.com/v1*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*ArliAPI* | [**ArliDocumentsGet**](docs/ArliAPI.md#arlidocumentsget) | **Get** /arli/documents | List documents
*ArliAPI* | [**ArliDocumentsIdDelete**](docs/ArliAPI.md#arlidocumentsiddelete) | **Delete** /arli/documents/{id} | Delete document
*ArliAPI* | [**ArliDocumentsIdGet**](docs/ArliAPI.md#arlidocumentsidget) | **Get** /arli/documents/{id} | Get document
*ArliAPI* | [**ArliDocumentsIdPut**](docs/ArliAPI.md#arlidocumentsidput) | **Put** /arli/documents/{id} | Update document
*ArliAPI* | [**ArliDocumentsPost**](docs/ArliAPI.md#arlidocumentspost) | **Post** /arli/documents | Create document
*ArliAPI* | [**ArliDocumentsVectorizePost**](docs/ArliAPI.md#arlidocumentsvectorizepost) | **Post** /arli/documents/vectorize | Vectorize document
*ArliAPI* | [**ArliFilterTagsGet**](docs/ArliAPI.md#arlifiltertagsget) | **Get** /arli/filter-tags | List filter tags
*ArliAPI* | [**ArliFilterTagsPost**](docs/ArliAPI.md#arlifiltertagspost) | **Post** /arli/filter-tags | Create filter tag
*AuthenticationAPI* | [**AuthSessionGet**](docs/AuthenticationAPI.md#authsessionget) | **Get** /auth/session | Get current session
*AuthenticationAPI* | [**AuthSigninOauthPost**](docs/AuthenticationAPI.md#authsigninoauthpost) | **Post** /auth/signin/oauth | Sign in with OAuth
*AuthenticationAPI* | [**AuthSigninPost**](docs/AuthenticationAPI.md#authsigninpost) | **Post** /auth/signin | Sign in with email/password
*AuthenticationAPI* | [**AuthSignoutPost**](docs/AuthenticationAPI.md#authsignoutpost) | **Post** /auth/signout | Sign out
*AuthenticationAPI* | [**AuthUserGet**](docs/AuthenticationAPI.md#authuserget) | **Get** /auth/user | Get current user
*CompaniesAPI* | [**CompaniesGet**](docs/CompaniesAPI.md#companiesget) | **Get** /companies | List companies
*CompaniesAPI* | [**CompaniesIdDelete**](docs/CompaniesAPI.md#companiesiddelete) | **Delete** /companies/{id} | Delete company
*CompaniesAPI* | [**CompaniesIdGet**](docs/CompaniesAPI.md#companiesidget) | **Get** /companies/{id} | Get company
*CompaniesAPI* | [**CompaniesIdPut**](docs/CompaniesAPI.md#companiesidput) | **Put** /companies/{id} | Update company
*CompaniesAPI* | [**CompaniesIdStatusPut**](docs/CompaniesAPI.md#companiesidstatusput) | **Put** /companies/{id}/status | Update company status
*CompaniesAPI* | [**CompaniesPost**](docs/CompaniesAPI.md#companiespost) | **Post** /companies | Create company
*CompaniesAPI* | [**CompanyStatusesGet**](docs/CompaniesAPI.md#companystatusesget) | **Get** /company-statuses | List company statuses
*ContactsAPI* | [**ContactsGet**](docs/ContactsAPI.md#contactsget) | **Get** /contacts | List contacts
*ContactsAPI* | [**ContactsIdCompanyDelete**](docs/ContactsAPI.md#contactsidcompanydelete) | **Delete** /contacts/{id}/company | Unlink contact from company
*ContactsAPI* | [**ContactsIdCompanyPut**](docs/ContactsAPI.md#contactsidcompanyput) | **Put** /contacts/{id}/company | Link contact to company
*ContactsAPI* | [**ContactsIdDelete**](docs/ContactsAPI.md#contactsiddelete) | **Delete** /contacts/{id} | Delete contact
*ContactsAPI* | [**ContactsIdGet**](docs/ContactsAPI.md#contactsidget) | **Get** /contacts/{id} | Get contact
*ContactsAPI* | [**ContactsIdPut**](docs/ContactsAPI.md#contactsidput) | **Put** /contacts/{id} | Update contact
*ContactsAPI* | [**ContactsPost**](docs/ContactsAPI.md#contactspost) | **Post** /contacts | Create contact
*DealsAPI* | [**DealStagesGet**](docs/DealsAPI.md#dealstagesget) | **Get** /deal-stages | List deal stages
*DealsAPI* | [**DealsGet**](docs/DealsAPI.md#dealsget) | **Get** /deals | List deals
*DealsAPI* | [**DealsIdDelete**](docs/DealsAPI.md#dealsiddelete) | **Delete** /deals/{id} | Delete deal
*DealsAPI* | [**DealsIdGet**](docs/DealsAPI.md#dealsidget) | **Get** /deals/{id} | Get deal
*DealsAPI* | [**DealsIdPut**](docs/DealsAPI.md#dealsidput) | **Put** /deals/{id} | Update deal
*DealsAPI* | [**DealsPost**](docs/DealsAPI.md#dealspost) | **Post** /deals | Create deal
*InteractionsAPI* | [**InteractionsGet**](docs/InteractionsAPI.md#interactionsget) | **Get** /interactions | List interactions
*InteractionsAPI* | [**InteractionsIdDelete**](docs/InteractionsAPI.md#interactionsiddelete) | **Delete** /interactions/{id} | Delete interaction
*InteractionsAPI* | [**InteractionsIdGet**](docs/InteractionsAPI.md#interactionsidget) | **Get** /interactions/{id} | Get interaction
*InteractionsAPI* | [**InteractionsIdPut**](docs/InteractionsAPI.md#interactionsidput) | **Put** /interactions/{id} | Update interaction
*InteractionsAPI* | [**InteractionsPost**](docs/InteractionsAPI.md#interactionspost) | **Post** /interactions | Create interaction
*UserManagementAPI* | [**UsersProfileGet**](docs/UserManagementAPI.md#usersprofileget) | **Get** /users/profile | Get user profile
*UserManagementAPI* | [**UsersProfilePut**](docs/UserManagementAPI.md#usersprofileput) | **Put** /users/profile | Update user profile


## Documentation For Models

 - [ArliDocumentsGet200Response](docs/ArliDocumentsGet200Response.md)
 - [ArliDocumentsVectorizePost200Response](docs/ArliDocumentsVectorizePost200Response.md)
 - [ArliDocumentsVectorizePostRequest](docs/ArliDocumentsVectorizePostRequest.md)
 - [AuthResponse](docs/AuthResponse.md)
 - [Company](docs/Company.md)
 - [CompanyBasicInfo](docs/CompanyBasicInfo.md)
 - [CompanyCreate](docs/CompanyCreate.md)
 - [CompanyDetails](docs/CompanyDetails.md)
 - [CompanyInfo](docs/CompanyInfo.md)
 - [CompanyStatus](docs/CompanyStatus.md)
 - [CompanyStatusUpdateRequest](docs/CompanyStatusUpdateRequest.md)
 - [CompanyUpdate](docs/CompanyUpdate.md)
 - [Contact](docs/Contact.md)
 - [ContactCompanyUpdateRequest](docs/ContactCompanyUpdateRequest.md)
 - [ContactCreate](docs/ContactCreate.md)
 - [ContactDetails](docs/ContactDetails.md)
 - [ContactUpdate](docs/ContactUpdate.md)
 - [ContactWithCompany](docs/ContactWithCompany.md)
 - [Deal](docs/Deal.md)
 - [DealCompanyInfo](docs/DealCompanyInfo.md)
 - [DealCreate](docs/DealCreate.md)
 - [DealDetails](docs/DealDetails.md)
 - [DealStage](docs/DealStage.md)
 - [DealStageInfo](docs/DealStageInfo.md)
 - [DealUpdate](docs/DealUpdate.md)
 - [DealWithDetails](docs/DealWithDetails.md)
 - [Document](docs/Document.md)
 - [DocumentCreate](docs/DocumentCreate.md)
 - [DocumentDetails](docs/DocumentDetails.md)
 - [DocumentUpdate](docs/DocumentUpdate.md)
 - [Error](docs/Error.md)
 - [FilterTag](docs/FilterTag.md)
 - [FilterTagCreate](docs/FilterTagCreate.md)
 - [Interaction](docs/Interaction.md)
 - [InteractionCreate](docs/InteractionCreate.md)
 - [InteractionDetails](docs/InteractionDetails.md)
 - [InteractionDetailsAllOfCompany](docs/InteractionDetailsAllOfCompany.md)
 - [InteractionDetailsAllOfContact](docs/InteractionDetailsAllOfContact.md)
 - [InteractionUpdate](docs/InteractionUpdate.md)
 - [InteractionWithDetails](docs/InteractionWithDetails.md)
 - [InteractionWithDetailsAllOfCompany](docs/InteractionWithDetailsAllOfCompany.md)
 - [InteractionWithDetailsAllOfContact](docs/InteractionWithDetailsAllOfContact.md)
 - [InteractionsGet200Response](docs/InteractionsGet200Response.md)
 - [OAuthRedirectResponse](docs/OAuthRedirectResponse.md)
 - [OAuthSignInRequest](docs/OAuthSignInRequest.md)
 - [Session](docs/Session.md)
 - [SignInRequest](docs/SignInRequest.md)
 - [User](docs/User.md)
 - [UserProfile](docs/UserProfile.md)
 - [UserProfileUpdate](docs/UserProfileUpdate.md)
 - [ValidationError](docs/ValidationError.md)
 - [ValidationErrorDetails](docs/ValidationErrorDetails.md)


## Documentation For Authorization


Authentication schemes defined for the API:
### bearerAuth

- **Type**: HTTP Bearer token authentication

Example

```go
auth := context.WithValue(context.Background(), crmclient.ContextAccessToken, "BEARER_TOKEN_STRING")
r, err := client.Service.Operation(auth, args)
```


## Documentation for Utility Methods

Due to the fact that model structure members are all pointers, this package contains
a number of utility functions to easily obtain pointers to values of basic types.
Each of these functions takes a value of the given basic type and returns a pointer to it:

* `PtrBool`
* `PtrInt`
* `PtrInt32`
* `PtrInt64`
* `PtrFloat`
* `PtrFloat32`
* `PtrFloat64`
* `PtrString`
* `PtrTime`

## Author

<EMAIL>

