/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"time"
)

// checks if the InteractionWithDetails type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &InteractionWithDetails{}

// InteractionWithDetails struct for InteractionWithDetails
type InteractionWithDetails struct {
	Id *string `json:"id,omitempty"`
	CompanyId NullableString `json:"company_id,omitempty"`
	ContactId NullableString `json:"contact_id,omitempty"`
	InteractionType *string `json:"interaction_type,omitempty"`
	Notes *string `json:"notes,omitempty"`
	InteractionDatetime *time.Time `json:"interaction_datetime,omitempty"`
	CreatedBy *string `json:"created_by,omitempty"`
	CreatedAt *time.Time `json:"created_at,omitempty"`
	UpdatedAt *time.Time `json:"updated_at,omitempty"`
	Company NullableInteractionWithDetailsAllOfCompany `json:"company,omitempty"`
	Contact NullableInteractionWithDetailsAllOfContact `json:"contact,omitempty"`
}

// NewInteractionWithDetails instantiates a new InteractionWithDetails object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewInteractionWithDetails() *InteractionWithDetails {
	this := InteractionWithDetails{}
	return &this
}

// NewInteractionWithDetailsWithDefaults instantiates a new InteractionWithDetails object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewInteractionWithDetailsWithDefaults() *InteractionWithDetails {
	this := InteractionWithDetails{}
	return &this
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *InteractionWithDetails) GetId() string {
	if o == nil || IsNil(o.Id) {
		var ret string
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionWithDetails) GetIdOk() (*string, bool) {
	if o == nil || IsNil(o.Id) {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *InteractionWithDetails) HasId() bool {
	if o != nil && !IsNil(o.Id) {
		return true
	}

	return false
}

// SetId gets a reference to the given string and assigns it to the Id field.
func (o *InteractionWithDetails) SetId(v string) {
	o.Id = &v
}

// GetCompanyId returns the CompanyId field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *InteractionWithDetails) GetCompanyId() string {
	if o == nil || IsNil(o.CompanyId.Get()) {
		var ret string
		return ret
	}
	return *o.CompanyId.Get()
}

// GetCompanyIdOk returns a tuple with the CompanyId field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *InteractionWithDetails) GetCompanyIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.CompanyId.Get(), o.CompanyId.IsSet()
}

// HasCompanyId returns a boolean if a field has been set.
func (o *InteractionWithDetails) HasCompanyId() bool {
	if o != nil && o.CompanyId.IsSet() {
		return true
	}

	return false
}

// SetCompanyId gets a reference to the given NullableString and assigns it to the CompanyId field.
func (o *InteractionWithDetails) SetCompanyId(v string) {
	o.CompanyId.Set(&v)
}
// SetCompanyIdNil sets the value for CompanyId to be an explicit nil
func (o *InteractionWithDetails) SetCompanyIdNil() {
	o.CompanyId.Set(nil)
}

// UnsetCompanyId ensures that no value is present for CompanyId, not even an explicit nil
func (o *InteractionWithDetails) UnsetCompanyId() {
	o.CompanyId.Unset()
}

// GetContactId returns the ContactId field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *InteractionWithDetails) GetContactId() string {
	if o == nil || IsNil(o.ContactId.Get()) {
		var ret string
		return ret
	}
	return *o.ContactId.Get()
}

// GetContactIdOk returns a tuple with the ContactId field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *InteractionWithDetails) GetContactIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.ContactId.Get(), o.ContactId.IsSet()
}

// HasContactId returns a boolean if a field has been set.
func (o *InteractionWithDetails) HasContactId() bool {
	if o != nil && o.ContactId.IsSet() {
		return true
	}

	return false
}

// SetContactId gets a reference to the given NullableString and assigns it to the ContactId field.
func (o *InteractionWithDetails) SetContactId(v string) {
	o.ContactId.Set(&v)
}
// SetContactIdNil sets the value for ContactId to be an explicit nil
func (o *InteractionWithDetails) SetContactIdNil() {
	o.ContactId.Set(nil)
}

// UnsetContactId ensures that no value is present for ContactId, not even an explicit nil
func (o *InteractionWithDetails) UnsetContactId() {
	o.ContactId.Unset()
}

// GetInteractionType returns the InteractionType field value if set, zero value otherwise.
func (o *InteractionWithDetails) GetInteractionType() string {
	if o == nil || IsNil(o.InteractionType) {
		var ret string
		return ret
	}
	return *o.InteractionType
}

// GetInteractionTypeOk returns a tuple with the InteractionType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionWithDetails) GetInteractionTypeOk() (*string, bool) {
	if o == nil || IsNil(o.InteractionType) {
		return nil, false
	}
	return o.InteractionType, true
}

// HasInteractionType returns a boolean if a field has been set.
func (o *InteractionWithDetails) HasInteractionType() bool {
	if o != nil && !IsNil(o.InteractionType) {
		return true
	}

	return false
}

// SetInteractionType gets a reference to the given string and assigns it to the InteractionType field.
func (o *InteractionWithDetails) SetInteractionType(v string) {
	o.InteractionType = &v
}

// GetNotes returns the Notes field value if set, zero value otherwise.
func (o *InteractionWithDetails) GetNotes() string {
	if o == nil || IsNil(o.Notes) {
		var ret string
		return ret
	}
	return *o.Notes
}

// GetNotesOk returns a tuple with the Notes field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionWithDetails) GetNotesOk() (*string, bool) {
	if o == nil || IsNil(o.Notes) {
		return nil, false
	}
	return o.Notes, true
}

// HasNotes returns a boolean if a field has been set.
func (o *InteractionWithDetails) HasNotes() bool {
	if o != nil && !IsNil(o.Notes) {
		return true
	}

	return false
}

// SetNotes gets a reference to the given string and assigns it to the Notes field.
func (o *InteractionWithDetails) SetNotes(v string) {
	o.Notes = &v
}

// GetInteractionDatetime returns the InteractionDatetime field value if set, zero value otherwise.
func (o *InteractionWithDetails) GetInteractionDatetime() time.Time {
	if o == nil || IsNil(o.InteractionDatetime) {
		var ret time.Time
		return ret
	}
	return *o.InteractionDatetime
}

// GetInteractionDatetimeOk returns a tuple with the InteractionDatetime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionWithDetails) GetInteractionDatetimeOk() (*time.Time, bool) {
	if o == nil || IsNil(o.InteractionDatetime) {
		return nil, false
	}
	return o.InteractionDatetime, true
}

// HasInteractionDatetime returns a boolean if a field has been set.
func (o *InteractionWithDetails) HasInteractionDatetime() bool {
	if o != nil && !IsNil(o.InteractionDatetime) {
		return true
	}

	return false
}

// SetInteractionDatetime gets a reference to the given time.Time and assigns it to the InteractionDatetime field.
func (o *InteractionWithDetails) SetInteractionDatetime(v time.Time) {
	o.InteractionDatetime = &v
}

// GetCreatedBy returns the CreatedBy field value if set, zero value otherwise.
func (o *InteractionWithDetails) GetCreatedBy() string {
	if o == nil || IsNil(o.CreatedBy) {
		var ret string
		return ret
	}
	return *o.CreatedBy
}

// GetCreatedByOk returns a tuple with the CreatedBy field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionWithDetails) GetCreatedByOk() (*string, bool) {
	if o == nil || IsNil(o.CreatedBy) {
		return nil, false
	}
	return o.CreatedBy, true
}

// HasCreatedBy returns a boolean if a field has been set.
func (o *InteractionWithDetails) HasCreatedBy() bool {
	if o != nil && !IsNil(o.CreatedBy) {
		return true
	}

	return false
}

// SetCreatedBy gets a reference to the given string and assigns it to the CreatedBy field.
func (o *InteractionWithDetails) SetCreatedBy(v string) {
	o.CreatedBy = &v
}

// GetCreatedAt returns the CreatedAt field value if set, zero value otherwise.
func (o *InteractionWithDetails) GetCreatedAt() time.Time {
	if o == nil || IsNil(o.CreatedAt) {
		var ret time.Time
		return ret
	}
	return *o.CreatedAt
}

// GetCreatedAtOk returns a tuple with the CreatedAt field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionWithDetails) GetCreatedAtOk() (*time.Time, bool) {
	if o == nil || IsNil(o.CreatedAt) {
		return nil, false
	}
	return o.CreatedAt, true
}

// HasCreatedAt returns a boolean if a field has been set.
func (o *InteractionWithDetails) HasCreatedAt() bool {
	if o != nil && !IsNil(o.CreatedAt) {
		return true
	}

	return false
}

// SetCreatedAt gets a reference to the given time.Time and assigns it to the CreatedAt field.
func (o *InteractionWithDetails) SetCreatedAt(v time.Time) {
	o.CreatedAt = &v
}

// GetUpdatedAt returns the UpdatedAt field value if set, zero value otherwise.
func (o *InteractionWithDetails) GetUpdatedAt() time.Time {
	if o == nil || IsNil(o.UpdatedAt) {
		var ret time.Time
		return ret
	}
	return *o.UpdatedAt
}

// GetUpdatedAtOk returns a tuple with the UpdatedAt field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionWithDetails) GetUpdatedAtOk() (*time.Time, bool) {
	if o == nil || IsNil(o.UpdatedAt) {
		return nil, false
	}
	return o.UpdatedAt, true
}

// HasUpdatedAt returns a boolean if a field has been set.
func (o *InteractionWithDetails) HasUpdatedAt() bool {
	if o != nil && !IsNil(o.UpdatedAt) {
		return true
	}

	return false
}

// SetUpdatedAt gets a reference to the given time.Time and assigns it to the UpdatedAt field.
func (o *InteractionWithDetails) SetUpdatedAt(v time.Time) {
	o.UpdatedAt = &v
}

// GetCompany returns the Company field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *InteractionWithDetails) GetCompany() InteractionWithDetailsAllOfCompany {
	if o == nil || IsNil(o.Company.Get()) {
		var ret InteractionWithDetailsAllOfCompany
		return ret
	}
	return *o.Company.Get()
}

// GetCompanyOk returns a tuple with the Company field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *InteractionWithDetails) GetCompanyOk() (*InteractionWithDetailsAllOfCompany, bool) {
	if o == nil {
		return nil, false
	}
	return o.Company.Get(), o.Company.IsSet()
}

// HasCompany returns a boolean if a field has been set.
func (o *InteractionWithDetails) HasCompany() bool {
	if o != nil && o.Company.IsSet() {
		return true
	}

	return false
}

// SetCompany gets a reference to the given NullableInteractionWithDetailsAllOfCompany and assigns it to the Company field.
func (o *InteractionWithDetails) SetCompany(v InteractionWithDetailsAllOfCompany) {
	o.Company.Set(&v)
}
// SetCompanyNil sets the value for Company to be an explicit nil
func (o *InteractionWithDetails) SetCompanyNil() {
	o.Company.Set(nil)
}

// UnsetCompany ensures that no value is present for Company, not even an explicit nil
func (o *InteractionWithDetails) UnsetCompany() {
	o.Company.Unset()
}

// GetContact returns the Contact field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *InteractionWithDetails) GetContact() InteractionWithDetailsAllOfContact {
	if o == nil || IsNil(o.Contact.Get()) {
		var ret InteractionWithDetailsAllOfContact
		return ret
	}
	return *o.Contact.Get()
}

// GetContactOk returns a tuple with the Contact field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *InteractionWithDetails) GetContactOk() (*InteractionWithDetailsAllOfContact, bool) {
	if o == nil {
		return nil, false
	}
	return o.Contact.Get(), o.Contact.IsSet()
}

// HasContact returns a boolean if a field has been set.
func (o *InteractionWithDetails) HasContact() bool {
	if o != nil && o.Contact.IsSet() {
		return true
	}

	return false
}

// SetContact gets a reference to the given NullableInteractionWithDetailsAllOfContact and assigns it to the Contact field.
func (o *InteractionWithDetails) SetContact(v InteractionWithDetailsAllOfContact) {
	o.Contact.Set(&v)
}
// SetContactNil sets the value for Contact to be an explicit nil
func (o *InteractionWithDetails) SetContactNil() {
	o.Contact.Set(nil)
}

// UnsetContact ensures that no value is present for Contact, not even an explicit nil
func (o *InteractionWithDetails) UnsetContact() {
	o.Contact.Unset()
}

func (o InteractionWithDetails) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o InteractionWithDetails) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Id) {
		toSerialize["id"] = o.Id
	}
	if o.CompanyId.IsSet() {
		toSerialize["company_id"] = o.CompanyId.Get()
	}
	if o.ContactId.IsSet() {
		toSerialize["contact_id"] = o.ContactId.Get()
	}
	if !IsNil(o.InteractionType) {
		toSerialize["interaction_type"] = o.InteractionType
	}
	if !IsNil(o.Notes) {
		toSerialize["notes"] = o.Notes
	}
	if !IsNil(o.InteractionDatetime) {
		toSerialize["interaction_datetime"] = o.InteractionDatetime
	}
	if !IsNil(o.CreatedBy) {
		toSerialize["created_by"] = o.CreatedBy
	}
	if !IsNil(o.CreatedAt) {
		toSerialize["created_at"] = o.CreatedAt
	}
	if !IsNil(o.UpdatedAt) {
		toSerialize["updated_at"] = o.UpdatedAt
	}
	if o.Company.IsSet() {
		toSerialize["company"] = o.Company.Get()
	}
	if o.Contact.IsSet() {
		toSerialize["contact"] = o.Contact.Get()
	}
	return toSerialize, nil
}

type NullableInteractionWithDetails struct {
	value *InteractionWithDetails
	isSet bool
}

func (v NullableInteractionWithDetails) Get() *InteractionWithDetails {
	return v.value
}

func (v *NullableInteractionWithDetails) Set(val *InteractionWithDetails) {
	v.value = val
	v.isSet = true
}

func (v NullableInteractionWithDetails) IsSet() bool {
	return v.isSet
}

func (v *NullableInteractionWithDetails) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableInteractionWithDetails(val *InteractionWithDetails) *NullableInteractionWithDetails {
	return &NullableInteractionWithDetails{value: val, isSet: true}
}

func (v NullableInteractionWithDetails) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableInteractionWithDetails) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


