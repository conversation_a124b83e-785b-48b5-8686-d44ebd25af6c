/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
)

// checks if the UserProfileUpdate type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &UserProfileUpdate{}

// UserProfileUpdate struct for UserProfileUpdate
type UserProfileUpdate struct {
	FullName *string `json:"full_name,omitempty"`
	AvatarUrl *string `json:"avatar_url,omitempty"`
	Timezone *string `json:"timezone,omitempty"`
}

// NewUserProfileUpdate instantiates a new UserProfileUpdate object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewUserProfileUpdate() *UserProfileUpdate {
	this := UserProfileUpdate{}
	return &this
}

// NewUserProfileUpdateWithDefaults instantiates a new UserProfileUpdate object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewUserProfileUpdateWithDefaults() *UserProfileUpdate {
	this := UserProfileUpdate{}
	return &this
}

// GetFullName returns the FullName field value if set, zero value otherwise.
func (o *UserProfileUpdate) GetFullName() string {
	if o == nil || IsNil(o.FullName) {
		var ret string
		return ret
	}
	return *o.FullName
}

// GetFullNameOk returns a tuple with the FullName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserProfileUpdate) GetFullNameOk() (*string, bool) {
	if o == nil || IsNil(o.FullName) {
		return nil, false
	}
	return o.FullName, true
}

// HasFullName returns a boolean if a field has been set.
func (o *UserProfileUpdate) HasFullName() bool {
	if o != nil && !IsNil(o.FullName) {
		return true
	}

	return false
}

// SetFullName gets a reference to the given string and assigns it to the FullName field.
func (o *UserProfileUpdate) SetFullName(v string) {
	o.FullName = &v
}

// GetAvatarUrl returns the AvatarUrl field value if set, zero value otherwise.
func (o *UserProfileUpdate) GetAvatarUrl() string {
	if o == nil || IsNil(o.AvatarUrl) {
		var ret string
		return ret
	}
	return *o.AvatarUrl
}

// GetAvatarUrlOk returns a tuple with the AvatarUrl field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserProfileUpdate) GetAvatarUrlOk() (*string, bool) {
	if o == nil || IsNil(o.AvatarUrl) {
		return nil, false
	}
	return o.AvatarUrl, true
}

// HasAvatarUrl returns a boolean if a field has been set.
func (o *UserProfileUpdate) HasAvatarUrl() bool {
	if o != nil && !IsNil(o.AvatarUrl) {
		return true
	}

	return false
}

// SetAvatarUrl gets a reference to the given string and assigns it to the AvatarUrl field.
func (o *UserProfileUpdate) SetAvatarUrl(v string) {
	o.AvatarUrl = &v
}

// GetTimezone returns the Timezone field value if set, zero value otherwise.
func (o *UserProfileUpdate) GetTimezone() string {
	if o == nil || IsNil(o.Timezone) {
		var ret string
		return ret
	}
	return *o.Timezone
}

// GetTimezoneOk returns a tuple with the Timezone field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *UserProfileUpdate) GetTimezoneOk() (*string, bool) {
	if o == nil || IsNil(o.Timezone) {
		return nil, false
	}
	return o.Timezone, true
}

// HasTimezone returns a boolean if a field has been set.
func (o *UserProfileUpdate) HasTimezone() bool {
	if o != nil && !IsNil(o.Timezone) {
		return true
	}

	return false
}

// SetTimezone gets a reference to the given string and assigns it to the Timezone field.
func (o *UserProfileUpdate) SetTimezone(v string) {
	o.Timezone = &v
}

func (o UserProfileUpdate) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o UserProfileUpdate) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.FullName) {
		toSerialize["full_name"] = o.FullName
	}
	if !IsNil(o.AvatarUrl) {
		toSerialize["avatar_url"] = o.AvatarUrl
	}
	if !IsNil(o.Timezone) {
		toSerialize["timezone"] = o.Timezone
	}
	return toSerialize, nil
}

type NullableUserProfileUpdate struct {
	value *UserProfileUpdate
	isSet bool
}

func (v NullableUserProfileUpdate) Get() *UserProfileUpdate {
	return v.value
}

func (v *NullableUserProfileUpdate) Set(val *UserProfileUpdate) {
	v.value = val
	v.isSet = true
}

func (v NullableUserProfileUpdate) IsSet() bool {
	return v.isSet
}

func (v *NullableUserProfileUpdate) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableUserProfileUpdate(val *UserProfileUpdate) *NullableUserProfileUpdate {
	return &NullableUserProfileUpdate{value: val, isSet: true}
}

func (v NullableUserProfileUpdate) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableUserProfileUpdate) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


