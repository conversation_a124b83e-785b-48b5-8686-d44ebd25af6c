import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server } from 'socket.io';
import dotenv from 'dotenv';

import { logger } from './utils/logger';
import { errorHandler } from './middleware/errorHandler';
// import { authMiddleware } from './middleware/auth'; // Temporarily disabled
import { validateEnv } from './config/env';

// Import routes
import authRoutes from './routes/auth';
// Temporarily disabled routes due to TypeScript errors - will fix after basic server is running
// import projectRoutes from './routes/projects';
// import taskRoutes from './routes/tasks';
// import companyRoutes from './routes/companies';
// import contactRoutes from './routes/contacts';
// import requirementRoutes from './routes/requirements';
// import interactionRoutes from './routes/interactions';
// import userRoutes from './routes/users';
import healthRoutes from './routes/health';

// Load environment variables
dotenv.config();

// Validate environment variables
validateEnv();

const app = express();
const server = createServer(app);

// Initialize Socket.IO
const io = new Server(server, {
  cors: {
    origin: process.env['CORS_ORIGIN'] || 'http://localhost:3000',
    credentials: true,
  },
});

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env['RATE_LIMIT_WINDOW_MS'] || '900000'), // 15 minutes
  max: parseInt(process.env['RATE_LIMIT_MAX_REQUESTS'] || '100'), // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(limiter);
app.use(cors({
  origin: process.env['CORS_ORIGIN'] || 'http://localhost:3000',
  credentials: true,
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging
app.use((req, _res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });
  next();
});

// Health check (no auth required)
app.use('/health', healthRoutes);

// API routes
const apiRouter = express.Router();

// Public routes (no auth required)
apiRouter.use('/auth', authRoutes);

// Protected routes (auth required) - temporarily disabled to get basic server running
// apiRouter.use('/projects', authMiddleware, projectRoutes);
// apiRouter.use('/tasks', authMiddleware, taskRoutes);
// apiRouter.use('/companies', authMiddleware, companyRoutes);
// apiRouter.use('/contacts', authMiddleware, contactRoutes);
// apiRouter.use('/requirements', authMiddleware, requirementRoutes);
// apiRouter.use('/interactions', authMiddleware, interactionRoutes);
// apiRouter.use('/users', authMiddleware, userRoutes);

// Mount API routes
app.use('/api', apiRouter);

// Socket.IO connection handling
io.use((socket, next) => {
  // Add authentication middleware for socket connections
  const token = socket.handshake.auth['token'];
  if (!token) {
    return next(new Error('Authentication error'));
  }
  // TODO: Verify JWT token and attach user to socket
  next();
});

io.on('connection', (socket) => {
  logger.info('User connected', { socketId: socket.id });

  // Join project rooms for real-time updates
  socket.on('join-project', (projectId: string) => {
    socket.join(`project-${projectId}`);
    logger.info('User joined project room', { socketId: socket.id, projectId });
  });

  // Leave project rooms
  socket.on('leave-project', (projectId: string) => {
    socket.leave(`project-${projectId}`);
    logger.info('User left project room', { socketId: socket.id, projectId });
  });

  // Handle task updates
  socket.on('task-update', (data) => {
    socket.to(`project-${data.projectId}`).emit('task-updated', data);
  });

  // Handle project updates
  socket.on('project-update', (data) => {
    socket.to(`project-${data.projectId}`).emit('project-updated', data);
  });

  socket.on('disconnect', () => {
    logger.info('User disconnected', { socketId: socket.id });
  });
});

// Make io available to routes
app.set('io', io);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    status: 'error',
    message: 'Route not found',
    path: req.originalUrl,
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

const PORT = process.env['PORT'] || 8000;
const HOST = process.env['HOST'] || 'localhost';

server.listen(PORT, () => {
  logger.info(`🚀 Server running on http://${HOST}:${PORT}`);
  logger.info(`📚 API documentation available at http://${HOST}:${PORT}/api/docs`);
  logger.info(`🔌 WebSocket server ready for connections`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

export default app;
