/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"time"
)

// checks if the DocumentDetails type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &DocumentDetails{}

// DocumentDetails struct for DocumentDetails
type DocumentDetails struct {
	Id *string `json:"id,omitempty"`
	Content *string `json:"content,omitempty"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	FilterTags []string `json:"filter_tags,omitempty"`
	SourceId *string `json:"source_id,omitempty"`
	CreatedAt *time.Time `json:"created_at,omitempty"`
	UpdatedAt *time.Time `json:"updated_at,omitempty"`
	VectorEmbeddings []float32 `json:"vector_embeddings,omitempty"`
	ProcessingStatus *string `json:"processing_status,omitempty"`
}

// NewDocumentDetails instantiates a new DocumentDetails object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDocumentDetails() *DocumentDetails {
	this := DocumentDetails{}
	return &this
}

// NewDocumentDetailsWithDefaults instantiates a new DocumentDetails object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDocumentDetailsWithDefaults() *DocumentDetails {
	this := DocumentDetails{}
	return &this
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *DocumentDetails) GetId() string {
	if o == nil || IsNil(o.Id) {
		var ret string
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentDetails) GetIdOk() (*string, bool) {
	if o == nil || IsNil(o.Id) {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *DocumentDetails) HasId() bool {
	if o != nil && !IsNil(o.Id) {
		return true
	}

	return false
}

// SetId gets a reference to the given string and assigns it to the Id field.
func (o *DocumentDetails) SetId(v string) {
	o.Id = &v
}

// GetContent returns the Content field value if set, zero value otherwise.
func (o *DocumentDetails) GetContent() string {
	if o == nil || IsNil(o.Content) {
		var ret string
		return ret
	}
	return *o.Content
}

// GetContentOk returns a tuple with the Content field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentDetails) GetContentOk() (*string, bool) {
	if o == nil || IsNil(o.Content) {
		return nil, false
	}
	return o.Content, true
}

// HasContent returns a boolean if a field has been set.
func (o *DocumentDetails) HasContent() bool {
	if o != nil && !IsNil(o.Content) {
		return true
	}

	return false
}

// SetContent gets a reference to the given string and assigns it to the Content field.
func (o *DocumentDetails) SetContent(v string) {
	o.Content = &v
}

// GetMetadata returns the Metadata field value if set, zero value otherwise.
func (o *DocumentDetails) GetMetadata() map[string]interface{} {
	if o == nil || IsNil(o.Metadata) {
		var ret map[string]interface{}
		return ret
	}
	return o.Metadata
}

// GetMetadataOk returns a tuple with the Metadata field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentDetails) GetMetadataOk() (map[string]interface{}, bool) {
	if o == nil || IsNil(o.Metadata) {
		return map[string]interface{}{}, false
	}
	return o.Metadata, true
}

// HasMetadata returns a boolean if a field has been set.
func (o *DocumentDetails) HasMetadata() bool {
	if o != nil && !IsNil(o.Metadata) {
		return true
	}

	return false
}

// SetMetadata gets a reference to the given map[string]interface{} and assigns it to the Metadata field.
func (o *DocumentDetails) SetMetadata(v map[string]interface{}) {
	o.Metadata = v
}

// GetFilterTags returns the FilterTags field value if set, zero value otherwise.
func (o *DocumentDetails) GetFilterTags() []string {
	if o == nil || IsNil(o.FilterTags) {
		var ret []string
		return ret
	}
	return o.FilterTags
}

// GetFilterTagsOk returns a tuple with the FilterTags field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentDetails) GetFilterTagsOk() ([]string, bool) {
	if o == nil || IsNil(o.FilterTags) {
		return nil, false
	}
	return o.FilterTags, true
}

// HasFilterTags returns a boolean if a field has been set.
func (o *DocumentDetails) HasFilterTags() bool {
	if o != nil && !IsNil(o.FilterTags) {
		return true
	}

	return false
}

// SetFilterTags gets a reference to the given []string and assigns it to the FilterTags field.
func (o *DocumentDetails) SetFilterTags(v []string) {
	o.FilterTags = v
}

// GetSourceId returns the SourceId field value if set, zero value otherwise.
func (o *DocumentDetails) GetSourceId() string {
	if o == nil || IsNil(o.SourceId) {
		var ret string
		return ret
	}
	return *o.SourceId
}

// GetSourceIdOk returns a tuple with the SourceId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentDetails) GetSourceIdOk() (*string, bool) {
	if o == nil || IsNil(o.SourceId) {
		return nil, false
	}
	return o.SourceId, true
}

// HasSourceId returns a boolean if a field has been set.
func (o *DocumentDetails) HasSourceId() bool {
	if o != nil && !IsNil(o.SourceId) {
		return true
	}

	return false
}

// SetSourceId gets a reference to the given string and assigns it to the SourceId field.
func (o *DocumentDetails) SetSourceId(v string) {
	o.SourceId = &v
}

// GetCreatedAt returns the CreatedAt field value if set, zero value otherwise.
func (o *DocumentDetails) GetCreatedAt() time.Time {
	if o == nil || IsNil(o.CreatedAt) {
		var ret time.Time
		return ret
	}
	return *o.CreatedAt
}

// GetCreatedAtOk returns a tuple with the CreatedAt field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentDetails) GetCreatedAtOk() (*time.Time, bool) {
	if o == nil || IsNil(o.CreatedAt) {
		return nil, false
	}
	return o.CreatedAt, true
}

// HasCreatedAt returns a boolean if a field has been set.
func (o *DocumentDetails) HasCreatedAt() bool {
	if o != nil && !IsNil(o.CreatedAt) {
		return true
	}

	return false
}

// SetCreatedAt gets a reference to the given time.Time and assigns it to the CreatedAt field.
func (o *DocumentDetails) SetCreatedAt(v time.Time) {
	o.CreatedAt = &v
}

// GetUpdatedAt returns the UpdatedAt field value if set, zero value otherwise.
func (o *DocumentDetails) GetUpdatedAt() time.Time {
	if o == nil || IsNil(o.UpdatedAt) {
		var ret time.Time
		return ret
	}
	return *o.UpdatedAt
}

// GetUpdatedAtOk returns a tuple with the UpdatedAt field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentDetails) GetUpdatedAtOk() (*time.Time, bool) {
	if o == nil || IsNil(o.UpdatedAt) {
		return nil, false
	}
	return o.UpdatedAt, true
}

// HasUpdatedAt returns a boolean if a field has been set.
func (o *DocumentDetails) HasUpdatedAt() bool {
	if o != nil && !IsNil(o.UpdatedAt) {
		return true
	}

	return false
}

// SetUpdatedAt gets a reference to the given time.Time and assigns it to the UpdatedAt field.
func (o *DocumentDetails) SetUpdatedAt(v time.Time) {
	o.UpdatedAt = &v
}

// GetVectorEmbeddings returns the VectorEmbeddings field value if set, zero value otherwise.
func (o *DocumentDetails) GetVectorEmbeddings() []float32 {
	if o == nil || IsNil(o.VectorEmbeddings) {
		var ret []float32
		return ret
	}
	return o.VectorEmbeddings
}

// GetVectorEmbeddingsOk returns a tuple with the VectorEmbeddings field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentDetails) GetVectorEmbeddingsOk() ([]float32, bool) {
	if o == nil || IsNil(o.VectorEmbeddings) {
		return nil, false
	}
	return o.VectorEmbeddings, true
}

// HasVectorEmbeddings returns a boolean if a field has been set.
func (o *DocumentDetails) HasVectorEmbeddings() bool {
	if o != nil && !IsNil(o.VectorEmbeddings) {
		return true
	}

	return false
}

// SetVectorEmbeddings gets a reference to the given []float32 and assigns it to the VectorEmbeddings field.
func (o *DocumentDetails) SetVectorEmbeddings(v []float32) {
	o.VectorEmbeddings = v
}

// GetProcessingStatus returns the ProcessingStatus field value if set, zero value otherwise.
func (o *DocumentDetails) GetProcessingStatus() string {
	if o == nil || IsNil(o.ProcessingStatus) {
		var ret string
		return ret
	}
	return *o.ProcessingStatus
}

// GetProcessingStatusOk returns a tuple with the ProcessingStatus field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentDetails) GetProcessingStatusOk() (*string, bool) {
	if o == nil || IsNil(o.ProcessingStatus) {
		return nil, false
	}
	return o.ProcessingStatus, true
}

// HasProcessingStatus returns a boolean if a field has been set.
func (o *DocumentDetails) HasProcessingStatus() bool {
	if o != nil && !IsNil(o.ProcessingStatus) {
		return true
	}

	return false
}

// SetProcessingStatus gets a reference to the given string and assigns it to the ProcessingStatus field.
func (o *DocumentDetails) SetProcessingStatus(v string) {
	o.ProcessingStatus = &v
}

func (o DocumentDetails) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o DocumentDetails) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Id) {
		toSerialize["id"] = o.Id
	}
	if !IsNil(o.Content) {
		toSerialize["content"] = o.Content
	}
	if !IsNil(o.Metadata) {
		toSerialize["metadata"] = o.Metadata
	}
	if !IsNil(o.FilterTags) {
		toSerialize["filter_tags"] = o.FilterTags
	}
	if !IsNil(o.SourceId) {
		toSerialize["source_id"] = o.SourceId
	}
	if !IsNil(o.CreatedAt) {
		toSerialize["created_at"] = o.CreatedAt
	}
	if !IsNil(o.UpdatedAt) {
		toSerialize["updated_at"] = o.UpdatedAt
	}
	if !IsNil(o.VectorEmbeddings) {
		toSerialize["vector_embeddings"] = o.VectorEmbeddings
	}
	if !IsNil(o.ProcessingStatus) {
		toSerialize["processing_status"] = o.ProcessingStatus
	}
	return toSerialize, nil
}

type NullableDocumentDetails struct {
	value *DocumentDetails
	isSet bool
}

func (v NullableDocumentDetails) Get() *DocumentDetails {
	return v.value
}

func (v *NullableDocumentDetails) Set(val *DocumentDetails) {
	v.value = val
	v.isSet = true
}

func (v NullableDocumentDetails) IsSet() bool {
	return v.isSet
}

func (v *NullableDocumentDetails) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDocumentDetails(val *DocumentDetails) *NullableDocumentDetails {
	return &NullableDocumentDetails{value: val, isSet: true}
}

func (v NullableDocumentDetails) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDocumentDetails) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


