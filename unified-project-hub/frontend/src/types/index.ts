// Re-export shared types
export * from '../../../shared/types'

// Frontend-specific types
export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  email: string
  password: string
  name: string
  role?: 'team_member' | 'requirements_analyst'
  title?: string
  department?: string
}

export interface AuthResponse {
  user: User
  accessToken: string
  refreshToken: string
}

export interface RefreshTokenRequest {
  refreshToken: string
}

// UI State types
export interface TableState {
  page: number
  limit: number
  search: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  filters: Record<string, any>
}

export interface SelectOption {
  value: string
  label: string
  disabled?: boolean
}

export interface NavigationItem {
  label: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: number
  roles: UserRole[]
  children?: NavigationItem[]
}

// Form types
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'textarea' | 'select' | 'multiselect' | 'date' | 'checkbox' | 'number'
  placeholder?: string
  required?: boolean
  options?: SelectOption[]
  validation?: any
}

// Dashboard types
export interface DashboardStats {
  totalProjects: number
  activeProjects: number
  completedProjects: number
  totalTasks: number
  completedTasks: number
  overdueTasks: number
  totalRequirements: number
  approvedRequirements: number
  pendingRequirements: number
}

export interface DashboardChart {
  name: string
  value: number
  color?: string
}

// Notification types
export interface Notification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  read: boolean
  created_at: string
  action_url?: string
}

// File upload types
export interface FileUpload {
  file: File
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  url?: string
  error?: string
}

// Search types
export interface SearchResult {
  id: string
  title: string
  description: string
  type: 'project' | 'task' | 'requirement' | 'company' | 'contact'
  url: string
  metadata?: Record<string, any>
}

// Filter types
export interface FilterOption {
  key: string
  label: string
  type: 'select' | 'multiselect' | 'date' | 'daterange' | 'text' | 'number'
  options?: SelectOption[]
  placeholder?: string
}

// Export/Import types
export interface ExportOptions {
  format: 'csv' | 'xlsx' | 'pdf'
  fields: string[]
  filters?: Record<string, any>
}

export interface ImportResult {
  success: number
  errors: number
  warnings: number
  details: Array<{
    row: number
    message: string
    type: 'error' | 'warning'
  }>
}

// WebSocket types
export interface WebSocketMessage {
  type: string
  payload: any
  timestamp: string
}

export interface RealTimeUpdate {
  type: 'project_updated' | 'task_updated' | 'requirement_updated' | 'interaction_created'
  data: any
  user_id: string
  timestamp: string
}

// Theme types
export type Theme = 'light' | 'dark' | 'system'

// Error types
export interface ApiError {
  status: number
  message: string
  details?: any
  timestamp: string
  path: string
}

// Loading states
export interface LoadingState {
  isLoading: boolean
  error?: string | null
}

// Pagination types
export interface PaginationInfo {
  page: number
  limit: number
  total: number
  pages: number
  has_more: boolean
}

// Sort types
export interface SortConfig {
  key: string
  direction: 'asc' | 'desc'
}

// Modal types
export interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
}

// Toast types
export interface ToastOptions {
  title: string
  description?: string
  variant?: 'default' | 'destructive' | 'success' | 'warning'
  duration?: number
}

// Component props types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface IconProps {
  className?: string
  size?: number | string
}

// Re-export User type with frontend-specific additions
export interface User extends Omit<TeamMember, 'assigned_project_ids' | 'leading_project_ids'> {
  // Add any frontend-specific user properties here
}
