// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// ENUMS
// ============================================================================

enum ProjectType {
  internal
  external
  hybrid
}

enum ProjectStatus {
  draft
  requirements
  backlog
  not_started
  in_progress
  completed
  archived
  on_hold
}

enum TaskStatus {
  to_do
  in_progress
  done
  blocked
}

enum PriorityLevel {
  P0
  P1
  P2
  P3
  P4
}

enum EffortEstimate {
  XS
  S
  M
  L
  XL
  XXL
}

enum ImpactType {
  Revenue
  Platform
  Bug_Fix
  R_D
  Customer_Success
}

enum UserRole {
  admin
  project_manager
  team_member
  customer
  requirements_analyst
}

enum CompanyStatus {
  prospect
  active
  inactive
  archived
}

enum ContactRole {
  primary
  technical
  business
  stakeholder
}

enum InteractionType {
  email
  call
  meeting
  note
  task_update
  milestone
}

enum RequirementStatus {
  draft
  review
  approved
  implemented
  tested
}

enum RequirementType {
  functional
  non_functional
  business
  technical
}

// ============================================================================
// CORE MODELS
// ============================================================================

model User {
  id                            String   @id @default(uuid())
  email                         String   @unique
  password_hash                 String
  name                          String
  role                          UserRole
  title                         String?
  department                    String?
  avatar_url                    String?
  is_active                     Boolean  @default(true)
  weekly_capacity_hours         Int?
  current_utilization_percentage Int?
  skills                        String[]
  created_at                    DateTime @default(now())
  updated_at                    DateTime @updatedAt

  // Relations
  led_projects                  Project[]           @relation("ProjectLead")
  assigned_projects             ProjectTeamMember[]
  assigned_tasks                Task[]              @relation("TaskAssignee")
  reviewed_tasks                Task[]              @relation("TaskReviewer")
  created_requirements          Requirement[]       @relation("RequirementCreator")
  assigned_requirements         Requirement[]       @relation("RequirementAssignee")
  approved_requirements         Requirement[]       @relation("RequirementApprover")
  created_interactions          Interaction[]       @relation("InteractionCreator")
  interaction_participants      InteractionParticipant[]

  @@map("users")
}

model Company {
  id                      String        @id @default(uuid())
  name                    String
  status                  CompanyStatus @default(prospect)
  industry                String?
  size                    String?
  website                 String?
  address                 String?
  phone                   String?
  email                   String?
  description             String?
  research_notes          String?
  deal_value              Decimal?
  deal_stage              String?
  last_interaction_date   DateTime?
  active_projects_count   Int           @default(0)
  total_projects_count    Int           @default(0)
  created_at              DateTime      @default(now())
  updated_at              DateTime      @updatedAt

  // Relations
  projects                Project[]
  contacts                Contact[]
  interactions            Interaction[]

  @@map("companies")
}

model Contact {
  id                        String       @id @default(uuid())
  company_id                String
  name                      String
  email                     String
  phone                     String?
  role                      ContactRole
  title                     String?
  department                String?
  is_primary                Boolean      @default(false)
  is_active                 Boolean      @default(true)
  preferred_contact_method  String?
  timezone                  String?
  created_at                DateTime     @default(now())
  updated_at                DateTime     @updatedAt

  // Relations
  company                   Company      @relation(fields: [company_id], references: [id], onDelete: Cascade)
  customer_projects         Project[]    @relation("CustomerContact")
  interactions              Interaction[]

  @@map("contacts")
}

model Project {
  id                                String            @id @default(uuid())
  name                              String
  description                       String?
  type                              ProjectType
  status                            ProjectStatus     @default(draft)
  company_id                        String?
  company_name                      String
  customer_contact_id               String?
  project_lead_id                   String
  priority_level                    PriorityLevel     @default(P3)
  effort_estimate                   EffortEstimate    @default(M)
  impact_type                       ImpactType        @default(Platform)
  start_date                        DateTime?
  end_date                          DateTime?
  original_end_date                 DateTime?
  prd_document_link                 String?
  requirements_status               RequirementStatus @default(draft)
  requirements_completion_percentage Int              @default(0)
  poc_url                           String?
  repository_url                    String?
  design_url                        String?
  progress_percentage               Int               @default(0)
  budget                            Decimal?
  actual_cost                       Decimal?
  is_customer_visible               Boolean           @default(false)
  customer_visible_fields           String[]
  tags                              String[]
  custom_fields                     Json?
  created_at                        DateTime          @default(now())
  updated_at                        DateTime          @updatedAt
  completed_at                      DateTime?
  archived_at                       DateTime?

  // Relations
  company                           Company?          @relation(fields: [company_id], references: [id])
  customer_contact                  Contact?          @relation("CustomerContact", fields: [customer_contact_id], references: [id])
  project_lead                      User              @relation("ProjectLead", fields: [project_lead_id], references: [id])
  team_members                      ProjectTeamMember[]
  tasks                             Task[]
  requirements                      Requirement[]
  interactions                      Interaction[]

  @@map("projects")
}

model ProjectTeamMember {
  id         String   @id @default(uuid())
  project_id String
  user_id    String
  created_at DateTime @default(now())

  // Relations
  project    Project  @relation(fields: [project_id], references: [id], onDelete: Cascade)
  user       User     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([project_id, user_id])
  @@map("project_team_members")
}

model Task {
  id                    String         @id @default(uuid())
  project_id            String
  parent_task_id        String?
  name                  String
  description           String?
  status                TaskStatus     @default(to_do)
  priority_level        PriorityLevel  @default(P3)
  effort_estimate       EffortEstimate @default(M)
  assignee_id           String
  reviewer_id           String?
  due_date              DateTime?
  start_date            DateTime?
  is_customer_visible   Boolean        @default(false)
  customer_description  String?
  tags                  String[]
  estimated_hours       Int?
  actual_hours          Int?
  attachment_urls       String[]
  external_links        String[]
  created_at            DateTime       @default(now())
  updated_at            DateTime       @updatedAt
  completed_at          DateTime?

  // Relations
  project               Project        @relation(fields: [project_id], references: [id], onDelete: Cascade)
  parent_task           Task?          @relation("TaskSubtasks", fields: [parent_task_id], references: [id])
  sub_tasks             Task[]         @relation("TaskSubtasks")
  assignee              User           @relation("TaskAssignee", fields: [assignee_id], references: [id])
  reviewer              User?          @relation("TaskReviewer", fields: [reviewer_id], references: [id])
  task_requirements     TaskRequirement[]
  depends_on            TaskDependency[] @relation("DependentTask")
  blocks                TaskDependency[] @relation("BlockingTask")

  @@map("tasks")
}

model TaskDependency {
  id                String @id @default(uuid())
  dependent_task_id String
  blocking_task_id  String

  // Relations
  dependent_task    Task   @relation("DependentTask", fields: [dependent_task_id], references: [id], onDelete: Cascade)
  blocking_task     Task   @relation("BlockingTask", fields: [blocking_task_id], references: [id], onDelete: Cascade)

  @@unique([dependent_task_id, blocking_task_id])
  @@map("task_dependencies")
}

model Requirement {
  id                      String            @id @default(uuid())
  project_id              String
  title                   String
  description             String
  type                    RequirementType
  status                  RequirementStatus @default(draft)
  priority_level          PriorityLevel     @default(P3)
  detailed_specification  String?
  acceptance_criteria     String[]
  user_stories            String[]
  created_by_id           String
  assigned_to_id          String?
  approved_by_id          String?
  implementation_notes    String?
  mockup_urls             String[]
  diagram_urls            String[]
  reference_urls          String[]
  created_at              DateTime          @default(now())
  updated_at              DateTime          @updatedAt
  approved_at             DateTime?

  // Relations
  project                 Project           @relation(fields: [project_id], references: [id], onDelete: Cascade)
  created_by              User              @relation("RequirementCreator", fields: [created_by_id], references: [id])
  assigned_to             User?             @relation("RequirementAssignee", fields: [assigned_to_id], references: [id])
  approved_by             User?             @relation("RequirementApprover", fields: [approved_by_id], references: [id])
  task_requirements       TaskRequirement[]

  @@map("requirements")
}

model TaskRequirement {
  id             String @id @default(uuid())
  task_id        String
  requirement_id String

  // Relations
  task           Task   @relation(fields: [task_id], references: [id], onDelete: Cascade)
  requirement    Requirement @relation(fields: [requirement_id], references: [id], onDelete: Cascade)

  @@unique([task_id, requirement_id])
  @@map("task_requirements")
}

model Interaction {
  id                String                   @id @default(uuid())
  project_id        String?
  company_id        String?
  contact_id        String?
  type              InteractionType
  subject           String
  content           String
  created_by_id     String
  interaction_date  DateTime
  is_customer_visible Boolean               @default(false)
  tags              String[]
  attachment_urls   String[]
  recording_url     String?
  transcript_url    String?
  created_at        DateTime                 @default(now())
  updated_at        DateTime                 @updatedAt

  // Relations
  project           Project?                 @relation(fields: [project_id], references: [id])
  company           Company?                 @relation(fields: [company_id], references: [id])
  contact           Contact?                 @relation(fields: [contact_id], references: [id])
  created_by        User                     @relation("InteractionCreator", fields: [created_by_id], references: [id])
  participants      InteractionParticipant[]

  @@map("interactions")
}

model InteractionParticipant {
  id             String @id @default(uuid())
  interaction_id String
  user_id        String

  // Relations
  interaction    Interaction @relation(fields: [interaction_id], references: [id], onDelete: Cascade)
  user           User        @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([interaction_id, user_id])
  @@map("interaction_participants")
}
