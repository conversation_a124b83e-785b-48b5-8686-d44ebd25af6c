# InteractionsApi

All URIs are relative to *https://api.crm.example.com/v1*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**interactionsGet**](InteractionsApi.md#interactionsGet) | **GET** /interactions | List interactions |
| [**interactionsIdDelete**](InteractionsApi.md#interactionsIdDelete) | **DELETE** /interactions/{id} | Delete interaction |
| [**interactionsIdGet**](InteractionsApi.md#interactionsIdGet) | **GET** /interactions/{id} | Get interaction |
| [**interactionsIdPut**](InteractionsApi.md#interactionsIdPut) | **PUT** /interactions/{id} | Update interaction |
| [**interactionsPost**](InteractionsApi.md#interactionsPost) | **POST** /interactions | Create interaction |


<a id="interactionsGet"></a>
# **interactionsGet**
> InteractionsGet200Response interactionsGet(companyId, contactId, interactionType, fromDate, toDate, limit, offset)

List interactions

Retrieve a list of all interactions

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.InteractionsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    InteractionsApi apiInstance = new InteractionsApi(defaultClient);
    UUID companyId = UUID.randomUUID(); // UUID | Filter by company ID
    UUID contactId = UUID.randomUUID(); // UUID | Filter by contact ID
    String interactionType = "interactionType_example"; // String | Filter by interaction type
    OffsetDateTime fromDate = OffsetDateTime.now(); // OffsetDateTime | Filter interactions from this date
    OffsetDateTime toDate = OffsetDateTime.now(); // OffsetDateTime | Filter interactions to this date
    Integer limit = 20; // Integer | Number of interactions to return
    Integer offset = 0; // Integer | Number of interactions to skip
    try {
      InteractionsGet200Response result = apiInstance.interactionsGet(companyId, contactId, interactionType, fromDate, toDate, limit, offset);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling InteractionsApi#interactionsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **companyId** | **UUID**| Filter by company ID | [optional] |
| **contactId** | **UUID**| Filter by contact ID | [optional] |
| **interactionType** | **String**| Filter by interaction type | [optional] |
| **fromDate** | **OffsetDateTime**| Filter interactions from this date | [optional] |
| **toDate** | **OffsetDateTime**| Filter interactions to this date | [optional] |
| **limit** | **Integer**| Number of interactions to return | [optional] [default to 20] |
| **offset** | **Integer**| Number of interactions to skip | [optional] [default to 0] |

### Return type

[**InteractionsGet200Response**](InteractionsGet200Response.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | List of interactions |  -  |

<a id="interactionsIdDelete"></a>
# **interactionsIdDelete**
> interactionsIdDelete(id)

Delete interaction

Delete an interaction record

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.InteractionsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    InteractionsApi apiInstance = new InteractionsApi(defaultClient);
    UUID id = UUID.randomUUID(); // UUID | 
    try {
      apiInstance.interactionsIdDelete(id);
    } catch (ApiException e) {
      System.err.println("Exception when calling InteractionsApi#interactionsIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **id** | **UUID**|  | |

### Return type

null (empty response body)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **204** | Interaction deleted successfully |  -  |
| **404** | Interaction not found |  -  |

<a id="interactionsIdGet"></a>
# **interactionsIdGet**
> InteractionDetails interactionsIdGet(id)

Get interaction

Retrieve a specific interaction by ID

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.InteractionsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    InteractionsApi apiInstance = new InteractionsApi(defaultClient);
    UUID id = UUID.randomUUID(); // UUID | 
    try {
      InteractionDetails result = apiInstance.interactionsIdGet(id);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling InteractionsApi#interactionsIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **id** | **UUID**|  | |

### Return type

[**InteractionDetails**](InteractionDetails.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Interaction details |  -  |
| **404** | Interaction not found |  -  |

<a id="interactionsIdPut"></a>
# **interactionsIdPut**
> Interaction interactionsIdPut(id, interactionUpdate)

Update interaction

Update an existing interaction

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.InteractionsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    InteractionsApi apiInstance = new InteractionsApi(defaultClient);
    UUID id = UUID.randomUUID(); // UUID | 
    InteractionUpdate interactionUpdate = new InteractionUpdate(); // InteractionUpdate | 
    try {
      Interaction result = apiInstance.interactionsIdPut(id, interactionUpdate);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling InteractionsApi#interactionsIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **id** | **UUID**|  | |
| **interactionUpdate** | [**InteractionUpdate**](InteractionUpdate.md)|  | |

### Return type

[**Interaction**](Interaction.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Interaction updated successfully |  -  |
| **404** | Interaction not found |  -  |

<a id="interactionsPost"></a>
# **interactionsPost**
> Interaction interactionsPost(interactionCreate)

Create interaction

Create a new interaction record

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.InteractionsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    InteractionsApi apiInstance = new InteractionsApi(defaultClient);
    InteractionCreate interactionCreate = new InteractionCreate(); // InteractionCreate | 
    try {
      Interaction result = apiInstance.interactionsPost(interactionCreate);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling InteractionsApi#interactionsPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **interactionCreate** | [**InteractionCreate**](InteractionCreate.md)|  | |

### Return type

[**Interaction**](Interaction.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **201** | Interaction created successfully |  -  |
| **400** | Invalid input |  -  |

