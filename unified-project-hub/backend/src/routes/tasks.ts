import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireRole, canAccessProject } from '../middleware/auth';

const router = Router();
const prisma = new PrismaClient();

// Validation schemas
const createTaskSchema = z.object({
  project_id: z.string().uuid('Invalid project ID'),
  parent_task_id: z.string().uuid().optional(),
  name: z.string().min(1, 'Task name is required'),
  description: z.string().optional(),
  priority_level: z.enum(['P0', 'P1', 'P2', 'P3', 'P4']).default('P3'),
  effort_estimate: z.enum(['XS', 'S', 'M', 'L', 'XL', 'XXL']).default('M'),
  assignee_id: z.string().uuid('Invalid assignee ID'),
  reviewer_id: z.string().uuid().optional(),
  due_date: z.string().datetime().optional(),
  is_customer_visible: z.boolean().default(false),
  tags: z.array(z.string()).default([]),
  estimated_hours: z.number().positive().optional(),
});

/**
 * @route GET /api/tasks
 * @desc Get tasks (filtered by user role and permissions)
 * @access Private
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { page = '1', limit = '10', status, project_id, assignee_id } = req.query;
  const skip = (parseInt(page as string) - 1) * parseInt(limit as string);

  let whereClause: any = {};

  // Filter based on user role
  if (req.user?.role === 'customer') {
    whereClause.is_customer_visible = true;
    whereClause.project = {
      customer_contact: { email: req.user.email },
    };
  } else if (req.user?.role === 'team_member') {
    whereClause.OR = [
      { assignee_id: req.user.id },
      { reviewer_id: req.user.id },
      { project: { project_lead_id: req.user.id } },
      { project: { team_members: { some: { user_id: req.user.id } } } },
    ];
  }

  // Apply filters
  if (status) whereClause.status = status;
  if (project_id) whereClause.project_id = project_id;
  if (assignee_id) whereClause.assignee_id = assignee_id;

  const [tasks, total] = await Promise.all([
    prisma.task.findMany({
      where: whereClause,
      include: {
        project: {
          select: { id: true, name: true, company_name: true },
        },
        assignee: {
          select: { id: true, name: true, email: true, avatar_url: true },
        },
        reviewer: {
          select: { id: true, name: true, email: true, avatar_url: true },
        },
        sub_tasks: {
          include: {
            assignee: {
              select: { id: true, name: true, email: true, avatar_url: true },
            },
          },
        },
      },
      skip,
      take: parseInt(limit as string),
      orderBy: { updated_at: 'desc' },
    }),
    prisma.task.count({ where: whereClause }),
  ]);

  res.json({
    status: 'success',
    data: {
      tasks,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        pages: Math.ceil(total / parseInt(limit as string)),
      },
    },
  });
}));

/**
 * @route POST /api/tasks
 * @desc Create new task
 * @access Private
 */
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createTaskSchema.parse(req.body);

  // Check if user can access the project
  const project = await prisma.project.findFirst({
    where: {
      id: validatedData.project_id,
      OR: [
        { project_lead_id: req.user!.id },
        { team_members: { some: { user_id: req.user!.id } } },
        ...(req.user!.role === 'admin' || req.user!.role === 'project_manager' ? [{}] : []),
      ],
    },
  });

  if (!project) {
    return res.status(404).json({
      status: 'error',
      message: 'Project not found or access denied',
    });
  }

  const task = await prisma.task.create({
    data: {
      ...validatedData,
      due_date: validatedData.due_date ? new Date(validatedData.due_date) : undefined,
    },
    include: {
      project: {
        select: { id: true, name: true, company_name: true },
      },
      assignee: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
      reviewer: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
    },
  });

  res.status(201).json({
    status: 'success',
    message: 'Task created successfully',
    data: { task },
  });
}));

/**
 * @route PUT /api/tasks/:id
 * @desc Update task
 * @access Private
 */
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const updateData = createTaskSchema.partial().parse(req.body);

  // Check if user can update this task
  const existingTask = await prisma.task.findFirst({
    where: {
      id,
      OR: [
        { assignee_id: req.user!.id },
        { reviewer_id: req.user!.id },
        { project: { project_lead_id: req.user!.id } },
        { project: { team_members: { some: { user_id: req.user!.id } } } },
        ...(req.user!.role === 'admin' || req.user!.role === 'project_manager' ? [{}] : []),
      ],
    },
  });

  if (!existingTask) {
    return res.status(404).json({
      status: 'error',
      message: 'Task not found or access denied',
    });
  }

  const task = await prisma.task.update({
    where: { id },
    data: {
      ...updateData,
      due_date: updateData.due_date ? new Date(updateData.due_date) : undefined,
      completed_at: updateData.status === 'done' ? new Date() : undefined,
    },
    include: {
      project: {
        select: { id: true, name: true, company_name: true },
      },
      assignee: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
      reviewer: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
    },
  });

  res.json({
    status: 'success',
    message: 'Task updated successfully',
    data: { task },
  });
}));

/**
 * @route DELETE /api/tasks/:id
 * @desc Delete task
 * @access Private
 */
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if user can delete this task
  const existingTask = await prisma.task.findFirst({
    where: {
      id,
      OR: [
        { project: { project_lead_id: req.user!.id } },
        ...(req.user!.role === 'admin' || req.user!.role === 'project_manager' ? [{}] : []),
      ],
    },
  });

  if (!existingTask) {
    return res.status(404).json({
      status: 'error',
      message: 'Task not found or access denied',
    });
  }

  await prisma.task.delete({
    where: { id },
  });

  res.json({
    status: 'success',
    message: 'Task deleted successfully',
  });
}));

export default router;
