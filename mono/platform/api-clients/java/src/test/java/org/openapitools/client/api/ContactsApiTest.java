/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.api;

import org.openapitools.client.ApiException;
import org.openapitools.client.model.Contact;
import org.openapitools.client.model.ContactCompanyUpdateRequest;
import org.openapitools.client.model.ContactCreate;
import org.openapitools.client.model.ContactDetails;
import org.openapitools.client.model.ContactUpdate;
import org.openapitools.client.model.ContactWithCompany;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for ContactsApi
 */
@Disabled
public class ContactsApiTest {

    private final ContactsApi api = new ContactsApi();

    /**
     * List contacts
     *
     * Retrieve a list of all active contacts with their associated companies
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void contactsGetTest() throws ApiException {
        UUID companyId = null;
        Boolean unlinked = null;
        Boolean includeDeleted = null;
        List<ContactWithCompany> response = api.contactsGet(companyId, unlinked, includeDeleted);
        // TODO: test validations
    }

    /**
     * Unlink contact from company
     *
     * Remove the association between a contact and company
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void contactsIdCompanyDeleteTest() throws ApiException {
        UUID id = null;
        api.contactsIdCompanyDelete(id);
        // TODO: test validations
    }

    /**
     * Link contact to company
     *
     * Associate a contact with a company
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void contactsIdCompanyPutTest() throws ApiException {
        UUID id = null;
        ContactCompanyUpdateRequest contactCompanyUpdateRequest = null;
        api.contactsIdCompanyPut(id, contactCompanyUpdateRequest);
        // TODO: test validations
    }

    /**
     * Delete contact
     *
     * Soft delete a contact (sets is_deleted to true)
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void contactsIdDeleteTest() throws ApiException {
        UUID id = null;
        api.contactsIdDelete(id);
        // TODO: test validations
    }

    /**
     * Get contact
     *
     * Retrieve a specific contact by ID
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void contactsIdGetTest() throws ApiException {
        UUID id = null;
        ContactDetails response = api.contactsIdGet(id);
        // TODO: test validations
    }

    /**
     * Update contact
     *
     * Update an existing contact
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void contactsIdPutTest() throws ApiException {
        UUID id = null;
        ContactUpdate contactUpdate = null;
        Contact response = api.contactsIdPut(id, contactUpdate);
        // TODO: test validations
    }

    /**
     * Create contact
     *
     * Create a new contact
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void contactsPostTest() throws ApiException {
        ContactCreate contactCreate = null;
        Contact response = api.contactsPost(contactCreate);
        // TODO: test validations
    }

}
