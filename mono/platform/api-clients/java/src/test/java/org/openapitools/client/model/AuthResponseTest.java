/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.util.Arrays;
import org.openapitools.client.model.User;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

/**
 * Model tests for AuthResponse
 */
public class AuthResponseTest {
    private final AuthResponse model = new AuthResponse();

    /**
     * Model tests for AuthResponse
     */
    @Test
    public void testAuthResponse() {
        // TODO: test AuthResponse
    }

    /**
     * Test the property 'accessToken'
     */
    @Test
    public void accessTokenTest() {
        // TODO: test accessToken
    }

    /**
     * Test the property 'refreshToken'
     */
    @Test
    public void refreshTokenTest() {
        // TODO: test refreshToken
    }

    /**
     * Test the property 'expiresIn'
     */
    @Test
    public void expiresInTest() {
        // TODO: test expiresIn
    }

    /**
     * Test the property 'tokenType'
     */
    @Test
    public void tokenTypeTest() {
        // TODO: test tokenType
    }

    /**
     * Test the property 'user'
     */
    @Test
    public void userTest() {
        // TODO: test user
    }

}
