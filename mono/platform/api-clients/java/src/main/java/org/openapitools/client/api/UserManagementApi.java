/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.api;

import org.openapitools.client.ApiCallback;
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.ApiResponse;
import org.openapitools.client.Configuration;
import org.openapitools.client.Pair;
import org.openapitools.client.ProgressRequestBody;
import org.openapitools.client.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import org.openapitools.client.model.UserProfile;
import org.openapitools.client.model.UserProfileUpdate;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UserManagementApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public UserManagementApi() {
        this(Configuration.getDefaultApiClient());
    }

    public UserManagementApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for usersProfileGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> User profile </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authenticated </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call usersProfileGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/users/profile";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call usersProfileGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return usersProfileGetCall(_callback);

    }

    /**
     * Get user profile
     * Retrieve the current user&#39;s profile information
     * @return UserProfile
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> User profile </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authenticated </td><td>  -  </td></tr>
     </table>
     */
    public UserProfile usersProfileGet() throws ApiException {
        ApiResponse<UserProfile> localVarResp = usersProfileGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Get user profile
     * Retrieve the current user&#39;s profile information
     * @return ApiResponse&lt;UserProfile&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> User profile </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authenticated </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<UserProfile> usersProfileGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = usersProfileGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<UserProfile>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get user profile (asynchronously)
     * Retrieve the current user&#39;s profile information
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> User profile </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authenticated </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call usersProfileGetAsync(final ApiCallback<UserProfile> _callback) throws ApiException {

        okhttp3.Call localVarCall = usersProfileGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<UserProfile>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for usersProfilePut
     * @param userProfileUpdate  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Profile updated successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call usersProfilePutCall(UserProfileUpdate userProfileUpdate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = userProfileUpdate;

        // create path and map variables
        String localVarPath = "/users/profile";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call usersProfilePutValidateBeforeCall(UserProfileUpdate userProfileUpdate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'userProfileUpdate' is set
        if (userProfileUpdate == null) {
            throw new ApiException("Missing the required parameter 'userProfileUpdate' when calling usersProfilePut(Async)");
        }

        return usersProfilePutCall(userProfileUpdate, _callback);

    }

    /**
     * Update user profile
     * Update the current user&#39;s profile information
     * @param userProfileUpdate  (required)
     * @return UserProfile
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Profile updated successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public UserProfile usersProfilePut(UserProfileUpdate userProfileUpdate) throws ApiException {
        ApiResponse<UserProfile> localVarResp = usersProfilePutWithHttpInfo(userProfileUpdate);
        return localVarResp.getData();
    }

    /**
     * Update user profile
     * Update the current user&#39;s profile information
     * @param userProfileUpdate  (required)
     * @return ApiResponse&lt;UserProfile&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Profile updated successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<UserProfile> usersProfilePutWithHttpInfo(UserProfileUpdate userProfileUpdate) throws ApiException {
        okhttp3.Call localVarCall = usersProfilePutValidateBeforeCall(userProfileUpdate, null);
        Type localVarReturnType = new TypeToken<UserProfile>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Update user profile (asynchronously)
     * Update the current user&#39;s profile information
     * @param userProfileUpdate  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Profile updated successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call usersProfilePutAsync(UserProfileUpdate userProfileUpdate, final ApiCallback<UserProfile> _callback) throws ApiException {

        okhttp3.Call localVarCall = usersProfilePutValidateBeforeCall(userProfileUpdate, _callback);
        Type localVarReturnType = new TypeToken<UserProfile>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
