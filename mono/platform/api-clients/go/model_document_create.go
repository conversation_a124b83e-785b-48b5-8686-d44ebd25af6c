/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"bytes"
	"fmt"
)

// checks if the DocumentCreate type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &DocumentCreate{}

// DocumentCreate struct for DocumentCreate
type DocumentCreate struct {
	Content string `json:"content"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	FilterTags []string `json:"filter_tags,omitempty"`
	SourceId *string `json:"source_id,omitempty"`
}

type _DocumentCreate DocumentCreate

// NewDocumentCreate instantiates a new DocumentCreate object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDocumentCreate(content string) *DocumentCreate {
	this := DocumentCreate{}
	this.Content = content
	return &this
}

// NewDocumentCreateWithDefaults instantiates a new DocumentCreate object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDocumentCreateWithDefaults() *DocumentCreate {
	this := DocumentCreate{}
	return &this
}

// GetContent returns the Content field value
func (o *DocumentCreate) GetContent() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Content
}

// GetContentOk returns a tuple with the Content field value
// and a boolean to check if the value has been set.
func (o *DocumentCreate) GetContentOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Content, true
}

// SetContent sets field value
func (o *DocumentCreate) SetContent(v string) {
	o.Content = v
}

// GetMetadata returns the Metadata field value if set, zero value otherwise.
func (o *DocumentCreate) GetMetadata() map[string]interface{} {
	if o == nil || IsNil(o.Metadata) {
		var ret map[string]interface{}
		return ret
	}
	return o.Metadata
}

// GetMetadataOk returns a tuple with the Metadata field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentCreate) GetMetadataOk() (map[string]interface{}, bool) {
	if o == nil || IsNil(o.Metadata) {
		return map[string]interface{}{}, false
	}
	return o.Metadata, true
}

// HasMetadata returns a boolean if a field has been set.
func (o *DocumentCreate) HasMetadata() bool {
	if o != nil && !IsNil(o.Metadata) {
		return true
	}

	return false
}

// SetMetadata gets a reference to the given map[string]interface{} and assigns it to the Metadata field.
func (o *DocumentCreate) SetMetadata(v map[string]interface{}) {
	o.Metadata = v
}

// GetFilterTags returns the FilterTags field value if set, zero value otherwise.
func (o *DocumentCreate) GetFilterTags() []string {
	if o == nil || IsNil(o.FilterTags) {
		var ret []string
		return ret
	}
	return o.FilterTags
}

// GetFilterTagsOk returns a tuple with the FilterTags field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentCreate) GetFilterTagsOk() ([]string, bool) {
	if o == nil || IsNil(o.FilterTags) {
		return nil, false
	}
	return o.FilterTags, true
}

// HasFilterTags returns a boolean if a field has been set.
func (o *DocumentCreate) HasFilterTags() bool {
	if o != nil && !IsNil(o.FilterTags) {
		return true
	}

	return false
}

// SetFilterTags gets a reference to the given []string and assigns it to the FilterTags field.
func (o *DocumentCreate) SetFilterTags(v []string) {
	o.FilterTags = v
}

// GetSourceId returns the SourceId field value if set, zero value otherwise.
func (o *DocumentCreate) GetSourceId() string {
	if o == nil || IsNil(o.SourceId) {
		var ret string
		return ret
	}
	return *o.SourceId
}

// GetSourceIdOk returns a tuple with the SourceId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentCreate) GetSourceIdOk() (*string, bool) {
	if o == nil || IsNil(o.SourceId) {
		return nil, false
	}
	return o.SourceId, true
}

// HasSourceId returns a boolean if a field has been set.
func (o *DocumentCreate) HasSourceId() bool {
	if o != nil && !IsNil(o.SourceId) {
		return true
	}

	return false
}

// SetSourceId gets a reference to the given string and assigns it to the SourceId field.
func (o *DocumentCreate) SetSourceId(v string) {
	o.SourceId = &v
}

func (o DocumentCreate) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o DocumentCreate) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["content"] = o.Content
	if !IsNil(o.Metadata) {
		toSerialize["metadata"] = o.Metadata
	}
	if !IsNil(o.FilterTags) {
		toSerialize["filter_tags"] = o.FilterTags
	}
	if !IsNil(o.SourceId) {
		toSerialize["source_id"] = o.SourceId
	}
	return toSerialize, nil
}

func (o *DocumentCreate) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"content",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varDocumentCreate := _DocumentCreate{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varDocumentCreate)

	if err != nil {
		return err
	}

	*o = DocumentCreate(varDocumentCreate)

	return err
}

type NullableDocumentCreate struct {
	value *DocumentCreate
	isSet bool
}

func (v NullableDocumentCreate) Get() *DocumentCreate {
	return v.value
}

func (v *NullableDocumentCreate) Set(val *DocumentCreate) {
	v.value = val
	v.isSet = true
}

func (v NullableDocumentCreate) IsSet() bool {
	return v.isSet
}

func (v *NullableDocumentCreate) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDocumentCreate(val *DocumentCreate) *NullableDocumentCreate {
	return &NullableDocumentCreate{value: val, isSet: true}
}

func (v NullableDocumentCreate) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDocumentCreate) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


