/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.UUID;
import org.openapitools.client.model.Company;
import org.openapitools.client.model.DealStage;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

/**
 * Model tests for DealDetails
 */
public class DealDetailsTest {
    private final DealDetails model = new DealDetails();

    /**
     * Model tests for DealDetails
     */
    @Test
    public void testDealDetails() {
        // TODO: test DealDetails
    }

    /**
     * Test the property 'id'
     */
    @Test
    public void idTest() {
        // TODO: test id
    }

    /**
     * Test the property 'title'
     */
    @Test
    public void titleTest() {
        // TODO: test title
    }

    /**
     * Test the property 'description'
     */
    @Test
    public void descriptionTest() {
        // TODO: test description
    }

    /**
     * Test the property 'estimatedValue'
     */
    @Test
    public void estimatedValueTest() {
        // TODO: test estimatedValue
    }

    /**
     * Test the property 'companyId'
     */
    @Test
    public void companyIdTest() {
        // TODO: test companyId
    }

    /**
     * Test the property 'dealStageId'
     */
    @Test
    public void dealStageIdTest() {
        // TODO: test dealStageId
    }

    /**
     * Test the property 'expectedCloseDate'
     */
    @Test
    public void expectedCloseDateTest() {
        // TODO: test expectedCloseDate
    }

    /**
     * Test the property 'createdBy'
     */
    @Test
    public void createdByTest() {
        // TODO: test createdBy
    }

    /**
     * Test the property 'createdAt'
     */
    @Test
    public void createdAtTest() {
        // TODO: test createdAt
    }

    /**
     * Test the property 'updatedAt'
     */
    @Test
    public void updatedAtTest() {
        // TODO: test updatedAt
    }

    /**
     * Test the property 'company'
     */
    @Test
    public void companyTest() {
        // TODO: test company
    }

    /**
     * Test the property 'dealStage'
     */
    @Test
    public void dealStageTest() {
        // TODO: test dealStage
    }

}
