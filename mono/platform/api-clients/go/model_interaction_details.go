/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"time"
)

// checks if the InteractionDetails type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &InteractionDetails{}

// InteractionDetails struct for InteractionDetails
type InteractionDetails struct {
	Id *string `json:"id,omitempty"`
	CompanyId NullableString `json:"company_id,omitempty"`
	ContactId NullableString `json:"contact_id,omitempty"`
	InteractionType *string `json:"interaction_type,omitempty"`
	Notes *string `json:"notes,omitempty"`
	InteractionDatetime *time.Time `json:"interaction_datetime,omitempty"`
	CreatedBy *string `json:"created_by,omitempty"`
	CreatedAt *time.Time `json:"created_at,omitempty"`
	UpdatedAt *time.Time `json:"updated_at,omitempty"`
	Company NullableInteractionDetailsAllOfCompany `json:"company,omitempty"`
	Contact NullableInteractionDetailsAllOfContact `json:"contact,omitempty"`
}

// NewInteractionDetails instantiates a new InteractionDetails object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewInteractionDetails() *InteractionDetails {
	this := InteractionDetails{}
	return &this
}

// NewInteractionDetailsWithDefaults instantiates a new InteractionDetails object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewInteractionDetailsWithDefaults() *InteractionDetails {
	this := InteractionDetails{}
	return &this
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *InteractionDetails) GetId() string {
	if o == nil || IsNil(o.Id) {
		var ret string
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetails) GetIdOk() (*string, bool) {
	if o == nil || IsNil(o.Id) {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *InteractionDetails) HasId() bool {
	if o != nil && !IsNil(o.Id) {
		return true
	}

	return false
}

// SetId gets a reference to the given string and assigns it to the Id field.
func (o *InteractionDetails) SetId(v string) {
	o.Id = &v
}

// GetCompanyId returns the CompanyId field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *InteractionDetails) GetCompanyId() string {
	if o == nil || IsNil(o.CompanyId.Get()) {
		var ret string
		return ret
	}
	return *o.CompanyId.Get()
}

// GetCompanyIdOk returns a tuple with the CompanyId field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *InteractionDetails) GetCompanyIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.CompanyId.Get(), o.CompanyId.IsSet()
}

// HasCompanyId returns a boolean if a field has been set.
func (o *InteractionDetails) HasCompanyId() bool {
	if o != nil && o.CompanyId.IsSet() {
		return true
	}

	return false
}

// SetCompanyId gets a reference to the given NullableString and assigns it to the CompanyId field.
func (o *InteractionDetails) SetCompanyId(v string) {
	o.CompanyId.Set(&v)
}
// SetCompanyIdNil sets the value for CompanyId to be an explicit nil
func (o *InteractionDetails) SetCompanyIdNil() {
	o.CompanyId.Set(nil)
}

// UnsetCompanyId ensures that no value is present for CompanyId, not even an explicit nil
func (o *InteractionDetails) UnsetCompanyId() {
	o.CompanyId.Unset()
}

// GetContactId returns the ContactId field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *InteractionDetails) GetContactId() string {
	if o == nil || IsNil(o.ContactId.Get()) {
		var ret string
		return ret
	}
	return *o.ContactId.Get()
}

// GetContactIdOk returns a tuple with the ContactId field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *InteractionDetails) GetContactIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.ContactId.Get(), o.ContactId.IsSet()
}

// HasContactId returns a boolean if a field has been set.
func (o *InteractionDetails) HasContactId() bool {
	if o != nil && o.ContactId.IsSet() {
		return true
	}

	return false
}

// SetContactId gets a reference to the given NullableString and assigns it to the ContactId field.
func (o *InteractionDetails) SetContactId(v string) {
	o.ContactId.Set(&v)
}
// SetContactIdNil sets the value for ContactId to be an explicit nil
func (o *InteractionDetails) SetContactIdNil() {
	o.ContactId.Set(nil)
}

// UnsetContactId ensures that no value is present for ContactId, not even an explicit nil
func (o *InteractionDetails) UnsetContactId() {
	o.ContactId.Unset()
}

// GetInteractionType returns the InteractionType field value if set, zero value otherwise.
func (o *InteractionDetails) GetInteractionType() string {
	if o == nil || IsNil(o.InteractionType) {
		var ret string
		return ret
	}
	return *o.InteractionType
}

// GetInteractionTypeOk returns a tuple with the InteractionType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetails) GetInteractionTypeOk() (*string, bool) {
	if o == nil || IsNil(o.InteractionType) {
		return nil, false
	}
	return o.InteractionType, true
}

// HasInteractionType returns a boolean if a field has been set.
func (o *InteractionDetails) HasInteractionType() bool {
	if o != nil && !IsNil(o.InteractionType) {
		return true
	}

	return false
}

// SetInteractionType gets a reference to the given string and assigns it to the InteractionType field.
func (o *InteractionDetails) SetInteractionType(v string) {
	o.InteractionType = &v
}

// GetNotes returns the Notes field value if set, zero value otherwise.
func (o *InteractionDetails) GetNotes() string {
	if o == nil || IsNil(o.Notes) {
		var ret string
		return ret
	}
	return *o.Notes
}

// GetNotesOk returns a tuple with the Notes field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetails) GetNotesOk() (*string, bool) {
	if o == nil || IsNil(o.Notes) {
		return nil, false
	}
	return o.Notes, true
}

// HasNotes returns a boolean if a field has been set.
func (o *InteractionDetails) HasNotes() bool {
	if o != nil && !IsNil(o.Notes) {
		return true
	}

	return false
}

// SetNotes gets a reference to the given string and assigns it to the Notes field.
func (o *InteractionDetails) SetNotes(v string) {
	o.Notes = &v
}

// GetInteractionDatetime returns the InteractionDatetime field value if set, zero value otherwise.
func (o *InteractionDetails) GetInteractionDatetime() time.Time {
	if o == nil || IsNil(o.InteractionDatetime) {
		var ret time.Time
		return ret
	}
	return *o.InteractionDatetime
}

// GetInteractionDatetimeOk returns a tuple with the InteractionDatetime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetails) GetInteractionDatetimeOk() (*time.Time, bool) {
	if o == nil || IsNil(o.InteractionDatetime) {
		return nil, false
	}
	return o.InteractionDatetime, true
}

// HasInteractionDatetime returns a boolean if a field has been set.
func (o *InteractionDetails) HasInteractionDatetime() bool {
	if o != nil && !IsNil(o.InteractionDatetime) {
		return true
	}

	return false
}

// SetInteractionDatetime gets a reference to the given time.Time and assigns it to the InteractionDatetime field.
func (o *InteractionDetails) SetInteractionDatetime(v time.Time) {
	o.InteractionDatetime = &v
}

// GetCreatedBy returns the CreatedBy field value if set, zero value otherwise.
func (o *InteractionDetails) GetCreatedBy() string {
	if o == nil || IsNil(o.CreatedBy) {
		var ret string
		return ret
	}
	return *o.CreatedBy
}

// GetCreatedByOk returns a tuple with the CreatedBy field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetails) GetCreatedByOk() (*string, bool) {
	if o == nil || IsNil(o.CreatedBy) {
		return nil, false
	}
	return o.CreatedBy, true
}

// HasCreatedBy returns a boolean if a field has been set.
func (o *InteractionDetails) HasCreatedBy() bool {
	if o != nil && !IsNil(o.CreatedBy) {
		return true
	}

	return false
}

// SetCreatedBy gets a reference to the given string and assigns it to the CreatedBy field.
func (o *InteractionDetails) SetCreatedBy(v string) {
	o.CreatedBy = &v
}

// GetCreatedAt returns the CreatedAt field value if set, zero value otherwise.
func (o *InteractionDetails) GetCreatedAt() time.Time {
	if o == nil || IsNil(o.CreatedAt) {
		var ret time.Time
		return ret
	}
	return *o.CreatedAt
}

// GetCreatedAtOk returns a tuple with the CreatedAt field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetails) GetCreatedAtOk() (*time.Time, bool) {
	if o == nil || IsNil(o.CreatedAt) {
		return nil, false
	}
	return o.CreatedAt, true
}

// HasCreatedAt returns a boolean if a field has been set.
func (o *InteractionDetails) HasCreatedAt() bool {
	if o != nil && !IsNil(o.CreatedAt) {
		return true
	}

	return false
}

// SetCreatedAt gets a reference to the given time.Time and assigns it to the CreatedAt field.
func (o *InteractionDetails) SetCreatedAt(v time.Time) {
	o.CreatedAt = &v
}

// GetUpdatedAt returns the UpdatedAt field value if set, zero value otherwise.
func (o *InteractionDetails) GetUpdatedAt() time.Time {
	if o == nil || IsNil(o.UpdatedAt) {
		var ret time.Time
		return ret
	}
	return *o.UpdatedAt
}

// GetUpdatedAtOk returns a tuple with the UpdatedAt field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetails) GetUpdatedAtOk() (*time.Time, bool) {
	if o == nil || IsNil(o.UpdatedAt) {
		return nil, false
	}
	return o.UpdatedAt, true
}

// HasUpdatedAt returns a boolean if a field has been set.
func (o *InteractionDetails) HasUpdatedAt() bool {
	if o != nil && !IsNil(o.UpdatedAt) {
		return true
	}

	return false
}

// SetUpdatedAt gets a reference to the given time.Time and assigns it to the UpdatedAt field.
func (o *InteractionDetails) SetUpdatedAt(v time.Time) {
	o.UpdatedAt = &v
}

// GetCompany returns the Company field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *InteractionDetails) GetCompany() InteractionDetailsAllOfCompany {
	if o == nil || IsNil(o.Company.Get()) {
		var ret InteractionDetailsAllOfCompany
		return ret
	}
	return *o.Company.Get()
}

// GetCompanyOk returns a tuple with the Company field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *InteractionDetails) GetCompanyOk() (*InteractionDetailsAllOfCompany, bool) {
	if o == nil {
		return nil, false
	}
	return o.Company.Get(), o.Company.IsSet()
}

// HasCompany returns a boolean if a field has been set.
func (o *InteractionDetails) HasCompany() bool {
	if o != nil && o.Company.IsSet() {
		return true
	}

	return false
}

// SetCompany gets a reference to the given NullableInteractionDetailsAllOfCompany and assigns it to the Company field.
func (o *InteractionDetails) SetCompany(v InteractionDetailsAllOfCompany) {
	o.Company.Set(&v)
}
// SetCompanyNil sets the value for Company to be an explicit nil
func (o *InteractionDetails) SetCompanyNil() {
	o.Company.Set(nil)
}

// UnsetCompany ensures that no value is present for Company, not even an explicit nil
func (o *InteractionDetails) UnsetCompany() {
	o.Company.Unset()
}

// GetContact returns the Contact field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *InteractionDetails) GetContact() InteractionDetailsAllOfContact {
	if o == nil || IsNil(o.Contact.Get()) {
		var ret InteractionDetailsAllOfContact
		return ret
	}
	return *o.Contact.Get()
}

// GetContactOk returns a tuple with the Contact field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *InteractionDetails) GetContactOk() (*InteractionDetailsAllOfContact, bool) {
	if o == nil {
		return nil, false
	}
	return o.Contact.Get(), o.Contact.IsSet()
}

// HasContact returns a boolean if a field has been set.
func (o *InteractionDetails) HasContact() bool {
	if o != nil && o.Contact.IsSet() {
		return true
	}

	return false
}

// SetContact gets a reference to the given NullableInteractionDetailsAllOfContact and assigns it to the Contact field.
func (o *InteractionDetails) SetContact(v InteractionDetailsAllOfContact) {
	o.Contact.Set(&v)
}
// SetContactNil sets the value for Contact to be an explicit nil
func (o *InteractionDetails) SetContactNil() {
	o.Contact.Set(nil)
}

// UnsetContact ensures that no value is present for Contact, not even an explicit nil
func (o *InteractionDetails) UnsetContact() {
	o.Contact.Unset()
}

func (o InteractionDetails) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o InteractionDetails) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Id) {
		toSerialize["id"] = o.Id
	}
	if o.CompanyId.IsSet() {
		toSerialize["company_id"] = o.CompanyId.Get()
	}
	if o.ContactId.IsSet() {
		toSerialize["contact_id"] = o.ContactId.Get()
	}
	if !IsNil(o.InteractionType) {
		toSerialize["interaction_type"] = o.InteractionType
	}
	if !IsNil(o.Notes) {
		toSerialize["notes"] = o.Notes
	}
	if !IsNil(o.InteractionDatetime) {
		toSerialize["interaction_datetime"] = o.InteractionDatetime
	}
	if !IsNil(o.CreatedBy) {
		toSerialize["created_by"] = o.CreatedBy
	}
	if !IsNil(o.CreatedAt) {
		toSerialize["created_at"] = o.CreatedAt
	}
	if !IsNil(o.UpdatedAt) {
		toSerialize["updated_at"] = o.UpdatedAt
	}
	if o.Company.IsSet() {
		toSerialize["company"] = o.Company.Get()
	}
	if o.Contact.IsSet() {
		toSerialize["contact"] = o.Contact.Get()
	}
	return toSerialize, nil
}

type NullableInteractionDetails struct {
	value *InteractionDetails
	isSet bool
}

func (v NullableInteractionDetails) Get() *InteractionDetails {
	return v.value
}

func (v *NullableInteractionDetails) Set(val *InteractionDetails) {
	v.value = val
	v.isSet = true
}

func (v NullableInteractionDetails) IsSet() bool {
	return v.isSet
}

func (v *NullableInteractionDetails) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableInteractionDetails(val *InteractionDetails) *NullableInteractionDetails {
	return &NullableInteractionDetails{value: val, isSet: true}
}

func (v NullableInteractionDetails) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableInteractionDetails) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


