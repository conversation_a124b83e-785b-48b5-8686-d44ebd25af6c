# Unified Project Hub

A comprehensive project management system that combines internal project tracking, customer relationship management (CRM), and project requirements documentation (PRD) into a single unified platform.

## Overview

This system merges three distinct project management approaches:
- **Internal Project Tracking**: Team task management, progress tracking, and internal metrics
- **Customer Relationship Management**: Company and contact management with project visibility for customers
- **Requirements Documentation**: PRD creation, requirement tracking, and documentation management

## Architecture

```
unified-project-hub/
├── backend/                 # Node.js/Express API server
│   ├── src/
│   │   ├── controllers/     # API route handlers
│   │   ├── models/         # Database models
│   │   ├── middleware/     # Authentication, validation, etc.
│   │   ├── services/       # Business logic
│   │   ├── routes/         # API routes
│   │   └── utils/          # Helper functions
│   ├── migrations/         # Database migrations
│   ├── seeds/             # Database seed data
│   └── tests/             # Backend tests
├── frontend/               # React/TypeScript application
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/         # Page components
│   │   ├── hooks/         # Custom React hooks
│   │   ├── services/      # API client services
│   │   ├── types/         # TypeScript type definitions
│   │   ├── utils/         # Helper functions
│   │   └── styles/        # Global styles
│   └── public/            # Static assets
├── shared/                # Shared types and utilities
│   ├── types/             # Shared TypeScript types
│   └── constants/         # Shared constants
└── docs/                  # Documentation
    ├── api/               # API documentation
    ├── deployment/        # Deployment guides
    └── development/       # Development setup
```

## Features

### Core Functionality
- **Unified Project Management**: Single interface for all project types
- **Role-Based Access Control**: Different views for team members, customers, and managers
- **Real-time Collaboration**: Live updates and notifications
- **Comprehensive Reporting**: Analytics across all project aspects

### Internal Team Features
- Project and task management with priority levels
- Team member assignment and capacity tracking
- Progress monitoring and deadline management
- Integration with development tools

### Customer-Facing Features
- Project progress visibility for clients
- Milestone tracking and deliverable status
- Communication history and updates
- Simplified project views for non-technical users

### Requirements Management
- PRD creation with markdown editor
- Requirement tracking and approval workflow
- Meeting recording and transcription
- Document attachment and version control

## Technology Stack

### Backend
- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with role-based access
- **File Storage**: AWS S3 or local storage
- **Real-time**: Socket.io for live updates

### Frontend
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **UI Library**: shadcn/ui with Tailwind CSS
- **State Management**: TanStack Query + Zustand
- **Routing**: React Router v6
- **Forms**: React Hook Form with Zod validation

### Development Tools
- **Package Manager**: npm/yarn
- **Code Quality**: ESLint, Prettier, Husky
- **Testing**: Jest, React Testing Library, Supertest
- **Documentation**: OpenAPI/Swagger
- **Containerization**: Docker & Docker Compose

## Quick Start

### Prerequisites
- Node.js 18 or higher
- PostgreSQL 14 or higher
- npm or yarn package manager

### Development Setup

1. **Clone and install dependencies**:
```bash
git clone <repository-url>
cd unified-project-hub
npm install
```

2. **Set up environment variables**:
```bash
# Copy environment templates
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# Edit the files with your configuration
```

3. **Set up the database**:
```bash
cd backend
npm run db:migrate
npm run db:seed
```

4. **Start development servers**:
```bash
# Terminal 1 - Backend
cd backend
npm run dev

# Terminal 2 - Frontend
cd frontend
npm run dev
```

5. **Access the application**:
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

### Docker Development

```bash
# Start all services with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## API Documentation

The API documentation is automatically generated and available at:
- Development: http://localhost:8000/docs
- Production: https://your-domain.com/api/docs

## Testing

```bash
# Run all tests
npm test

# Backend tests only
cd backend && npm test

# Frontend tests only
cd frontend && npm test

# Test coverage
npm run test:coverage
```

## Deployment

See the [deployment documentation](./docs/deployment/) for detailed instructions on deploying to various platforms.

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature`
3. Commit your changes: `git commit -am 'Add some feature'`
4. Push to the branch: `git push origin feature/your-feature`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions:
- Create an issue in the GitHub repository
- Check the [documentation](./docs/)
- Contact the development team

## Roadmap

- [ ] Mobile application (React Native)
- [ ] Advanced analytics and reporting
- [ ] Third-party integrations (Slack, Jira, etc.)
- [ ] AI-powered project insights
- [ ] Multi-tenant support
- [ ] Advanced workflow automation
