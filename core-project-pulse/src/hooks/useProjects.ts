import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Project, ProjectWithDetails } from "@/types/project";
import { useToast } from "@/hooks/use-toast";

export function useProjects() {
  return useQuery({
    queryKey: ['projects'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          tasks (
            id,
            project_id,
            name,
            description,
            assignee,
            due_date,
            status,
            created_at,
            updated_at,
            sub_tasks (
              id,
              task_id,
              name,
              description,
              assignee,
              due_date,
              status,
              created_at,
              updated_at
            )
          )
        `)
        .order('priority_order', { ascending: true });

      if (error) throw error;
      return data as ProjectWithDetails[];
    },
  });
}

export function useProjectsBasic() {
  return useQuery({
    queryKey: ['projects-basic'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .order('priority_order', { ascending: true });

      if (error) throw error;
      return data as Project[];
    },
  });
}

export function useProjectProgress() {
  return useQuery({
    queryKey: ['project-progress'],
    queryFn: async () => {
      const { data: projects } = await supabase
        .from('projects')
        .select('id');

      if (!projects) return {};

      const progressMap: Record<string, number> = {};
      
      for (const project of projects) {
        const { data } = await supabase
          .rpc('calculate_project_progress', { project_uuid: project.id });
        progressMap[project.id] = data || 0;
      }

      return progressMap;
    },
  });
}

export function useReorderProjects() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ projectIds, newOrders }: { projectIds: string[], newOrders: number[] }) => {
      const { error } = await supabase.rpc('reorder_projects', {
        project_ids: projectIds,
        new_orders: newOrders
      });
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });
    },
    onError: (error) => {
      console.error('Error reordering projects:', error);
      toast({
        title: "Error",
        description: "Failed to reorder projects. Please try again.",
        variant: "destructive",
      });
    },
  });
}