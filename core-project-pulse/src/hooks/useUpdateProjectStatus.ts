import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

export function useUpdateProjectStatus() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ projectId, status }: { projectId: string, status: string }) => {
      const { error } = await supabase
        .from('projects')
        .update({ 
          status,
          status_changed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', projectId);
      
      if (error) throw error;
      
      return { projectId, status };
    },
    onSuccess: (data) => {
      // Optimistic update with immediate cache refresh
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      queryClient.invalidateQueries({ queryKey: ['projects-basic'] });
    },
    onError: (error, variables) => {
      console.error('Error updating project status:', error);
      
      // Revert optimistic update by refreshing cache
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      
      toast({
        title: "Update Failed",
        description: "Failed to update project status. Please try again.",
        variant: "destructive",
      });
    },
    retry: 1,
    retryDelay: 1000,
  });
}