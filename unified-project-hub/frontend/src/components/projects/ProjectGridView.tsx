import React from 'react';
import { ProjectCard } from './ProjectCard';

interface ProjectGridViewProps {
  projects: any[];
  groupedProjects?: Record<string, any[]>;
  groupBy?: string;
  onEdit?: (project: any) => void;
  onArchive?: (project: any) => void;
  onDelete?: (project: any) => void;
  showActions?: boolean;
}

export function ProjectGridView({
  projects,
  groupedProjects,
  groupBy = 'none',
  onEdit,
  onArchive,
  onDelete,
  showActions = true
}: ProjectGridViewProps) {
  if (groupBy !== 'none' && groupedProjects) {
    return (
      <div className="space-y-8">
        {Object.entries(groupedProjects).map(([groupKey, groupProjects]) => (
          <div key={groupKey} className="space-y-4">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold">{groupKey}</h3>
              <span className="text-sm text-muted-foreground">
                ({groupProjects.length} project{groupProjects.length !== 1 ? 's' : ''})
              </span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {groupProjects.map((project) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  onEdit={onEdit}
                  onArchive={onArchive}
                  onDelete={onDelete}
                  showActions={showActions}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {projects.map((project) => (
        <ProjectCard
          key={project.id}
          project={project}
          onEdit={onEdit}
          onArchive={onArchive}
          onDelete={onDelete}
          showActions={showActions}
        />
      ))}
    </div>
  );
}
