{"main": {"id": "38c984ecd137fe85", "type": "split", "children": [{"id": "4d839117ca68153f", "type": "tabs", "children": [{"id": "c3b3a620acdeda2c", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mono/docs/BAZEL_GUIDE.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "BAZEL_GUIDE"}}]}], "direction": "vertical"}, "left": {"id": "2ec0786b84aaa29d", "type": "split", "children": [{"id": "e1a7c0d07fcebc22", "type": "tabs", "children": [{"id": "8ce528aed50d231d", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "16bdc7865ff06ace", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "ca19a4369a9cae7b", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "c30ec40850d8ab73", "type": "split", "children": [{"id": "2dc4dc79d0fa7ad0", "type": "tabs", "children": [{"id": "2f75f99322335ad8", "type": "leaf", "state": {"type": "backlink", "state": {"file": "mono/MONOREPO_REORGANIZATION.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for MONOREPO_REORGANIZATION"}}, {"id": "1abbc22ad373324b", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "mono/MONOREPO_REORGANIZATION.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from MONOREPO_REORGANIZATION"}}, {"id": "11ed7072e4608442", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "7d1a9d83d4ced364", "type": "leaf", "state": {"type": "outline", "state": {"file": "mono/MONOREPO_REORGANIZATION.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of MONOREPO_REORGANIZATION"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "c3b3a620acdeda2c", "lastOpenFiles": ["unified-project-hub/backend/node_modules/zod/v4/core/versions.d.ts", "unified-project-hub/backend/node_modules/zod/v4/core/util.d.ts", "unified-project-hub/backend/node_modules/zod/v4/core/to-json-schema.d.ts", "unified-project-hub/backend/node_modules/zod/v4/core/standard-schema.d.ts", "unified-project-hub/backend/node_modules/zod/v4/core/schemas.d.ts", "unified-project-hub/backend/node_modules/zod/v4/classic/schemas.d.ts", "unified-project-hub/backend/node_modules/zod/v4/core/registries.d.ts", "unified-project-hub/backend/node_modules/zod/v4/core/regexes.d.ts", "unified-project-hub/backend/node_modules/zod/v4/core/parse.d.ts", "unified-project-hub/backend/node_modules/zod/v4/classic/parse.d.ts", "unified-project-hub/backend/node_modules/zod/v4/core/json-schema.d.ts", "unified-project-hub/backend/node_modules/zod/README.md", "unified-project-hub/backend/node_modules/tsconfig-paths/README.md", "unified-project-hub/backend/node_modules/tsconfig-paths/CHANGELOG.md", "unified-project-hub/backend/node_modules/tsconfig-paths/node_modules/strip-bom/readme.md", "unified-project-hub/frontend/node_modules/fsevents/README.md", "unified-project-hub/backend/node_modules/fsevents/README.md", "unified-project-hub/frontend/node_modules/date-fns/README.md", "unified-project-hub/frontend/node_modules/date-fns/LICENSE.md", "unified-project-hub/frontend/node_modules/date-fns/CHANGELOG.md", "unified-project-hub/frontend/node_modules/@vitest/ui/README.md", "unified-project-hub/frontend/node_modules/@vitest/ui/dist/client/bg.png", "unified-project-hub/frontend/node_modules/@vitest/ui/dist/client/favicon.svg", "unified-project-hub/frontend/node_modules/@dnd-kit/accessibility/README.md", "unified-project-hub/frontend/node_modules/@dnd-kit/accessibility/CHANGELOG.md", "unified-project-hub/frontend/node_modules/react-hook-form/README.md", "unified-project-hub/frontend/node_modules/@asamuzakjp/dom-selector/README.md", "unified-project-hub/frontend/node_modules/@dnd-kit/sortable/README.md", "unified-project-hub/frontend/node_modules/@dnd-kit/sortable/CHANGELOG.md", "unified-project-hub/frontend/node_modules/@dnd-kit/core/README.md", "unified-project-hub/frontend/node_modules/@dnd-kit/core/CHANGELOG.md", "unified-project-hub/frontend/node_modules/@dnd-kit/utilities/README.md", "unified-project-hub/frontend/node_modules/@dnd-kit/utilities/CHANGELOG.md", "unified-project-hub/frontend/node_modules/@radix-ui/react-separator/README.md", "unified-project-hub/frontend/node_modules/lucide-react/README.md", "unified-project-hub/frontend/node_modules/@testing-library/react/node_modules/@testing-library/dom/README.md", "unified-project-hub/frontend/node_modules/@testing-library/react/node_modules/@testing-library/dom/CHANGELOG.md", "unified-project-hub/frontend/node_modules/parse-entities/node_modules/@types/unist/README.md", "unified-project-hub/frontend/node_modules/date-fns/docs/logotype.svg", "unified-project-hub/frontend/node_modules/date-fns/docs/logo.svg", "unified-project-hub/frontend/node_modules/psl/browserstack-logo.svg", "unified-project-hub/frontend/node_modules/highlight.js/styles/pojoaque.jpg", "unified-project-hub/frontend/node_modules/highlight.js/styles/brown-papersq.png", "unified-project-hub/backend/node_modules/prisma/build/public/assets/tick.8cbb6a93.svg", "unified-project-hub/backend/node_modules/prisma/build/public/assets/tick-indeterminate.aec8a44d.svg", "unified-project-hub/backend/node_modules/prisma/build/public/assets/string.ea615a24.svg"]}