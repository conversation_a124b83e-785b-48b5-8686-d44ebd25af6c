import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireRole } from '../middleware/auth';

const router = Router();
const prisma = new PrismaClient();

// Validation schemas
const createInteractionSchema = z.object({
  project_id: z.string().uuid().optional(),
  company_id: z.string().uuid().optional(),
  contact_id: z.string().uuid().optional(),
  type: z.enum(['email', 'call', 'meeting', 'note', 'task_update', 'milestone']),
  subject: z.string().min(1, 'Subject is required'),
  content: z.string().min(1, 'Content is required'),
  interaction_date: z.string().datetime().optional(),
  is_customer_visible: z.boolean().default(false),
  tags: z.array(z.string()).default([]),
  attachment_urls: z.array(z.string().url()).default([]),
  recording_url: z.string().url().optional(),
  transcript_url: z.string().url().optional(),
  participant_ids: z.array(z.string().uuid()).default([]),
});

/**
 * @route GET /api/interactions
 * @desc Get interactions (filtered by user role and permissions)
 * @access Private
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { page = '1', limit = '10', type, project_id, company_id, contact_id } = req.query;
  const skip = (parseInt(page as string) - 1) * parseInt(limit as string);

  let whereClause: any = {};

  // Filter based on user role
  if (req.user?.role === 'customer') {
    whereClause.is_customer_visible = true;
    whereClause.OR = [
      { project: { customer_contact: { email: req.user.email } } },
      { contact: { email: req.user.email } },
    ];
  } else if (req.user?.role === 'team_member') {
    whereClause.OR = [
      { created_by_id: req.user.id },
      { participants: { some: { user_id: req.user.id } } },
      { project: { project_lead_id: req.user.id } },
      { project: { team_members: { some: { user_id: req.user.id } } } },
    ];
  }

  // Apply filters
  if (type) whereClause.type = type;
  if (project_id) whereClause.project_id = project_id;
  if (company_id) whereClause.company_id = company_id;
  if (contact_id) whereClause.contact_id = contact_id;

  const [interactions, total] = await Promise.all([
    prisma.interaction.findMany({
      where: whereClause,
      include: {
        project: {
          select: { id: true, name: true, company_name: true },
        },
        company: {
          select: { id: true, name: true },
        },
        contact: {
          select: { id: true, name: true, email: true, title: true },
        },
        created_by: {
          select: { id: true, name: true, email: true, avatar_url: true },
        },
        participants: {
          include: {
            user: {
              select: { id: true, name: true, email: true, avatar_url: true },
            },
          },
        },
      },
      skip,
      take: parseInt(limit as string),
      orderBy: { interaction_date: 'desc' },
    }),
    prisma.interaction.count({ where: whereClause }),
  ]);

  res.json({
    status: 'success',
    data: {
      interactions,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        pages: Math.ceil(total / parseInt(limit as string)),
      },
    },
  });
}));

/**
 * @route GET /api/interactions/:id
 * @desc Get interaction by ID
 * @access Private
 */
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  let whereClause: any = { id };

  // Filter based on user role
  if (req.user?.role === 'customer') {
    whereClause.is_customer_visible = true;
    whereClause.OR = [
      { project: { customer_contact: { email: req.user.email } } },
      { contact: { email: req.user.email } },
    ];
  } else if (req.user?.role === 'team_member') {
    whereClause.OR = [
      { created_by_id: req.user.id },
      { participants: { some: { user_id: req.user.id } } },
      { project: { project_lead_id: req.user.id } },
      { project: { team_members: { some: { user_id: req.user.id } } } },
    ];
  }

  const interaction = await prisma.interaction.findFirst({
    where: whereClause,
    include: {
      project: {
        select: { id: true, name: true, company_name: true },
      },
      company: {
        select: { id: true, name: true },
      },
      contact: {
        select: { id: true, name: true, email: true, title: true },
      },
      created_by: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
      participants: {
        include: {
          user: {
            select: { id: true, name: true, email: true, avatar_url: true },
          },
        },
      },
    },
  });

  if (!interaction) {
    return res.status(404).json({
      status: 'error',
      message: 'Interaction not found or access denied',
    });
  }

  res.json({
    status: 'success',
    data: { interaction },
  });
}));

/**
 * @route POST /api/interactions
 * @desc Create new interaction
 * @access Private
 */
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createInteractionSchema.parse(req.body);

  // Customers cannot create interactions
  if (req.user?.role === 'customer') {
    return res.status(403).json({
      status: 'error',
      message: 'Access denied',
    });
  }

  // If project_id is provided, check access
  if (validatedData.project_id) {
    const project = await prisma.project.findFirst({
      where: {
        id: validatedData.project_id,
        OR: [
          { project_lead_id: req.user!.id },
          { team_members: { some: { user_id: req.user!.id } } },
          ...(req.user!.role === 'admin' || req.user!.role === 'project_manager' ? [{}] : []),
        ],
      },
    });

    if (!project) {
      return res.status(404).json({
        status: 'error',
        message: 'Project not found or access denied',
      });
    }
  }

  const interaction = await prisma.interaction.create({
    data: {
      ...validatedData,
      created_by_id: req.user!.id,
      interaction_date: validatedData.interaction_date 
        ? new Date(validatedData.interaction_date) 
        : new Date(),
    },
    include: {
      project: {
        select: { id: true, name: true, company_name: true },
      },
      company: {
        select: { id: true, name: true },
      },
      contact: {
        select: { id: true, name: true, email: true, title: true },
      },
      created_by: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
    },
  });

  // Add participants
  if (validatedData.participant_ids.length > 0) {
    await prisma.interactionParticipant.createMany({
      data: validatedData.participant_ids.map(userId => ({
        interaction_id: interaction.id,
        user_id: userId,
      })),
    });
  }

  res.status(201).json({
    status: 'success',
    message: 'Interaction created successfully',
    data: { interaction },
  });
}));

/**
 * @route PUT /api/interactions/:id
 * @desc Update interaction
 * @access Private
 */
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const updateData = createInteractionSchema.partial().parse(req.body);

  // Check if user can update this interaction
  const existingInteraction = await prisma.interaction.findFirst({
    where: {
      id,
      OR: [
        { created_by_id: req.user!.id },
        ...(req.user!.role === 'admin' || req.user!.role === 'project_manager' ? [{}] : []),
      ],
    },
  });

  if (!existingInteraction) {
    return res.status(404).json({
      status: 'error',
      message: 'Interaction not found or access denied',
    });
  }

  const interaction = await prisma.interaction.update({
    where: { id },
    data: {
      ...updateData,
      interaction_date: updateData.interaction_date 
        ? new Date(updateData.interaction_date) 
        : undefined,
    },
    include: {
      project: {
        select: { id: true, name: true, company_name: true },
      },
      company: {
        select: { id: true, name: true },
      },
      contact: {
        select: { id: true, name: true, email: true, title: true },
      },
      created_by: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
      participants: {
        include: {
          user: {
            select: { id: true, name: true, email: true, avatar_url: true },
          },
        },
      },
    },
  });

  res.json({
    status: 'success',
    message: 'Interaction updated successfully',
    data: { interaction },
  });
}));

/**
 * @route DELETE /api/interactions/:id
 * @desc Delete interaction
 * @access Private (Admin, Project Manager, Creator)
 */
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if user can delete this interaction
  const existingInteraction = await prisma.interaction.findFirst({
    where: {
      id,
      OR: [
        { created_by_id: req.user!.id },
        ...(req.user!.role === 'admin' || req.user!.role === 'project_manager' ? [{}] : []),
      ],
    },
  });

  if (!existingInteraction) {
    return res.status(404).json({
      status: 'error',
      message: 'Interaction not found or access denied',
    });
  }

  await prisma.interaction.delete({
    where: { id },
  });

  res.json({
    status: 'success',
    message: 'Interaction deleted successfully',
  });
}));

export default router;
