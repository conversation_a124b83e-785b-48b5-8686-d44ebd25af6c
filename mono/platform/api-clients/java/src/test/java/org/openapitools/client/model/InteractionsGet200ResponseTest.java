/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.openapitools.client.model.InteractionWithDetails;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

/**
 * Model tests for InteractionsGet200Response
 */
public class InteractionsGet200ResponseTest {
    private final InteractionsGet200Response model = new InteractionsGet200Response();

    /**
     * Model tests for InteractionsGet200Response
     */
    @Test
    public void testInteractionsGet200Response() {
        // TODO: test InteractionsGet200Response
    }

    /**
     * Test the property 'interactions'
     */
    @Test
    public void interactionsTest() {
        // TODO: test interactions
    }

    /**
     * Test the property 'totalCount'
     */
    @Test
    public void totalCountTest() {
        // TODO: test totalCount
    }

    /**
     * Test the property 'hasMore'
     */
    @Test
    public void hasMoreTest() {
        // TODO: test hasMore
    }

}
