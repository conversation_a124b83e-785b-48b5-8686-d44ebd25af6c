import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { PrismaClient, UserRole } from '@prisma/client';
import { CustomError } from './errorHandler';
import { getEnv } from '../config/env';

const env = getEnv();
const prisma = new PrismaClient();

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    name: string;
    role: UserRole;
    is_active: boolean;
  };
}

export interface JwtPayload {
  userId: string;
  email: string;
  role: UserRole;
  iat?: number;
  exp?: number;
}

export const authMiddleware = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new CustomError('Access token is required', 401);
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify token
    const decoded = jwt.verify(token, env.JWT_SECRET) as JwtPayload;

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        is_active: true,
      },
    });

    if (!user) {
      throw new CustomError('User not found', 401);
    }

    if (!user.is_active) {
      throw new CustomError('User account is deactivated', 401);
    }

    // Attach user to request
    req.user = user;
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new CustomError('Invalid token', 401));
    } else if (error instanceof jwt.TokenExpiredError) {
      next(new CustomError('Token expired', 401));
    } else {
      next(error);
    }
  }
};

// Role-based authorization middleware
export const requireRole = (...roles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new CustomError('Authentication required', 401));
    }

    if (!roles.includes(req.user.role)) {
      return next(new CustomError('Insufficient permissions', 403));
    }

    next();
  };
};

// Check if user can access project
export const canAccessProject = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      return next(new CustomError('Authentication required', 401));
    }

    const projectId = req.params.id || req.params.projectId || req.body.project_id;
    
    if (!projectId) {
      return next(new CustomError('Project ID is required', 400));
    }

    // Admin and project managers can access all projects
    if (req.user.role === 'admin' || req.user.role === 'project_manager') {
      return next();
    }

    // Check if user is project lead or team member
    const project = await prisma.project.findFirst({
      where: {
        id: projectId,
        OR: [
          { project_lead_id: req.user.id },
          { team_members: { some: { user_id: req.user.id } } },
          // For customers, check if they are the customer contact
          req.user.role === 'customer' ? { customer_contact: { email: req.user.email } } : {},
        ],
      },
    });

    if (!project) {
      return next(new CustomError('Project not found or access denied', 404));
    }

    next();
  } catch (error) {
    next(error);
  }
};

// Check if user can access company
export const canAccessCompany = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      return next(new CustomError('Authentication required', 401));
    }

    // Customers cannot access company management
    if (req.user.role === 'customer') {
      return next(new CustomError('Access denied', 403));
    }

    next();
  } catch (error) {
    next(error);
  }
};

// Optional auth middleware (doesn't fail if no token)
export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, env.JWT_SECRET) as JwtPayload;

    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        is_active: true,
      },
    });

    if (user && user.is_active) {
      req.user = user;
    }

    next();
  } catch (error) {
    // Ignore auth errors for optional auth
    next();
  }
};
