/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.api;

import org.openapitools.client.ApiCallback;
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.ApiResponse;
import org.openapitools.client.Configuration;
import org.openapitools.client.Pair;
import org.openapitools.client.ProgressRequestBody;
import org.openapitools.client.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import org.openapitools.client.model.Company;
import org.openapitools.client.model.CompanyCreate;
import org.openapitools.client.model.CompanyDetails;
import org.openapitools.client.model.CompanyStatus;
import org.openapitools.client.model.CompanyStatusUpdateRequest;
import org.openapitools.client.model.CompanyUpdate;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CompaniesApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public CompaniesApi() {
        this(Configuration.getDefaultApiClient());
    }

    public CompaniesApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for companiesGet
     * @param statusId Filter by company status (optional)
     * @param includeDeleted Include soft-deleted companies (optional, default to false)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of companies </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call companiesGetCall(UUID statusId, Boolean includeDeleted, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/companies";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (statusId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("status_id", statusId));
        }

        if (includeDeleted != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("include_deleted", includeDeleted));
        }

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call companiesGetValidateBeforeCall(UUID statusId, Boolean includeDeleted, final ApiCallback _callback) throws ApiException {
        return companiesGetCall(statusId, includeDeleted, _callback);

    }

    /**
     * List companies
     * Retrieve a list of all active companies
     * @param statusId Filter by company status (optional)
     * @param includeDeleted Include soft-deleted companies (optional, default to false)
     * @return List&lt;Company&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of companies </td><td>  -  </td></tr>
     </table>
     */
    public List<Company> companiesGet(UUID statusId, Boolean includeDeleted) throws ApiException {
        ApiResponse<List<Company>> localVarResp = companiesGetWithHttpInfo(statusId, includeDeleted);
        return localVarResp.getData();
    }

    /**
     * List companies
     * Retrieve a list of all active companies
     * @param statusId Filter by company status (optional)
     * @param includeDeleted Include soft-deleted companies (optional, default to false)
     * @return ApiResponse&lt;List&lt;Company&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of companies </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<Company>> companiesGetWithHttpInfo(UUID statusId, Boolean includeDeleted) throws ApiException {
        okhttp3.Call localVarCall = companiesGetValidateBeforeCall(statusId, includeDeleted, null);
        Type localVarReturnType = new TypeToken<List<Company>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * List companies (asynchronously)
     * Retrieve a list of all active companies
     * @param statusId Filter by company status (optional)
     * @param includeDeleted Include soft-deleted companies (optional, default to false)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of companies </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call companiesGetAsync(UUID statusId, Boolean includeDeleted, final ApiCallback<List<Company>> _callback) throws ApiException {

        okhttp3.Call localVarCall = companiesGetValidateBeforeCall(statusId, includeDeleted, _callback);
        Type localVarReturnType = new TypeToken<List<Company>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for companiesIdDelete
     * @param id  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Company deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Company not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call companiesIdDeleteCall(UUID id, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/companies/{id}"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call companiesIdDeleteValidateBeforeCall(UUID id, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling companiesIdDelete(Async)");
        }

        return companiesIdDeleteCall(id, _callback);

    }

    /**
     * Delete company
     * Soft delete a company (sets is_deleted to true)
     * @param id  (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Company deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Company not found </td><td>  -  </td></tr>
     </table>
     */
    public void companiesIdDelete(UUID id) throws ApiException {
        companiesIdDeleteWithHttpInfo(id);
    }

    /**
     * Delete company
     * Soft delete a company (sets is_deleted to true)
     * @param id  (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Company deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Company not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> companiesIdDeleteWithHttpInfo(UUID id) throws ApiException {
        okhttp3.Call localVarCall = companiesIdDeleteValidateBeforeCall(id, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Delete company (asynchronously)
     * Soft delete a company (sets is_deleted to true)
     * @param id  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Company deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Company not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call companiesIdDeleteAsync(UUID id, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = companiesIdDeleteValidateBeforeCall(id, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for companiesIdGet
     * @param id  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Company details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Company not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call companiesIdGetCall(UUID id, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/companies/{id}"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call companiesIdGetValidateBeforeCall(UUID id, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling companiesIdGet(Async)");
        }

        return companiesIdGetCall(id, _callback);

    }

    /**
     * Get company
     * Retrieve a specific company by ID
     * @param id  (required)
     * @return CompanyDetails
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Company details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Company not found </td><td>  -  </td></tr>
     </table>
     */
    public CompanyDetails companiesIdGet(UUID id) throws ApiException {
        ApiResponse<CompanyDetails> localVarResp = companiesIdGetWithHttpInfo(id);
        return localVarResp.getData();
    }

    /**
     * Get company
     * Retrieve a specific company by ID
     * @param id  (required)
     * @return ApiResponse&lt;CompanyDetails&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Company details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Company not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<CompanyDetails> companiesIdGetWithHttpInfo(UUID id) throws ApiException {
        okhttp3.Call localVarCall = companiesIdGetValidateBeforeCall(id, null);
        Type localVarReturnType = new TypeToken<CompanyDetails>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get company (asynchronously)
     * Retrieve a specific company by ID
     * @param id  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Company details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Company not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call companiesIdGetAsync(UUID id, final ApiCallback<CompanyDetails> _callback) throws ApiException {

        okhttp3.Call localVarCall = companiesIdGetValidateBeforeCall(id, _callback);
        Type localVarReturnType = new TypeToken<CompanyDetails>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for companiesIdPut
     * @param id  (required)
     * @param companyUpdate  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Company updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Company not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call companiesIdPutCall(UUID id, CompanyUpdate companyUpdate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = companyUpdate;

        // create path and map variables
        String localVarPath = "/companies/{id}"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call companiesIdPutValidateBeforeCall(UUID id, CompanyUpdate companyUpdate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling companiesIdPut(Async)");
        }

        // verify the required parameter 'companyUpdate' is set
        if (companyUpdate == null) {
            throw new ApiException("Missing the required parameter 'companyUpdate' when calling companiesIdPut(Async)");
        }

        return companiesIdPutCall(id, companyUpdate, _callback);

    }

    /**
     * Update company
     * Update an existing company
     * @param id  (required)
     * @param companyUpdate  (required)
     * @return Company
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Company updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Company not found </td><td>  -  </td></tr>
     </table>
     */
    public Company companiesIdPut(UUID id, CompanyUpdate companyUpdate) throws ApiException {
        ApiResponse<Company> localVarResp = companiesIdPutWithHttpInfo(id, companyUpdate);
        return localVarResp.getData();
    }

    /**
     * Update company
     * Update an existing company
     * @param id  (required)
     * @param companyUpdate  (required)
     * @return ApiResponse&lt;Company&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Company updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Company not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Company> companiesIdPutWithHttpInfo(UUID id, CompanyUpdate companyUpdate) throws ApiException {
        okhttp3.Call localVarCall = companiesIdPutValidateBeforeCall(id, companyUpdate, null);
        Type localVarReturnType = new TypeToken<Company>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Update company (asynchronously)
     * Update an existing company
     * @param id  (required)
     * @param companyUpdate  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Company updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Company not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call companiesIdPutAsync(UUID id, CompanyUpdate companyUpdate, final ApiCallback<Company> _callback) throws ApiException {

        okhttp3.Call localVarCall = companiesIdPutValidateBeforeCall(id, companyUpdate, _callback);
        Type localVarReturnType = new TypeToken<Company>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for companiesIdStatusPut
     * @param id  (required)
     * @param companyStatusUpdateRequest  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Company status updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Company not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call companiesIdStatusPutCall(UUID id, CompanyStatusUpdateRequest companyStatusUpdateRequest, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = companyStatusUpdateRequest;

        // create path and map variables
        String localVarPath = "/companies/{id}/status"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call companiesIdStatusPutValidateBeforeCall(UUID id, CompanyStatusUpdateRequest companyStatusUpdateRequest, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling companiesIdStatusPut(Async)");
        }

        // verify the required parameter 'companyStatusUpdateRequest' is set
        if (companyStatusUpdateRequest == null) {
            throw new ApiException("Missing the required parameter 'companyStatusUpdateRequest' when calling companiesIdStatusPut(Async)");
        }

        return companiesIdStatusPutCall(id, companyStatusUpdateRequest, _callback);

    }

    /**
     * Update company status
     * Update the status of a company
     * @param id  (required)
     * @param companyStatusUpdateRequest  (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Company status updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Company not found </td><td>  -  </td></tr>
     </table>
     */
    public void companiesIdStatusPut(UUID id, CompanyStatusUpdateRequest companyStatusUpdateRequest) throws ApiException {
        companiesIdStatusPutWithHttpInfo(id, companyStatusUpdateRequest);
    }

    /**
     * Update company status
     * Update the status of a company
     * @param id  (required)
     * @param companyStatusUpdateRequest  (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Company status updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Company not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> companiesIdStatusPutWithHttpInfo(UUID id, CompanyStatusUpdateRequest companyStatusUpdateRequest) throws ApiException {
        okhttp3.Call localVarCall = companiesIdStatusPutValidateBeforeCall(id, companyStatusUpdateRequest, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Update company status (asynchronously)
     * Update the status of a company
     * @param id  (required)
     * @param companyStatusUpdateRequest  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Company status updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Company not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call companiesIdStatusPutAsync(UUID id, CompanyStatusUpdateRequest companyStatusUpdateRequest, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = companiesIdStatusPutValidateBeforeCall(id, companyStatusUpdateRequest, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for companiesPost
     * @param companyCreate  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Company created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call companiesPostCall(CompanyCreate companyCreate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = companyCreate;

        // create path and map variables
        String localVarPath = "/companies";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call companiesPostValidateBeforeCall(CompanyCreate companyCreate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'companyCreate' is set
        if (companyCreate == null) {
            throw new ApiException("Missing the required parameter 'companyCreate' when calling companiesPost(Async)");
        }

        return companiesPostCall(companyCreate, _callback);

    }

    /**
     * Create company
     * Create a new company
     * @param companyCreate  (required)
     * @return Company
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Company created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public Company companiesPost(CompanyCreate companyCreate) throws ApiException {
        ApiResponse<Company> localVarResp = companiesPostWithHttpInfo(companyCreate);
        return localVarResp.getData();
    }

    /**
     * Create company
     * Create a new company
     * @param companyCreate  (required)
     * @return ApiResponse&lt;Company&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Company created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Company> companiesPostWithHttpInfo(CompanyCreate companyCreate) throws ApiException {
        okhttp3.Call localVarCall = companiesPostValidateBeforeCall(companyCreate, null);
        Type localVarReturnType = new TypeToken<Company>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Create company (asynchronously)
     * Create a new company
     * @param companyCreate  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Company created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call companiesPostAsync(CompanyCreate companyCreate, final ApiCallback<Company> _callback) throws ApiException {

        okhttp3.Call localVarCall = companiesPostValidateBeforeCall(companyCreate, _callback);
        Type localVarReturnType = new TypeToken<Company>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for companyStatusesGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of company statuses </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call companyStatusesGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/company-statuses";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call companyStatusesGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return companyStatusesGetCall(_callback);

    }

    /**
     * List company statuses
     * Retrieve all company statuses ordered by pipeline order
     * @return List&lt;CompanyStatus&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of company statuses </td><td>  -  </td></tr>
     </table>
     */
    public List<CompanyStatus> companyStatusesGet() throws ApiException {
        ApiResponse<List<CompanyStatus>> localVarResp = companyStatusesGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * List company statuses
     * Retrieve all company statuses ordered by pipeline order
     * @return ApiResponse&lt;List&lt;CompanyStatus&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of company statuses </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<CompanyStatus>> companyStatusesGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = companyStatusesGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<List<CompanyStatus>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * List company statuses (asynchronously)
     * Retrieve all company statuses ordered by pipeline order
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of company statuses </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call companyStatusesGetAsync(final ApiCallback<List<CompanyStatus>> _callback) throws ApiException {

        okhttp3.Call localVarCall = companyStatusesGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<List<CompanyStatus>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
