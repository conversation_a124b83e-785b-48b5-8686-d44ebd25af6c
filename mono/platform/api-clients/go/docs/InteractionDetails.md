# InteractionDetails

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Id** | Pointer to **string** |  | [optional] 
**CompanyId** | Pointer to **NullableString** |  | [optional] 
**ContactId** | Pointer to **NullableString** |  | [optional] 
**InteractionType** | Pointer to **string** |  | [optional] 
**Notes** | Pointer to **string** |  | [optional] 
**InteractionDatetime** | Pointer to **time.Time** |  | [optional] 
**CreatedBy** | Pointer to **string** |  | [optional] 
**CreatedAt** | Pointer to **time.Time** |  | [optional] 
**UpdatedAt** | Pointer to **time.Time** |  | [optional] 
**Company** | Pointer to [**NullableInteractionDetailsAllOfCompany**](InteractionDetailsAllOfCompany.md) |  | [optional] 
**Contact** | Pointer to [**NullableInteractionDetailsAllOfContact**](InteractionDetailsAllOfContact.md) |  | [optional] 

## Methods

### NewInteractionDetails

`func NewInteractionDetails() *InteractionDetails`

NewInteractionDetails instantiates a new InteractionDetails object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewInteractionDetailsWithDefaults

`func NewInteractionDetailsWithDefaults() *InteractionDetails`

NewInteractionDetailsWithDefaults instantiates a new InteractionDetails object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetId

`func (o *InteractionDetails) GetId() string`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *InteractionDetails) GetIdOk() (*string, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *InteractionDetails) SetId(v string)`

SetId sets Id field to given value.

### HasId

`func (o *InteractionDetails) HasId() bool`

HasId returns a boolean if a field has been set.

### GetCompanyId

`func (o *InteractionDetails) GetCompanyId() string`

GetCompanyId returns the CompanyId field if non-nil, zero value otherwise.

### GetCompanyIdOk

`func (o *InteractionDetails) GetCompanyIdOk() (*string, bool)`

GetCompanyIdOk returns a tuple with the CompanyId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCompanyId

`func (o *InteractionDetails) SetCompanyId(v string)`

SetCompanyId sets CompanyId field to given value.

### HasCompanyId

`func (o *InteractionDetails) HasCompanyId() bool`

HasCompanyId returns a boolean if a field has been set.

### SetCompanyIdNil

`func (o *InteractionDetails) SetCompanyIdNil(b bool)`

 SetCompanyIdNil sets the value for CompanyId to be an explicit nil

### UnsetCompanyId
`func (o *InteractionDetails) UnsetCompanyId()`

UnsetCompanyId ensures that no value is present for CompanyId, not even an explicit nil
### GetContactId

`func (o *InteractionDetails) GetContactId() string`

GetContactId returns the ContactId field if non-nil, zero value otherwise.

### GetContactIdOk

`func (o *InteractionDetails) GetContactIdOk() (*string, bool)`

GetContactIdOk returns a tuple with the ContactId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetContactId

`func (o *InteractionDetails) SetContactId(v string)`

SetContactId sets ContactId field to given value.

### HasContactId

`func (o *InteractionDetails) HasContactId() bool`

HasContactId returns a boolean if a field has been set.

### SetContactIdNil

`func (o *InteractionDetails) SetContactIdNil(b bool)`

 SetContactIdNil sets the value for ContactId to be an explicit nil

### UnsetContactId
`func (o *InteractionDetails) UnsetContactId()`

UnsetContactId ensures that no value is present for ContactId, not even an explicit nil
### GetInteractionType

`func (o *InteractionDetails) GetInteractionType() string`

GetInteractionType returns the InteractionType field if non-nil, zero value otherwise.

### GetInteractionTypeOk

`func (o *InteractionDetails) GetInteractionTypeOk() (*string, bool)`

GetInteractionTypeOk returns a tuple with the InteractionType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetInteractionType

`func (o *InteractionDetails) SetInteractionType(v string)`

SetInteractionType sets InteractionType field to given value.

### HasInteractionType

`func (o *InteractionDetails) HasInteractionType() bool`

HasInteractionType returns a boolean if a field has been set.

### GetNotes

`func (o *InteractionDetails) GetNotes() string`

GetNotes returns the Notes field if non-nil, zero value otherwise.

### GetNotesOk

`func (o *InteractionDetails) GetNotesOk() (*string, bool)`

GetNotesOk returns a tuple with the Notes field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetNotes

`func (o *InteractionDetails) SetNotes(v string)`

SetNotes sets Notes field to given value.

### HasNotes

`func (o *InteractionDetails) HasNotes() bool`

HasNotes returns a boolean if a field has been set.

### GetInteractionDatetime

`func (o *InteractionDetails) GetInteractionDatetime() time.Time`

GetInteractionDatetime returns the InteractionDatetime field if non-nil, zero value otherwise.

### GetInteractionDatetimeOk

`func (o *InteractionDetails) GetInteractionDatetimeOk() (*time.Time, bool)`

GetInteractionDatetimeOk returns a tuple with the InteractionDatetime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetInteractionDatetime

`func (o *InteractionDetails) SetInteractionDatetime(v time.Time)`

SetInteractionDatetime sets InteractionDatetime field to given value.

### HasInteractionDatetime

`func (o *InteractionDetails) HasInteractionDatetime() bool`

HasInteractionDatetime returns a boolean if a field has been set.

### GetCreatedBy

`func (o *InteractionDetails) GetCreatedBy() string`

GetCreatedBy returns the CreatedBy field if non-nil, zero value otherwise.

### GetCreatedByOk

`func (o *InteractionDetails) GetCreatedByOk() (*string, bool)`

GetCreatedByOk returns a tuple with the CreatedBy field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreatedBy

`func (o *InteractionDetails) SetCreatedBy(v string)`

SetCreatedBy sets CreatedBy field to given value.

### HasCreatedBy

`func (o *InteractionDetails) HasCreatedBy() bool`

HasCreatedBy returns a boolean if a field has been set.

### GetCreatedAt

`func (o *InteractionDetails) GetCreatedAt() time.Time`

GetCreatedAt returns the CreatedAt field if non-nil, zero value otherwise.

### GetCreatedAtOk

`func (o *InteractionDetails) GetCreatedAtOk() (*time.Time, bool)`

GetCreatedAtOk returns a tuple with the CreatedAt field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreatedAt

`func (o *InteractionDetails) SetCreatedAt(v time.Time)`

SetCreatedAt sets CreatedAt field to given value.

### HasCreatedAt

`func (o *InteractionDetails) HasCreatedAt() bool`

HasCreatedAt returns a boolean if a field has been set.

### GetUpdatedAt

`func (o *InteractionDetails) GetUpdatedAt() time.Time`

GetUpdatedAt returns the UpdatedAt field if non-nil, zero value otherwise.

### GetUpdatedAtOk

`func (o *InteractionDetails) GetUpdatedAtOk() (*time.Time, bool)`

GetUpdatedAtOk returns a tuple with the UpdatedAt field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUpdatedAt

`func (o *InteractionDetails) SetUpdatedAt(v time.Time)`

SetUpdatedAt sets UpdatedAt field to given value.

### HasUpdatedAt

`func (o *InteractionDetails) HasUpdatedAt() bool`

HasUpdatedAt returns a boolean if a field has been set.

### GetCompany

`func (o *InteractionDetails) GetCompany() InteractionDetailsAllOfCompany`

GetCompany returns the Company field if non-nil, zero value otherwise.

### GetCompanyOk

`func (o *InteractionDetails) GetCompanyOk() (*InteractionDetailsAllOfCompany, bool)`

GetCompanyOk returns a tuple with the Company field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCompany

`func (o *InteractionDetails) SetCompany(v InteractionDetailsAllOfCompany)`

SetCompany sets Company field to given value.

### HasCompany

`func (o *InteractionDetails) HasCompany() bool`

HasCompany returns a boolean if a field has been set.

### SetCompanyNil

`func (o *InteractionDetails) SetCompanyNil(b bool)`

 SetCompanyNil sets the value for Company to be an explicit nil

### UnsetCompany
`func (o *InteractionDetails) UnsetCompany()`

UnsetCompany ensures that no value is present for Company, not even an explicit nil
### GetContact

`func (o *InteractionDetails) GetContact() InteractionDetailsAllOfContact`

GetContact returns the Contact field if non-nil, zero value otherwise.

### GetContactOk

`func (o *InteractionDetails) GetContactOk() (*InteractionDetailsAllOfContact, bool)`

GetContactOk returns a tuple with the Contact field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetContact

`func (o *InteractionDetails) SetContact(v InteractionDetailsAllOfContact)`

SetContact sets Contact field to given value.

### HasContact

`func (o *InteractionDetails) HasContact() bool`

HasContact returns a boolean if a field has been set.

### SetContactNil

`func (o *InteractionDetails) SetContactNil(b bool)`

 SetContactNil sets the value for Contact to be an explicit nil

### UnsetContact
`func (o *InteractionDetails) UnsetContact()`

UnsetContact ensures that no value is present for Contact, not even an explicit nil

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


