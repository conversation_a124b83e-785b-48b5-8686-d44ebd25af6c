/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
)

// checks if the InteractionDetailsAllOfContact type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &InteractionDetailsAllOfContact{}

// InteractionDetailsAllOfContact struct for InteractionDetailsAllOfContact
type InteractionDetailsAllOfContact struct {
	Id *string `json:"id,omitempty"`
	FirstName *string `json:"first_name,omitempty"`
	LastName *string `json:"last_name,omitempty"`
	Email *string `json:"email,omitempty"`
	Phone *string `json:"phone,omitempty"`
	JobTitle *string `json:"job_title,omitempty"`
}

// NewInteractionDetailsAllOfContact instantiates a new InteractionDetailsAllOfContact object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewInteractionDetailsAllOfContact() *InteractionDetailsAllOfContact {
	this := InteractionDetailsAllOfContact{}
	return &this
}

// NewInteractionDetailsAllOfContactWithDefaults instantiates a new InteractionDetailsAllOfContact object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewInteractionDetailsAllOfContactWithDefaults() *InteractionDetailsAllOfContact {
	this := InteractionDetailsAllOfContact{}
	return &this
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *InteractionDetailsAllOfContact) GetId() string {
	if o == nil || IsNil(o.Id) {
		var ret string
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetailsAllOfContact) GetIdOk() (*string, bool) {
	if o == nil || IsNil(o.Id) {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *InteractionDetailsAllOfContact) HasId() bool {
	if o != nil && !IsNil(o.Id) {
		return true
	}

	return false
}

// SetId gets a reference to the given string and assigns it to the Id field.
func (o *InteractionDetailsAllOfContact) SetId(v string) {
	o.Id = &v
}

// GetFirstName returns the FirstName field value if set, zero value otherwise.
func (o *InteractionDetailsAllOfContact) GetFirstName() string {
	if o == nil || IsNil(o.FirstName) {
		var ret string
		return ret
	}
	return *o.FirstName
}

// GetFirstNameOk returns a tuple with the FirstName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetailsAllOfContact) GetFirstNameOk() (*string, bool) {
	if o == nil || IsNil(o.FirstName) {
		return nil, false
	}
	return o.FirstName, true
}

// HasFirstName returns a boolean if a field has been set.
func (o *InteractionDetailsAllOfContact) HasFirstName() bool {
	if o != nil && !IsNil(o.FirstName) {
		return true
	}

	return false
}

// SetFirstName gets a reference to the given string and assigns it to the FirstName field.
func (o *InteractionDetailsAllOfContact) SetFirstName(v string) {
	o.FirstName = &v
}

// GetLastName returns the LastName field value if set, zero value otherwise.
func (o *InteractionDetailsAllOfContact) GetLastName() string {
	if o == nil || IsNil(o.LastName) {
		var ret string
		return ret
	}
	return *o.LastName
}

// GetLastNameOk returns a tuple with the LastName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetailsAllOfContact) GetLastNameOk() (*string, bool) {
	if o == nil || IsNil(o.LastName) {
		return nil, false
	}
	return o.LastName, true
}

// HasLastName returns a boolean if a field has been set.
func (o *InteractionDetailsAllOfContact) HasLastName() bool {
	if o != nil && !IsNil(o.LastName) {
		return true
	}

	return false
}

// SetLastName gets a reference to the given string and assigns it to the LastName field.
func (o *InteractionDetailsAllOfContact) SetLastName(v string) {
	o.LastName = &v
}

// GetEmail returns the Email field value if set, zero value otherwise.
func (o *InteractionDetailsAllOfContact) GetEmail() string {
	if o == nil || IsNil(o.Email) {
		var ret string
		return ret
	}
	return *o.Email
}

// GetEmailOk returns a tuple with the Email field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetailsAllOfContact) GetEmailOk() (*string, bool) {
	if o == nil || IsNil(o.Email) {
		return nil, false
	}
	return o.Email, true
}

// HasEmail returns a boolean if a field has been set.
func (o *InteractionDetailsAllOfContact) HasEmail() bool {
	if o != nil && !IsNil(o.Email) {
		return true
	}

	return false
}

// SetEmail gets a reference to the given string and assigns it to the Email field.
func (o *InteractionDetailsAllOfContact) SetEmail(v string) {
	o.Email = &v
}

// GetPhone returns the Phone field value if set, zero value otherwise.
func (o *InteractionDetailsAllOfContact) GetPhone() string {
	if o == nil || IsNil(o.Phone) {
		var ret string
		return ret
	}
	return *o.Phone
}

// GetPhoneOk returns a tuple with the Phone field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetailsAllOfContact) GetPhoneOk() (*string, bool) {
	if o == nil || IsNil(o.Phone) {
		return nil, false
	}
	return o.Phone, true
}

// HasPhone returns a boolean if a field has been set.
func (o *InteractionDetailsAllOfContact) HasPhone() bool {
	if o != nil && !IsNil(o.Phone) {
		return true
	}

	return false
}

// SetPhone gets a reference to the given string and assigns it to the Phone field.
func (o *InteractionDetailsAllOfContact) SetPhone(v string) {
	o.Phone = &v
}

// GetJobTitle returns the JobTitle field value if set, zero value otherwise.
func (o *InteractionDetailsAllOfContact) GetJobTitle() string {
	if o == nil || IsNil(o.JobTitle) {
		var ret string
		return ret
	}
	return *o.JobTitle
}

// GetJobTitleOk returns a tuple with the JobTitle field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetailsAllOfContact) GetJobTitleOk() (*string, bool) {
	if o == nil || IsNil(o.JobTitle) {
		return nil, false
	}
	return o.JobTitle, true
}

// HasJobTitle returns a boolean if a field has been set.
func (o *InteractionDetailsAllOfContact) HasJobTitle() bool {
	if o != nil && !IsNil(o.JobTitle) {
		return true
	}

	return false
}

// SetJobTitle gets a reference to the given string and assigns it to the JobTitle field.
func (o *InteractionDetailsAllOfContact) SetJobTitle(v string) {
	o.JobTitle = &v
}

func (o InteractionDetailsAllOfContact) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o InteractionDetailsAllOfContact) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Id) {
		toSerialize["id"] = o.Id
	}
	if !IsNil(o.FirstName) {
		toSerialize["first_name"] = o.FirstName
	}
	if !IsNil(o.LastName) {
		toSerialize["last_name"] = o.LastName
	}
	if !IsNil(o.Email) {
		toSerialize["email"] = o.Email
	}
	if !IsNil(o.Phone) {
		toSerialize["phone"] = o.Phone
	}
	if !IsNil(o.JobTitle) {
		toSerialize["job_title"] = o.JobTitle
	}
	return toSerialize, nil
}

type NullableInteractionDetailsAllOfContact struct {
	value *InteractionDetailsAllOfContact
	isSet bool
}

func (v NullableInteractionDetailsAllOfContact) Get() *InteractionDetailsAllOfContact {
	return v.value
}

func (v *NullableInteractionDetailsAllOfContact) Set(val *InteractionDetailsAllOfContact) {
	v.value = val
	v.isSet = true
}

func (v NullableInteractionDetailsAllOfContact) IsSet() bool {
	return v.isSet
}

func (v *NullableInteractionDetailsAllOfContact) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableInteractionDetailsAllOfContact(val *InteractionDetailsAllOfContact) *NullableInteractionDetailsAllOfContact {
	return &NullableInteractionDetailsAllOfContact{value: val, isSet: true}
}

func (v NullableInteractionDetailsAllOfContact) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableInteractionDetailsAllOfContact) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


