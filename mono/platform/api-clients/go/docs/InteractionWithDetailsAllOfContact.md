# InteractionWithDetailsAllOfContact

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Id** | Pointer to **string** |  | [optional] 
**FirstName** | Pointer to **string** |  | [optional] 
**LastName** | Pointer to **string** |  | [optional] 
**Email** | Pointer to **string** |  | [optional] 

## Methods

### NewInteractionWithDetailsAllOfContact

`func NewInteractionWithDetailsAllOfContact() *InteractionWithDetailsAllOfContact`

NewInteractionWithDetailsAllOfContact instantiates a new InteractionWithDetailsAllOfContact object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewInteractionWithDetailsAllOfContactWithDefaults

`func NewInteractionWithDetailsAllOfContactWithDefaults() *InteractionWithDetailsAllOfContact`

NewInteractionWithDetailsAllOfContactWithDefaults instantiates a new InteractionWithDetailsAllOfContact object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetId

`func (o *InteractionWithDetailsAllOfContact) GetId() string`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *InteractionWithDetailsAllOfContact) GetIdOk() (*string, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *InteractionWithDetailsAllOfContact) SetId(v string)`

SetId sets Id field to given value.

### HasId

`func (o *InteractionWithDetailsAllOfContact) HasId() bool`

HasId returns a boolean if a field has been set.

### GetFirstName

`func (o *InteractionWithDetailsAllOfContact) GetFirstName() string`

GetFirstName returns the FirstName field if non-nil, zero value otherwise.

### GetFirstNameOk

`func (o *InteractionWithDetailsAllOfContact) GetFirstNameOk() (*string, bool)`

GetFirstNameOk returns a tuple with the FirstName field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFirstName

`func (o *InteractionWithDetailsAllOfContact) SetFirstName(v string)`

SetFirstName sets FirstName field to given value.

### HasFirstName

`func (o *InteractionWithDetailsAllOfContact) HasFirstName() bool`

HasFirstName returns a boolean if a field has been set.

### GetLastName

`func (o *InteractionWithDetailsAllOfContact) GetLastName() string`

GetLastName returns the LastName field if non-nil, zero value otherwise.

### GetLastNameOk

`func (o *InteractionWithDetailsAllOfContact) GetLastNameOk() (*string, bool)`

GetLastNameOk returns a tuple with the LastName field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetLastName

`func (o *InteractionWithDetailsAllOfContact) SetLastName(v string)`

SetLastName sets LastName field to given value.

### HasLastName

`func (o *InteractionWithDetailsAllOfContact) HasLastName() bool`

HasLastName returns a boolean if a field has been set.

### GetEmail

`func (o *InteractionWithDetailsAllOfContact) GetEmail() string`

GetEmail returns the Email field if non-nil, zero value otherwise.

### GetEmailOk

`func (o *InteractionWithDetailsAllOfContact) GetEmailOk() (*string, bool)`

GetEmailOk returns a tuple with the Email field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetEmail

`func (o *InteractionWithDetailsAllOfContact) SetEmail(v string)`

SetEmail sets Email field to given value.

### HasEmail

`func (o *InteractionWithDetailsAllOfContact) HasEmail() bool`

HasEmail returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


