-- Phase 3: <PERSON>reate function to sync project_lead text with team member name
CREATE OR REPLACE FUNCTION public.sync_project_lead_name()
RETURNS TRIGGER AS $$
BEGIN
  -- Sync project_lead when project_lead_id changes
  IF OLD.project_lead_id IS DISTINCT FROM NEW.project_lead_id THEN
    IF NEW.project_lead_id IS NOT NULL THEN
      SELECT name INTO NEW.project_lead
      FROM public.team_members
      WHERE id = NEW.project_lead_id;
    ELSE
      NEW.project_lead = NULL;
    END IF;
  END IF;
  
  -- Sync customer_lead when customer_lead_id changes
  IF OLD.customer_lead_id IS DISTINCT FROM NEW.customer_lead_id THEN
    IF NEW.customer_lead_id IS NOT NULL THEN
      SELECT name INTO NEW.customer_lead
      FROM public.team_members
      WHERE id = NEW.customer_lead_id;
    ELSE
      NEW.customer_lead = NULL;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON>reate trigger to automatically sync names
CREATE TRIGGER sync_project_team_names
  BEFORE UPDATE ON public.projects
  FOR EACH ROW
  EXECUTE FUNCTION public.sync_project_lead_name();

-- Phase 4: Fix existing data inconsistencies
-- Update project_lead text to match actual team member names
UPDATE public.projects 
SET project_lead = tm.name
FROM public.team_members tm
WHERE projects.project_lead_id = tm.id
  AND (projects.project_lead != tm.name OR projects.project_lead IS NULL);

-- Update customer_lead text to match actual team member names
UPDATE public.projects 
SET customer_lead = tm.name
FROM public.team_members tm
WHERE projects.customer_lead_id = tm.id
  AND (projects.customer_lead != tm.name OR projects.customer_lead IS NULL);

-- Clear customer_lead text where customer_lead_id is null
UPDATE public.projects 
SET customer_lead = NULL
WHERE customer_lead_id IS NULL AND customer_lead IS NOT NULL;