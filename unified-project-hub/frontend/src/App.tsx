import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { Layout } from '@/components/layout/Layout'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

// Pages
import LoginPage from '@/pages/auth/LoginPage'
import RegisterPage from '@/pages/auth/RegisterPage'
import DashboardPage from '@/pages/DashboardPage'
import ProjectsPage from '@/pages/projects/ProjectsPage'
import ProjectDetailPage from '@/pages/projects/ProjectDetailPage'
import TasksPage from '@/pages/tasks/TasksPage'
import CompaniesPage from '@/pages/companies/CompaniesPage'
import ContactsPage from '@/pages/contacts/ContactsPage'
import RequirementsPage from '@/pages/requirements/RequirementsPage'
import ReportsPage from '@/pages/reports/ReportsPage'
import SettingsPage from '@/pages/settings/SettingsPage'
import ProfilePage from '@/pages/profile/ProfilePage'
import NotFoundPage from '@/pages/NotFoundPage'

// Protected Route Component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useAuth()

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!user) {
    return <Navigate to="/login" replace />
  }

  return <>{children}</>
}

// Public Route Component (redirect to dashboard if authenticated)
const PublicRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useAuth()

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (user) {
    return <Navigate to="/" replace />
  }

  return <>{children}</>
}

function App() {
  return (
    <div className="min-h-screen bg-background">
      <Routes>
        {/* Public Routes */}
        <Route
          path="/login"
          element={
            <PublicRoute>
              <LoginPage />
            </PublicRoute>
          }
        />
        <Route
          path="/register"
          element={
            <PublicRoute>
              <RegisterPage />
            </PublicRoute>
          }
        />

        {/* Protected Routes */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          <Route index element={<DashboardPage />} />
          
          {/* Projects */}
          <Route path="projects" element={<ProjectsPage />} />
          <Route path="projects/my" element={<ProjectsPage filter="my" />} />
          <Route path="projects/active" element={<ProjectsPage filter="active" />} />
          <Route path="projects/archived" element={<ProjectsPage filter="archived" />} />
          <Route path="projects/:id" element={<ProjectDetailPage />} />
          
          {/* Tasks */}
          <Route path="tasks" element={<TasksPage />} />
          <Route path="tasks/my" element={<TasksPage filter="my" />} />
          <Route path="tasks/overdue" element={<TasksPage filter="overdue" />} />
          
          {/* Companies & Contacts */}
          <Route path="companies" element={<CompaniesPage />} />
          <Route path="contacts" element={<ContactsPage />} />
          
          {/* Requirements */}
          <Route path="requirements" element={<RequirementsPage />} />
          <Route path="requirements/pending" element={<RequirementsPage filter="pending" />} />
          <Route path="requirements/templates" element={<RequirementsPage filter="templates" />} />
          
          {/* Reports */}
          <Route path="reports" element={<ReportsPage />} />
          <Route path="reports/projects" element={<ReportsPage type="projects" />} />
          <Route path="reports/team" element={<ReportsPage type="team" />} />
          <Route path="reports/customers" element={<ReportsPage type="customers" />} />
          
          {/* Settings & Profile */}
          <Route path="settings" element={<SettingsPage />} />
          <Route path="profile" element={<ProfilePage />} />
        </Route>

        {/* 404 Route */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </div>
  )
}

export default App
