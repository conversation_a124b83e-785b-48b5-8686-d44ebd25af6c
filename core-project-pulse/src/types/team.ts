export interface TeamMember {
  id: string;
  name: string;
  email: string;
  role?: string;
  department?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateTeamMemberData {
  name: string;
  email: string;
  role?: string;
  department?: string;
  is_active?: boolean;
}

export interface UpdateTeamMemberData {
  name?: string;
  email?: string;
  role?: string;
  department?: string;
  is_active?: boolean;
}