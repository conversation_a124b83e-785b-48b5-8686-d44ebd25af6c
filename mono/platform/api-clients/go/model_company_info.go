/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
)

// checks if the CompanyInfo type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &CompanyInfo{}

// CompanyInfo struct for CompanyInfo
type CompanyInfo struct {
	Id *string `json:"id,omitempty"`
	Name *string `json:"name,omitempty"`
	IsDeleted *bool `json:"is_deleted,omitempty"`
}

// NewCompanyInfo instantiates a new CompanyInfo object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewCompanyInfo() *CompanyInfo {
	this := CompanyInfo{}
	return &this
}

// NewCompanyInfoWithDefaults instantiates a new CompanyInfo object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewCompanyInfoWithDefaults() *CompanyInfo {
	this := CompanyInfo{}
	return &this
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *CompanyInfo) GetId() string {
	if o == nil || IsNil(o.Id) {
		var ret string
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyInfo) GetIdOk() (*string, bool) {
	if o == nil || IsNil(o.Id) {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *CompanyInfo) HasId() bool {
	if o != nil && !IsNil(o.Id) {
		return true
	}

	return false
}

// SetId gets a reference to the given string and assigns it to the Id field.
func (o *CompanyInfo) SetId(v string) {
	o.Id = &v
}

// GetName returns the Name field value if set, zero value otherwise.
func (o *CompanyInfo) GetName() string {
	if o == nil || IsNil(o.Name) {
		var ret string
		return ret
	}
	return *o.Name
}

// GetNameOk returns a tuple with the Name field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyInfo) GetNameOk() (*string, bool) {
	if o == nil || IsNil(o.Name) {
		return nil, false
	}
	return o.Name, true
}

// HasName returns a boolean if a field has been set.
func (o *CompanyInfo) HasName() bool {
	if o != nil && !IsNil(o.Name) {
		return true
	}

	return false
}

// SetName gets a reference to the given string and assigns it to the Name field.
func (o *CompanyInfo) SetName(v string) {
	o.Name = &v
}

// GetIsDeleted returns the IsDeleted field value if set, zero value otherwise.
func (o *CompanyInfo) GetIsDeleted() bool {
	if o == nil || IsNil(o.IsDeleted) {
		var ret bool
		return ret
	}
	return *o.IsDeleted
}

// GetIsDeletedOk returns a tuple with the IsDeleted field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyInfo) GetIsDeletedOk() (*bool, bool) {
	if o == nil || IsNil(o.IsDeleted) {
		return nil, false
	}
	return o.IsDeleted, true
}

// HasIsDeleted returns a boolean if a field has been set.
func (o *CompanyInfo) HasIsDeleted() bool {
	if o != nil && !IsNil(o.IsDeleted) {
		return true
	}

	return false
}

// SetIsDeleted gets a reference to the given bool and assigns it to the IsDeleted field.
func (o *CompanyInfo) SetIsDeleted(v bool) {
	o.IsDeleted = &v
}

func (o CompanyInfo) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o CompanyInfo) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Id) {
		toSerialize["id"] = o.Id
	}
	if !IsNil(o.Name) {
		toSerialize["name"] = o.Name
	}
	if !IsNil(o.IsDeleted) {
		toSerialize["is_deleted"] = o.IsDeleted
	}
	return toSerialize, nil
}

type NullableCompanyInfo struct {
	value *CompanyInfo
	isSet bool
}

func (v NullableCompanyInfo) Get() *CompanyInfo {
	return v.value
}

func (v *NullableCompanyInfo) Set(val *CompanyInfo) {
	v.value = val
	v.isSet = true
}

func (v NullableCompanyInfo) IsSet() bool {
	return v.isSet
}

func (v *NullableCompanyInfo) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableCompanyInfo(val *CompanyInfo) *NullableCompanyInfo {
	return &NullableCompanyInfo{value: val, isSet: true}
}

func (v NullableCompanyInfo) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableCompanyInfo) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


