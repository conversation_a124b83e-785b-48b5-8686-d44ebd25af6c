import React from 'react';
import { Search, X, Archive, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';

interface ProjectFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  typeFilter: string;
  onTypeFilterChange: (value: string) => void;
  statusFilter: string;
  onStatusFilterChange: (value: string) => void;
  priorityFilter: string;
  onPriorityFilterChange: (value: string) => void;
  leadFilter: string;
  onLeadFilterChange: (value: string) => void;
  companyFilter: string;
  onCompanyFilterChange: (value: string) => void;
  showArchivedProjects: boolean;
  onShowArchivedProjectsChange: (value: boolean) => void;
  sortBy: string;
  onSortByChange: (value: string) => void;
  sortOrder: string;
  onSortOrderChange: (value: string) => void;
  groupBy: string;
  onGroupByChange: (value: string) => void;
  onClearFilters: () => void;
  archivedProjectsCount?: number;
  teamMembers?: Array<{ id: string; name: string }>;
  companies?: Array<{ id: string; name: string }>;
}

const PROJECT_TYPES = [
  { value: 'all', label: 'All Types' },
  { value: 'internal', label: 'Internal' },
  { value: 'external', label: 'External' },
  { value: 'hybrid', label: 'Hybrid' }
];

const PROJECT_STATUSES = [
  { value: 'all', label: 'All Statuses' },
  { value: 'draft', label: 'Draft' },
  { value: 'requirements', label: 'Requirements' },
  { value: 'backlog', label: 'Backlog' },
  { value: 'not_started', label: 'Not Started' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'completed', label: 'Completed' },
  { value: 'on_hold', label: 'On Hold' }
];

const PRIORITY_LEVELS = [
  { value: 'all', label: 'All Priorities' },
  { value: 'P0', label: 'P0 (Critical)' },
  { value: 'P1', label: 'P1 (High)' },
  { value: 'P2', label: 'P2 (Medium)' },
  { value: 'P3', label: 'P3 (Low)' },
  { value: 'P4', label: 'P4 (Lowest)' }
];

const SORT_OPTIONS = [
  { value: 'name', label: 'Name' },
  { value: 'created_at', label: 'Created Date' },
  { value: 'updated_at', label: 'Updated Date' },
  { value: 'start_date', label: 'Start Date' },
  { value: 'end_date', label: 'End Date' },
  { value: 'priority_level', label: 'Priority' },
  { value: 'status', label: 'Status' }
];

const GROUP_OPTIONS = [
  { value: 'none', label: 'No Grouping' },
  { value: 'status', label: 'Status' },
  { value: 'priority_level', label: 'Priority' },
  { value: 'lead', label: 'Project Lead' },
  { value: 'start_date', label: 'Start Date' },
  { value: 'end_date', label: 'End Date' }
];

export function ProjectFilters({
  searchTerm,
  onSearchChange,
  typeFilter,
  onTypeFilterChange,
  statusFilter,
  onStatusFilterChange,
  priorityFilter,
  onPriorityFilterChange,
  leadFilter,
  onLeadFilterChange,
  companyFilter,
  onCompanyFilterChange,
  showArchivedProjects,
  onShowArchivedProjectsChange,
  sortBy,
  onSortByChange,
  sortOrder,
  onSortOrderChange,
  groupBy,
  onGroupByChange,
  onClearFilters,
  archivedProjectsCount = 0,
  teamMembers = [],
  companies = []
}: ProjectFiltersProps) {
  const hasActiveFilters = 
    typeFilter !== 'all' || 
    statusFilter !== 'all' || 
    priorityFilter !== 'all' || 
    leadFilter !== 'all' || 
    companyFilter !== 'all' || 
    searchTerm || 
    groupBy !== 'none';

  return (
    <div className="space-y-4">
      {/* Archive Mode Banner */}
      {showArchivedProjects && (
        <div className="bg-muted/50 border border-border rounded-lg p-4">
          <div className="flex items-center gap-2">
            <Archive className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Archive Mode</span>
            <Badge variant="secondary" className="ml-auto">
              {archivedProjectsCount} archived project{archivedProjectsCount !== 1 ? 's' : ''}
            </Badge>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Viewing only archived projects. Toggle off to see active projects.
          </p>
        </div>
      )}

      {/* Search and Archive Toggle */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search projects by name, description, or company..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-8"
          />
        </div>
        <div className="flex items-center space-x-2">
          <Switch
            id="show-archived"
            checked={showArchivedProjects}
            onCheckedChange={onShowArchivedProjectsChange}
          />
          <Label htmlFor="show-archived" className="text-sm whitespace-nowrap flex items-center gap-2">
            <Archive className="h-3 w-3" />
            View archived projects
            {archivedProjectsCount > 0 && (
              <Badge variant="outline" className="ml-1 text-xs">
                {archivedProjectsCount}
              </Badge>
            )}
          </Label>
        </div>
      </div>
      
      {/* Filters Row */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Select value={typeFilter} onValueChange={onTypeFilterChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Project Type" />
          </SelectTrigger>
          <SelectContent>
            {PROJECT_TYPES.map((type) => (
              <SelectItem key={type.value} value={type.value}>
                {type.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={statusFilter} onValueChange={onStatusFilterChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            {PROJECT_STATUSES.map((status) => (
              <SelectItem key={status.value} value={status.value}>
                {status.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={priorityFilter} onValueChange={onPriorityFilterChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Priority" />
          </SelectTrigger>
          <SelectContent>
            {PRIORITY_LEVELS.map((priority) => (
              <SelectItem key={priority.value} value={priority.value}>
                {priority.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={leadFilter} onValueChange={onLeadFilterChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Project Lead" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Leads</SelectItem>
            {teamMembers.map((member) => (
              <SelectItem key={member.id} value={member.id}>
                {member.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={companyFilter} onValueChange={onCompanyFilterChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Company" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Companies</SelectItem>
            {companies.map((company) => (
              <SelectItem key={company.id} value={company.id}>
                {company.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Sorting and Grouping */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Select value={sortBy} onValueChange={onSortByChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            {SORT_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={sortOrder} onValueChange={onSortOrderChange}>
          <SelectTrigger className="w-full sm:w-[120px]">
            <SelectValue placeholder="Order" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="asc">Ascending</SelectItem>
            <SelectItem value="desc">Descending</SelectItem>
          </SelectContent>
        </Select>

        <Select value={groupBy} onValueChange={onGroupByChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Group by" />
          </SelectTrigger>
          <SelectContent>
            {GROUP_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {hasActiveFilters && (
          <Button
            variant="outline"
            onClick={onClearFilters}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  );
}
