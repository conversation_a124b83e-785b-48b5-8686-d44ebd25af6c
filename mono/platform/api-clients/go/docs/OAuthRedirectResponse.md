# OAuthRedirectResponse

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Url** | Pointer to **string** |  | [optional] 

## Methods

### NewOAuthRedirectResponse

`func NewOAuthRedirectResponse() *OAuthRedirectResponse`

NewOAuthRedirectResponse instantiates a new OAuthRedirectResponse object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewOAuthRedirectResponseWithDefaults

`func NewOAuthRedirectResponseWithDefaults() *OAuthRedirectResponse`

NewOAuthRedirectResponseWithDefaults instantiates a new OAuthRedirectResponse object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetUrl

`func (o *OAuthRedirectResponse) GetUrl() string`

GetUrl returns the Url field if non-nil, zero value otherwise.

### GetUrlOk

`func (o *OAuthRedirectResponse) GetUrlOk() (*string, bool)`

GetUrlOk returns a tuple with the Url field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUrl

`func (o *OAuthRedirectResponse) SetUrl(v string)`

SetUrl sets Url field to given value.

### HasUrl

`func (o *OAuthRedirectResponse) HasUrl() bool`

HasUrl returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


