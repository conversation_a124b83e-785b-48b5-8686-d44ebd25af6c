/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import java.util.Objects;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.util.Arrays;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.openapitools.client.JSON;

/**
 * ArliDocumentsVectorizePost200Response
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-07-14T08:13:23.820009+10:00[Australia/Brisbane]", comments = "Generator version: 7.9.0")
public class ArliDocumentsVectorizePost200Response {
  public static final String SERIALIZED_NAME_SUCCESS = "success";
  @SerializedName(SERIALIZED_NAME_SUCCESS)
  private Boolean success;

  public static final String SERIALIZED_NAME_MESSAGE = "message";
  @SerializedName(SERIALIZED_NAME_MESSAGE)
  private String message;

  public static final String SERIALIZED_NAME_VECTOR_ID = "vector_id";
  @SerializedName(SERIALIZED_NAME_VECTOR_ID)
  private String vectorId;

  public ArliDocumentsVectorizePost200Response() {
  }

  public ArliDocumentsVectorizePost200Response success(Boolean success) {
    this.success = success;
    return this;
  }

  /**
   * Get success
   * @return success
   */
  @javax.annotation.Nullable
  public Boolean getSuccess() {
    return success;
  }

  public void setSuccess(Boolean success) {
    this.success = success;
  }


  public ArliDocumentsVectorizePost200Response message(String message) {
    this.message = message;
    return this;
  }

  /**
   * Get message
   * @return message
   */
  @javax.annotation.Nullable
  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }


  public ArliDocumentsVectorizePost200Response vectorId(String vectorId) {
    this.vectorId = vectorId;
    return this;
  }

  /**
   * Get vectorId
   * @return vectorId
   */
  @javax.annotation.Nullable
  public String getVectorId() {
    return vectorId;
  }

  public void setVectorId(String vectorId) {
    this.vectorId = vectorId;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ArliDocumentsVectorizePost200Response arliDocumentsVectorizePost200Response = (ArliDocumentsVectorizePost200Response) o;
    return Objects.equals(this.success, arliDocumentsVectorizePost200Response.success) &&
        Objects.equals(this.message, arliDocumentsVectorizePost200Response.message) &&
        Objects.equals(this.vectorId, arliDocumentsVectorizePost200Response.vectorId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(success, message, vectorId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ArliDocumentsVectorizePost200Response {\n");
    sb.append("    success: ").append(toIndentedString(success)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    vectorId: ").append(toIndentedString(vectorId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("success");
    openapiFields.add("message");
    openapiFields.add("vector_id");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

  /**
   * Validates the JSON Element and throws an exception if issues found
   *
   * @param jsonElement JSON Element
   * @throws IOException if the JSON Element is invalid with respect to ArliDocumentsVectorizePost200Response
   */
  public static void validateJsonElement(JsonElement jsonElement) throws IOException {
      if (jsonElement == null) {
        if (!ArliDocumentsVectorizePost200Response.openapiRequiredFields.isEmpty()) { // has required fields but JSON element is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in ArliDocumentsVectorizePost200Response is not found in the empty JSON string", ArliDocumentsVectorizePost200Response.openapiRequiredFields.toString()));
        }
      }

      Set<Map.Entry<String, JsonElement>> entries = jsonElement.getAsJsonObject().entrySet();
      // check to see if the JSON string contains additional fields
      for (Map.Entry<String, JsonElement> entry : entries) {
        if (!ArliDocumentsVectorizePost200Response.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `ArliDocumentsVectorizePost200Response` properties. JSON: %s", entry.getKey(), jsonElement.toString()));
        }
      }
        JsonObject jsonObj = jsonElement.getAsJsonObject();
      if ((jsonObj.get("message") != null && !jsonObj.get("message").isJsonNull()) && !jsonObj.get("message").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `message` to be a primitive type in the JSON string but got `%s`", jsonObj.get("message").toString()));
      }
      if ((jsonObj.get("vector_id") != null && !jsonObj.get("vector_id").isJsonNull()) && !jsonObj.get("vector_id").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `vector_id` to be a primitive type in the JSON string but got `%s`", jsonObj.get("vector_id").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!ArliDocumentsVectorizePost200Response.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'ArliDocumentsVectorizePost200Response' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<ArliDocumentsVectorizePost200Response> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(ArliDocumentsVectorizePost200Response.class));

       return (TypeAdapter<T>) new TypeAdapter<ArliDocumentsVectorizePost200Response>() {
           @Override
           public void write(JsonWriter out, ArliDocumentsVectorizePost200Response value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public ArliDocumentsVectorizePost200Response read(JsonReader in) throws IOException {
             JsonElement jsonElement = elementAdapter.read(in);
             validateJsonElement(jsonElement);
             return thisAdapter.fromJsonTree(jsonElement);
           }

       }.nullSafe();
    }
  }

  /**
   * Create an instance of ArliDocumentsVectorizePost200Response given an JSON string
   *
   * @param jsonString JSON string
   * @return An instance of ArliDocumentsVectorizePost200Response
   * @throws IOException if the JSON string is invalid with respect to ArliDocumentsVectorizePost200Response
   */
  public static ArliDocumentsVectorizePost200Response fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, ArliDocumentsVectorizePost200Response.class);
  }

  /**
   * Convert an instance of ArliDocumentsVectorizePost200Response to an JSON string
   *
   * @return JSON string
   */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

