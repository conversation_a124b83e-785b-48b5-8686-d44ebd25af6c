# coding: utf-8

"""
    CRM API

    Complete API specification for the CRM system

    The version of the OpenAPI document: 1.0.0
    Contact: <EMAIL>
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import date, datetime
from pydantic import BaseModel, ConfigDict, StrictFloat, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional, Union
from crm_api_client.models.company import Company
from crm_api_client.models.deal_stage import DealStage
from typing import Optional, Set
from typing_extensions import Self

class DealDetails(BaseModel):
    """
    DealDetails
    """ # noqa: E501
    id: Optional[StrictStr] = None
    title: Optional[StrictStr] = None
    description: Optional[StrictStr] = None
    estimated_value: Optional[Union[StrictFloat, StrictInt]] = None
    company_id: Optional[StrictStr] = None
    deal_stage_id: Optional[StrictStr] = None
    expected_close_date: Optional[date] = None
    created_by: Optional[StrictStr] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    company: Optional[Company] = None
    deal_stage: Optional[DealStage] = None
    __properties: ClassVar[List[str]] = ["id", "title", "description", "estimated_value", "company_id", "deal_stage_id", "expected_close_date", "created_by", "created_at", "updated_at", "company", "deal_stage"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of DealDetails from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of company
        if self.company:
            _dict['company'] = self.company.to_dict()
        # override the default output from pydantic by calling `to_dict()` of deal_stage
        if self.deal_stage:
            _dict['deal_stage'] = self.deal_stage.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of DealDetails from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "id": obj.get("id"),
            "title": obj.get("title"),
            "description": obj.get("description"),
            "estimated_value": obj.get("estimated_value"),
            "company_id": obj.get("company_id"),
            "deal_stage_id": obj.get("deal_stage_id"),
            "expected_close_date": obj.get("expected_close_date"),
            "created_by": obj.get("created_by"),
            "created_at": obj.get("created_at"),
            "updated_at": obj.get("updated_at"),
            "company": Company.from_dict(obj["company"]) if obj.get("company") is not None else None,
            "deal_stage": DealStage.from_dict(obj["deal_stage"]) if obj.get("deal_stage") is not None else None
        })
        return _obj


