import { useAuth } from '@/contexts/AuthContext'

interface ReportsPageProps {
  type?: 'projects' | 'team' | 'customers'
}

export default function ReportsPage({ type }: ReportsPageProps) {
  const { user } = useAuth()

  // Only admin and project managers can access reports
  if (user?.role !== 'admin' && user?.role !== 'project_manager') {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Access Denied</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>You don't have permission to access reports.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const getPageTitle = () => {
    switch (type) {
      case 'projects':
        return 'Project Analytics'
      case 'team':
        return 'Team Performance'
      case 'customers':
        return 'Customer Reports'
      default:
        return 'Reports & Analytics'
    }
  }

  const getPageDescription = () => {
    switch (type) {
      case 'projects':
        return 'Analyze project performance, timelines, and deliverables'
      case 'team':
        return 'Track team productivity, capacity, and workload distribution'
      case 'customers':
        return 'Customer satisfaction, project outcomes, and business metrics'
      default:
        return 'Comprehensive analytics and reporting dashboard'
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">{getPageTitle()}</h1>
        <p className="text-gray-600 mt-1">{getPageDescription()}</p>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">Reports feature coming soon</h3>
          <p className="mt-1 text-sm text-gray-500">
            Advanced analytics and reporting functionality will be available in the next update.
          </p>
        </div>
      </div>
    </div>
  )
}
