import React from 'react';
import { TaskCard } from './TaskCard';

interface TaskGridViewProps {
  tasks: any[];
  groupedTasks?: Record<string, any[]>;
  groupBy?: string;
  onEdit?: (task: any) => void;
  onDelete?: (task: any) => void;
  onAssign?: (task: any) => void;
  onUpdateStatus?: (task: any, status: string) => void;
  onUpdatePriority?: (task: any, priority: string) => void;
  showActions?: boolean;
  showProject?: boolean;
}

export function TaskGridView({
  tasks,
  groupedTasks,
  groupBy = 'none',
  onEdit,
  onDelete,
  onAssign,
  onUpdateStatus,
  onUpdatePriority,
  showActions = true,
  showProject = true
}: TaskGridViewProps) {
  if (groupBy !== 'none' && groupedTasks) {
    return (
      <div className="space-y-8">
        {Object.entries(groupedTasks).map(([groupKey, groupTasks]) => (
          <div key={groupKey} className="space-y-4">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold">{groupKey}</h3>
              <span className="text-sm text-muted-foreground">
                ({groupTasks.length} task{groupTasks.length !== 1 ? 's' : ''})
              </span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {groupTasks.map((task) => (
                <TaskCard
                  key={task.id}
                  task={task}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onAssign={onAssign}
                  onUpdateStatus={onUpdateStatus}
                  onUpdatePriority={onUpdatePriority}
                  showActions={showActions}
                  showProject={showProject}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {tasks.map((task) => (
        <TaskCard
          key={task.id}
          task={task}
          onEdit={onEdit}
          onDelete={onDelete}
          onAssign={onAssign}
          onUpdateStatus={onUpdateStatus}
          onUpdatePriority={onUpdatePriority}
          showActions={showActions}
          showProject={showProject}
        />
      ))}
    </div>
  );
}
