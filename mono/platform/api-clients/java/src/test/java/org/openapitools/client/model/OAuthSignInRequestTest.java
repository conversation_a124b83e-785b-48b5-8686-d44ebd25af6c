/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.net.URI;
import java.util.Arrays;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

/**
 * Model tests for OAuthSignInRequest
 */
public class OAuthSignInRequestTest {
    private final OAuthSignInRequest model = new OAuthSignInRequest();

    /**
     * Model tests for OAuthSignInRequest
     */
    @Test
    public void testOAuthSignInRequest() {
        // TODO: test OAuthSignInRequest
    }

    /**
     * Test the property 'provider'
     */
    @Test
    public void providerTest() {
        // TODO: test provider
    }

    /**
     * Test the property 'redirectTo'
     */
    @Test
    public void redirectToTest() {
        // TODO: test redirectTo
    }

}
