# DealStage

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Id** | Pointer to **string** |  | [optional] 
**Name** | Pointer to **string** |  | [optional] 
**PipelineOrder** | Pointer to **int32** |  | [optional] 
**IsClosedWon** | Pointer to **bool** |  | [optional] [default to false]
**IsClosedLost** | Pointer to **bool** |  | [optional] [default to false]
**CreatedAt** | Pointer to **time.Time** |  | [optional] 

## Methods

### NewDealStage

`func NewDealStage() *DealStage`

NewDealStage instantiates a new DealStage object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDealStageWithDefaults

`func NewDealStageWithDefaults() *DealStage`

NewDealStageWithDefaults instantiates a new DealStage object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetId

`func (o *DealStage) GetId() string`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *DealStage) GetIdOk() (*string, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *DealStage) SetId(v string)`

SetId sets Id field to given value.

### HasId

`func (o *DealStage) HasId() bool`

HasId returns a boolean if a field has been set.

### GetName

`func (o *DealStage) GetName() string`

GetName returns the Name field if non-nil, zero value otherwise.

### GetNameOk

`func (o *DealStage) GetNameOk() (*string, bool)`

GetNameOk returns a tuple with the Name field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetName

`func (o *DealStage) SetName(v string)`

SetName sets Name field to given value.

### HasName

`func (o *DealStage) HasName() bool`

HasName returns a boolean if a field has been set.

### GetPipelineOrder

`func (o *DealStage) GetPipelineOrder() int32`

GetPipelineOrder returns the PipelineOrder field if non-nil, zero value otherwise.

### GetPipelineOrderOk

`func (o *DealStage) GetPipelineOrderOk() (*int32, bool)`

GetPipelineOrderOk returns a tuple with the PipelineOrder field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPipelineOrder

`func (o *DealStage) SetPipelineOrder(v int32)`

SetPipelineOrder sets PipelineOrder field to given value.

### HasPipelineOrder

`func (o *DealStage) HasPipelineOrder() bool`

HasPipelineOrder returns a boolean if a field has been set.

### GetIsClosedWon

`func (o *DealStage) GetIsClosedWon() bool`

GetIsClosedWon returns the IsClosedWon field if non-nil, zero value otherwise.

### GetIsClosedWonOk

`func (o *DealStage) GetIsClosedWonOk() (*bool, bool)`

GetIsClosedWonOk returns a tuple with the IsClosedWon field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetIsClosedWon

`func (o *DealStage) SetIsClosedWon(v bool)`

SetIsClosedWon sets IsClosedWon field to given value.

### HasIsClosedWon

`func (o *DealStage) HasIsClosedWon() bool`

HasIsClosedWon returns a boolean if a field has been set.

### GetIsClosedLost

`func (o *DealStage) GetIsClosedLost() bool`

GetIsClosedLost returns the IsClosedLost field if non-nil, zero value otherwise.

### GetIsClosedLostOk

`func (o *DealStage) GetIsClosedLostOk() (*bool, bool)`

GetIsClosedLostOk returns a tuple with the IsClosedLost field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetIsClosedLost

`func (o *DealStage) SetIsClosedLost(v bool)`

SetIsClosedLost sets IsClosedLost field to given value.

### HasIsClosedLost

`func (o *DealStage) HasIsClosedLost() bool`

HasIsClosedLost returns a boolean if a field has been set.

### GetCreatedAt

`func (o *DealStage) GetCreatedAt() time.Time`

GetCreatedAt returns the CreatedAt field if non-nil, zero value otherwise.

### GetCreatedAtOk

`func (o *DealStage) GetCreatedAtOk() (*time.Time, bool)`

GetCreatedAtOk returns a tuple with the CreatedAt field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreatedAt

`func (o *DealStage) SetCreatedAt(v time.Time)`

SetCreatedAt sets CreatedAt field to given value.

### HasCreatedAt

`func (o *DealStage) HasCreatedAt() bool`

HasCreatedAt returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


