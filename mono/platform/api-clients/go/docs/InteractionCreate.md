# InteractionCreate

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CompanyId** | Pointer to **string** |  | [optional] 
**ContactId** | Pointer to **string** |  | [optional] 
**InteractionType** | **string** |  | 
**Notes** | **string** |  | 
**InteractionDatetime** | **time.Time** |  | 

## Methods

### NewInteractionCreate

`func NewInteractionCreate(interactionType string, notes string, interactionDatetime time.Time, ) *InteractionCreate`

NewInteractionCreate instantiates a new InteractionCreate object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewInteractionCreateWithDefaults

`func NewInteractionCreateWithDefaults() *InteractionCreate`

NewInteractionCreateWithDefaults instantiates a new InteractionCreate object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetCompanyId

`func (o *InteractionCreate) GetCompanyId() string`

GetCompanyId returns the CompanyId field if non-nil, zero value otherwise.

### GetCompanyIdOk

`func (o *InteractionCreate) GetCompanyIdOk() (*string, bool)`

GetCompanyIdOk returns a tuple with the CompanyId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCompanyId

`func (o *InteractionCreate) SetCompanyId(v string)`

SetCompanyId sets CompanyId field to given value.

### HasCompanyId

`func (o *InteractionCreate) HasCompanyId() bool`

HasCompanyId returns a boolean if a field has been set.

### GetContactId

`func (o *InteractionCreate) GetContactId() string`

GetContactId returns the ContactId field if non-nil, zero value otherwise.

### GetContactIdOk

`func (o *InteractionCreate) GetContactIdOk() (*string, bool)`

GetContactIdOk returns a tuple with the ContactId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetContactId

`func (o *InteractionCreate) SetContactId(v string)`

SetContactId sets ContactId field to given value.

### HasContactId

`func (o *InteractionCreate) HasContactId() bool`

HasContactId returns a boolean if a field has been set.

### GetInteractionType

`func (o *InteractionCreate) GetInteractionType() string`

GetInteractionType returns the InteractionType field if non-nil, zero value otherwise.

### GetInteractionTypeOk

`func (o *InteractionCreate) GetInteractionTypeOk() (*string, bool)`

GetInteractionTypeOk returns a tuple with the InteractionType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetInteractionType

`func (o *InteractionCreate) SetInteractionType(v string)`

SetInteractionType sets InteractionType field to given value.


### GetNotes

`func (o *InteractionCreate) GetNotes() string`

GetNotes returns the Notes field if non-nil, zero value otherwise.

### GetNotesOk

`func (o *InteractionCreate) GetNotesOk() (*string, bool)`

GetNotesOk returns a tuple with the Notes field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetNotes

`func (o *InteractionCreate) SetNotes(v string)`

SetNotes sets Notes field to given value.


### GetInteractionDatetime

`func (o *InteractionCreate) GetInteractionDatetime() time.Time`

GetInteractionDatetime returns the InteractionDatetime field if non-nil, zero value otherwise.

### GetInteractionDatetimeOk

`func (o *InteractionCreate) GetInteractionDatetimeOk() (*time.Time, bool)`

GetInteractionDatetimeOk returns a tuple with the InteractionDatetime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetInteractionDatetime

`func (o *InteractionCreate) SetInteractionDatetime(v time.Time)`

SetInteractionDatetime sets InteractionDatetime field to given value.



[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


