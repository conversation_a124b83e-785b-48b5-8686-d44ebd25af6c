# CompaniesApi

All URIs are relative to *https://api.crm.example.com/v1*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**companiesGet**](CompaniesApi.md#companiesGet) | **GET** /companies | List companies |
| [**companiesIdDelete**](CompaniesApi.md#companiesIdDelete) | **DELETE** /companies/{id} | Delete company |
| [**companiesIdGet**](CompaniesApi.md#companiesIdGet) | **GET** /companies/{id} | Get company |
| [**companiesIdPut**](CompaniesApi.md#companiesIdPut) | **PUT** /companies/{id} | Update company |
| [**companiesIdStatusPut**](CompaniesApi.md#companiesIdStatusPut) | **PUT** /companies/{id}/status | Update company status |
| [**companiesPost**](CompaniesApi.md#companiesPost) | **POST** /companies | Create company |
| [**companyStatusesGet**](CompaniesApi.md#companyStatusesGet) | **GET** /company-statuses | List company statuses |


<a id="companiesGet"></a>
# **companiesGet**
> List&lt;Company&gt; companiesGet(statusId, includeDeleted)

List companies

Retrieve a list of all active companies

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.CompaniesApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    CompaniesApi apiInstance = new CompaniesApi(defaultClient);
    UUID statusId = UUID.randomUUID(); // UUID | Filter by company status
    Boolean includeDeleted = false; // Boolean | Include soft-deleted companies
    try {
      List<Company> result = apiInstance.companiesGet(statusId, includeDeleted);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling CompaniesApi#companiesGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **statusId** | **UUID**| Filter by company status | [optional] |
| **includeDeleted** | **Boolean**| Include soft-deleted companies | [optional] [default to false] |

### Return type

[**List&lt;Company&gt;**](Company.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | List of companies |  -  |

<a id="companiesIdDelete"></a>
# **companiesIdDelete**
> companiesIdDelete(id)

Delete company

Soft delete a company (sets is_deleted to true)

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.CompaniesApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    CompaniesApi apiInstance = new CompaniesApi(defaultClient);
    UUID id = UUID.randomUUID(); // UUID | 
    try {
      apiInstance.companiesIdDelete(id);
    } catch (ApiException e) {
      System.err.println("Exception when calling CompaniesApi#companiesIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **id** | **UUID**|  | |

### Return type

null (empty response body)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **204** | Company deleted successfully |  -  |
| **404** | Company not found |  -  |

<a id="companiesIdGet"></a>
# **companiesIdGet**
> CompanyDetails companiesIdGet(id)

Get company

Retrieve a specific company by ID

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.CompaniesApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    CompaniesApi apiInstance = new CompaniesApi(defaultClient);
    UUID id = UUID.randomUUID(); // UUID | 
    try {
      CompanyDetails result = apiInstance.companiesIdGet(id);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling CompaniesApi#companiesIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **id** | **UUID**|  | |

### Return type

[**CompanyDetails**](CompanyDetails.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Company details |  -  |
| **404** | Company not found |  -  |

<a id="companiesIdPut"></a>
# **companiesIdPut**
> Company companiesIdPut(id, companyUpdate)

Update company

Update an existing company

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.CompaniesApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    CompaniesApi apiInstance = new CompaniesApi(defaultClient);
    UUID id = UUID.randomUUID(); // UUID | 
    CompanyUpdate companyUpdate = new CompanyUpdate(); // CompanyUpdate | 
    try {
      Company result = apiInstance.companiesIdPut(id, companyUpdate);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling CompaniesApi#companiesIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **id** | **UUID**|  | |
| **companyUpdate** | [**CompanyUpdate**](CompanyUpdate.md)|  | |

### Return type

[**Company**](Company.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Company updated successfully |  -  |
| **404** | Company not found |  -  |

<a id="companiesIdStatusPut"></a>
# **companiesIdStatusPut**
> companiesIdStatusPut(id, companyStatusUpdateRequest)

Update company status

Update the status of a company

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.CompaniesApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    CompaniesApi apiInstance = new CompaniesApi(defaultClient);
    UUID id = UUID.randomUUID(); // UUID | 
    CompanyStatusUpdateRequest companyStatusUpdateRequest = new CompanyStatusUpdateRequest(); // CompanyStatusUpdateRequest | 
    try {
      apiInstance.companiesIdStatusPut(id, companyStatusUpdateRequest);
    } catch (ApiException e) {
      System.err.println("Exception when calling CompaniesApi#companiesIdStatusPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **id** | **UUID**|  | |
| **companyStatusUpdateRequest** | [**CompanyStatusUpdateRequest**](CompanyStatusUpdateRequest.md)|  | |

### Return type

null (empty response body)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Company status updated successfully |  -  |
| **404** | Company not found |  -  |

<a id="companiesPost"></a>
# **companiesPost**
> Company companiesPost(companyCreate)

Create company

Create a new company

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.CompaniesApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    CompaniesApi apiInstance = new CompaniesApi(defaultClient);
    CompanyCreate companyCreate = new CompanyCreate(); // CompanyCreate | 
    try {
      Company result = apiInstance.companiesPost(companyCreate);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling CompaniesApi#companiesPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **companyCreate** | [**CompanyCreate**](CompanyCreate.md)|  | |

### Return type

[**Company**](Company.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **201** | Company created successfully |  -  |
| **400** | Invalid input |  -  |

<a id="companyStatusesGet"></a>
# **companyStatusesGet**
> List&lt;CompanyStatus&gt; companyStatusesGet()

List company statuses

Retrieve all company statuses ordered by pipeline order

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.CompaniesApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    CompaniesApi apiInstance = new CompaniesApi(defaultClient);
    try {
      List<CompanyStatus> result = apiInstance.companyStatusesGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling CompaniesApi#companyStatusesGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;CompanyStatus&gt;**](CompanyStatus.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | List of company statuses |  -  |

