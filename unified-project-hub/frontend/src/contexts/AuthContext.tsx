import React, { createContext, useContext, useEffect, useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { authApi } from '@/services/api'
import { User, LoginRequest, RegisterRequest } from '@/types'
import { toast } from '@/hooks/use-toast'

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (credentials: LoginRequest) => Promise<void>
  register: (userData: RegisterRequest) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const queryClient = useQueryClient()

  // Get stored tokens
  const getStoredTokens = () => {
    const accessToken = localStorage.getItem('accessToken')
    const refreshToken = localStorage.getItem('refreshToken')
    return { accessToken, refreshToken }
  }

  // Store tokens
  const storeTokens = (accessToken: string, refreshToken: string) => {
    localStorage.setItem('accessToken', accessToken)
    localStorage.setItem('refreshToken', refreshToken)
  }

  // Clear tokens
  const clearTokens = () => {
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
  }

  // Fetch current user
  const { isLoading } = useQuery({
    queryKey: ['auth', 'me'],
    queryFn: authApi.getCurrentUser,
    enabled: !!getStoredTokens().accessToken,
    retry: false,
    onSuccess: (data) => {
      setUser(data.user)
    },
    onError: () => {
      clearTokens()
      setUser(null)
    },
  })

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: authApi.login,
    onSuccess: (data) => {
      storeTokens(data.accessToken, data.refreshToken)
      setUser(data.user)
      queryClient.setQueryData(['auth', 'me'], data)
      toast({
        title: 'Welcome back!',
        description: 'You have been successfully logged in.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Login failed',
        description: error.response?.data?.message || 'Invalid credentials',
        variant: 'destructive',
      })
    },
  })

  // Register mutation
  const registerMutation = useMutation({
    mutationFn: authApi.register,
    onSuccess: (data) => {
      storeTokens(data.accessToken, data.refreshToken)
      setUser(data.user)
      queryClient.setQueryData(['auth', 'me'], data)
      toast({
        title: 'Account created!',
        description: 'Your account has been created successfully.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Registration failed',
        description: error.response?.data?.message || 'Failed to create account',
        variant: 'destructive',
      })
    },
  })

  // Refresh token mutation
  const refreshMutation = useMutation({
    mutationFn: () => {
      const { refreshToken } = getStoredTokens()
      if (!refreshToken) throw new Error('No refresh token')
      return authApi.refreshToken(refreshToken)
    },
    onSuccess: (data) => {
      storeTokens(data.accessToken, data.refreshToken)
      setUser(data.user)
      queryClient.setQueryData(['auth', 'me'], data)
    },
    onError: () => {
      clearTokens()
      setUser(null)
      queryClient.clear()
    },
  })

  // Login function
  const login = async (credentials: LoginRequest) => {
    await loginMutation.mutateAsync(credentials)
  }

  // Register function
  const register = async (userData: RegisterRequest) => {
    await registerMutation.mutateAsync(userData)
  }

  // Logout function
  const logout = () => {
    clearTokens()
    setUser(null)
    queryClient.clear()
    toast({
      title: 'Logged out',
      description: 'You have been successfully logged out.',
    })
  }

  // Refresh token function
  const refreshToken = async () => {
    await refreshMutation.mutateAsync()
  }

  // Set up axios interceptor for token refresh
  useEffect(() => {
    const { accessToken } = getStoredTokens()
    if (accessToken) {
      // Set default authorization header
      authApi.setAuthToken(accessToken)
    }

    // Response interceptor for token refresh
    const interceptor = authApi.setupTokenRefresh(refreshToken, logout)

    return () => {
      // Clean up interceptor
      if (interceptor) {
        authApi.removeInterceptor(interceptor)
      }
    }
  }, [])

  const value: AuthContextType = {
    user,
    isLoading: isLoading || loginMutation.isPending || registerMutation.isPending,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    refreshToken,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
