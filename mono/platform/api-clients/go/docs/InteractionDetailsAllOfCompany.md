# InteractionDetailsAllOfCompany

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Id** | Pointer to **string** |  | [optional] 
**Name** | Pointer to **string** |  | [optional] 
**Website** | Pointer to **string** |  | [optional] 
**Phone** | Pointer to **string** |  | [optional] 

## Methods

### NewInteractionDetailsAllOfCompany

`func NewInteractionDetailsAllOfCompany() *InteractionDetailsAllOfCompany`

NewInteractionDetailsAllOfCompany instantiates a new InteractionDetailsAllOfCompany object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewInteractionDetailsAllOfCompanyWithDefaults

`func NewInteractionDetailsAllOfCompanyWithDefaults() *InteractionDetailsAllOfCompany`

NewInteractionDetailsAllOfCompanyWithDefaults instantiates a new InteractionDetailsAllOfCompany object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetId

`func (o *InteractionDetailsAllOfCompany) GetId() string`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *InteractionDetailsAllOfCompany) GetIdOk() (*string, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *InteractionDetailsAllOfCompany) SetId(v string)`

SetId sets Id field to given value.

### HasId

`func (o *InteractionDetailsAllOfCompany) HasId() bool`

HasId returns a boolean if a field has been set.

### GetName

`func (o *InteractionDetailsAllOfCompany) GetName() string`

GetName returns the Name field if non-nil, zero value otherwise.

### GetNameOk

`func (o *InteractionDetailsAllOfCompany) GetNameOk() (*string, bool)`

GetNameOk returns a tuple with the Name field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetName

`func (o *InteractionDetailsAllOfCompany) SetName(v string)`

SetName sets Name field to given value.

### HasName

`func (o *InteractionDetailsAllOfCompany) HasName() bool`

HasName returns a boolean if a field has been set.

### GetWebsite

`func (o *InteractionDetailsAllOfCompany) GetWebsite() string`

GetWebsite returns the Website field if non-nil, zero value otherwise.

### GetWebsiteOk

`func (o *InteractionDetailsAllOfCompany) GetWebsiteOk() (*string, bool)`

GetWebsiteOk returns a tuple with the Website field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetWebsite

`func (o *InteractionDetailsAllOfCompany) SetWebsite(v string)`

SetWebsite sets Website field to given value.

### HasWebsite

`func (o *InteractionDetailsAllOfCompany) HasWebsite() bool`

HasWebsite returns a boolean if a field has been set.

### GetPhone

`func (o *InteractionDetailsAllOfCompany) GetPhone() string`

GetPhone returns the Phone field if non-nil, zero value otherwise.

### GetPhoneOk

`func (o *InteractionDetailsAllOfCompany) GetPhoneOk() (*string, bool)`

GetPhoneOk returns a tuple with the Phone field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPhone

`func (o *InteractionDetailsAllOfCompany) SetPhone(v string)`

SetPhone sets Phone field to given value.

### HasPhone

`func (o *InteractionDetailsAllOfCompany) HasPhone() bool`

HasPhone returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


