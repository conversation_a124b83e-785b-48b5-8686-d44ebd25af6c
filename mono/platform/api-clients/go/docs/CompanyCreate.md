# CompanyCreate

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Name** | **string** |  | 
**Website** | Pointer to **string** |  | [optional] 
**Phone** | Pointer to **string** |  | [optional] 
**Address** | Pointer to **string** |  | [optional] 
**Notes** | Pointer to **string** |  | [optional] 
**CompanyStatusId** | Pointer to **string** |  | [optional] 

## Methods

### NewCompanyCreate

`func NewCompanyCreate(name string, ) *CompanyCreate`

NewCompanyCreate instantiates a new CompanyCreate object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewCompanyCreateWithDefaults

`func NewCompanyCreateWithDefaults() *CompanyCreate`

NewCompanyCreateWithDefaults instantiates a new CompanyCreate object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetName

`func (o *CompanyCreate) GetName() string`

GetName returns the Name field if non-nil, zero value otherwise.

### GetNameOk

`func (o *CompanyCreate) GetNameOk() (*string, bool)`

GetNameOk returns a tuple with the Name field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetName

`func (o *CompanyCreate) SetName(v string)`

SetName sets Name field to given value.


### GetWebsite

`func (o *CompanyCreate) GetWebsite() string`

GetWebsite returns the Website field if non-nil, zero value otherwise.

### GetWebsiteOk

`func (o *CompanyCreate) GetWebsiteOk() (*string, bool)`

GetWebsiteOk returns a tuple with the Website field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetWebsite

`func (o *CompanyCreate) SetWebsite(v string)`

SetWebsite sets Website field to given value.

### HasWebsite

`func (o *CompanyCreate) HasWebsite() bool`

HasWebsite returns a boolean if a field has been set.

### GetPhone

`func (o *CompanyCreate) GetPhone() string`

GetPhone returns the Phone field if non-nil, zero value otherwise.

### GetPhoneOk

`func (o *CompanyCreate) GetPhoneOk() (*string, bool)`

GetPhoneOk returns a tuple with the Phone field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPhone

`func (o *CompanyCreate) SetPhone(v string)`

SetPhone sets Phone field to given value.

### HasPhone

`func (o *CompanyCreate) HasPhone() bool`

HasPhone returns a boolean if a field has been set.

### GetAddress

`func (o *CompanyCreate) GetAddress() string`

GetAddress returns the Address field if non-nil, zero value otherwise.

### GetAddressOk

`func (o *CompanyCreate) GetAddressOk() (*string, bool)`

GetAddressOk returns a tuple with the Address field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetAddress

`func (o *CompanyCreate) SetAddress(v string)`

SetAddress sets Address field to given value.

### HasAddress

`func (o *CompanyCreate) HasAddress() bool`

HasAddress returns a boolean if a field has been set.

### GetNotes

`func (o *CompanyCreate) GetNotes() string`

GetNotes returns the Notes field if non-nil, zero value otherwise.

### GetNotesOk

`func (o *CompanyCreate) GetNotesOk() (*string, bool)`

GetNotesOk returns a tuple with the Notes field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetNotes

`func (o *CompanyCreate) SetNotes(v string)`

SetNotes sets Notes field to given value.

### HasNotes

`func (o *CompanyCreate) HasNotes() bool`

HasNotes returns a boolean if a field has been set.

### GetCompanyStatusId

`func (o *CompanyCreate) GetCompanyStatusId() string`

GetCompanyStatusId returns the CompanyStatusId field if non-nil, zero value otherwise.

### GetCompanyStatusIdOk

`func (o *CompanyCreate) GetCompanyStatusIdOk() (*string, bool)`

GetCompanyStatusIdOk returns a tuple with the CompanyStatusId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCompanyStatusId

`func (o *CompanyCreate) SetCompanyStatusId(v string)`

SetCompanyStatusId sets CompanyStatusId field to given value.

### HasCompanyStatusId

`func (o *CompanyCreate) HasCompanyStatusId() bool`

HasCompanyStatusId returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


