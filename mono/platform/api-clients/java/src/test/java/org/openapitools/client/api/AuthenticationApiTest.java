/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.api;

import org.openapitools.client.ApiException;
import org.openapitools.client.model.AuthResponse;
import org.openapitools.client.model.OAuthRedirectResponse;
import org.openapitools.client.model.OAuthSignInRequest;
import org.openapitools.client.model.Session;
import org.openapitools.client.model.SignInRequest;
import org.openapitools.client.model.User;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for AuthenticationApi
 */
@Disabled
public class AuthenticationApiTest {

    private final AuthenticationApi api = new AuthenticationApi();

    /**
     * Get current session
     *
     * Retrieve the current user session
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void authSessionGetTest() throws ApiException {
        Session response = api.authSessionGet();
        // TODO: test validations
    }

    /**
     * Sign in with OAuth
     *
     * Authenticate user with OAuth provider (Google)
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void authSigninOauthPostTest() throws ApiException {
        OAuthSignInRequest oauthSignInRequest = null;
        OAuthRedirectResponse response = api.authSigninOauthPost(oauthSignInRequest);
        // TODO: test validations
    }

    /**
     * Sign in with email/password
     *
     * Authenticate user with email and password
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void authSigninPostTest() throws ApiException {
        SignInRequest signInRequest = null;
        AuthResponse response = api.authSigninPost(signInRequest);
        // TODO: test validations
    }

    /**
     * Sign out
     *
     * End the current user session
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void authSignoutPostTest() throws ApiException {
        api.authSignoutPost();
        // TODO: test validations
    }

    /**
     * Get current user
     *
     * Retrieve current authenticated user information
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void authUserGetTest() throws ApiException {
        User response = api.authUserGet();
        // TODO: test validations
    }

}
