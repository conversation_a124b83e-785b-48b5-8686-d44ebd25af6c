# FilterTagCreate

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Name** | **string** |  | 
**Color** | Pointer to **string** |  | [optional] [default to "#3b82f6"]

## Methods

### NewFilterTagCreate

`func NewFilterTagCreate(name string, ) *FilterTagCreate`

NewFilterTagCreate instantiates a new FilterTagCreate object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewFilterTagCreateWithDefaults

`func NewFilterTagCreateWithDefaults() *FilterTagCreate`

NewFilterTagCreateWithDefaults instantiates a new FilterTagCreate object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetName

`func (o *FilterTagCreate) GetName() string`

GetName returns the Name field if non-nil, zero value otherwise.

### GetNameOk

`func (o *FilterTagCreate) GetNameOk() (*string, bool)`

GetNameOk returns a tuple with the Name field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetName

`func (o *FilterTagCreate) SetName(v string)`

SetName sets Name field to given value.


### GetColor

`func (o *FilterTagCreate) GetColor() string`

GetColor returns the Color field if non-nil, zero value otherwise.

### GetColorOk

`func (o *FilterTagCreate) GetColorOk() (*string, bool)`

GetColorOk returns a tuple with the Color field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetColor

`func (o *FilterTagCreate) SetColor(v string)`

SetColor sets Color field to given value.

### HasColor

`func (o *FilterTagCreate) HasColor() bool`

HasColor returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


