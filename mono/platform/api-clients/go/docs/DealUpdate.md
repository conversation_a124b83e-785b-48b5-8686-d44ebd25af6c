# DealUpdate

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Title** | Pointer to **string** |  | [optional] 
**Description** | Pointer to **string** |  | [optional] 
**EstimatedValue** | Pointer to **float64** |  | [optional] 
**CompanyId** | Pointer to **string** |  | [optional] 
**DealStageId** | Pointer to **string** |  | [optional] 
**ExpectedCloseDate** | Pointer to **string** |  | [optional] 

## Methods

### NewDealUpdate

`func NewDealUpdate() *DealUpdate`

NewDealUpdate instantiates a new DealUpdate object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDealUpdateWithDefaults

`func NewDealUpdateWithDefaults() *DealUpdate`

NewDealUpdateWithDefaults instantiates a new DealUpdate object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetTitle

`func (o *DealUpdate) GetTitle() string`

GetTitle returns the Title field if non-nil, zero value otherwise.

### GetTitleOk

`func (o *DealUpdate) GetTitleOk() (*string, bool)`

GetTitleOk returns a tuple with the Title field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTitle

`func (o *DealUpdate) SetTitle(v string)`

SetTitle sets Title field to given value.

### HasTitle

`func (o *DealUpdate) HasTitle() bool`

HasTitle returns a boolean if a field has been set.

### GetDescription

`func (o *DealUpdate) GetDescription() string`

GetDescription returns the Description field if non-nil, zero value otherwise.

### GetDescriptionOk

`func (o *DealUpdate) GetDescriptionOk() (*string, bool)`

GetDescriptionOk returns a tuple with the Description field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDescription

`func (o *DealUpdate) SetDescription(v string)`

SetDescription sets Description field to given value.

### HasDescription

`func (o *DealUpdate) HasDescription() bool`

HasDescription returns a boolean if a field has been set.

### GetEstimatedValue

`func (o *DealUpdate) GetEstimatedValue() float64`

GetEstimatedValue returns the EstimatedValue field if non-nil, zero value otherwise.

### GetEstimatedValueOk

`func (o *DealUpdate) GetEstimatedValueOk() (*float64, bool)`

GetEstimatedValueOk returns a tuple with the EstimatedValue field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetEstimatedValue

`func (o *DealUpdate) SetEstimatedValue(v float64)`

SetEstimatedValue sets EstimatedValue field to given value.

### HasEstimatedValue

`func (o *DealUpdate) HasEstimatedValue() bool`

HasEstimatedValue returns a boolean if a field has been set.

### GetCompanyId

`func (o *DealUpdate) GetCompanyId() string`

GetCompanyId returns the CompanyId field if non-nil, zero value otherwise.

### GetCompanyIdOk

`func (o *DealUpdate) GetCompanyIdOk() (*string, bool)`

GetCompanyIdOk returns a tuple with the CompanyId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCompanyId

`func (o *DealUpdate) SetCompanyId(v string)`

SetCompanyId sets CompanyId field to given value.

### HasCompanyId

`func (o *DealUpdate) HasCompanyId() bool`

HasCompanyId returns a boolean if a field has been set.

### GetDealStageId

`func (o *DealUpdate) GetDealStageId() string`

GetDealStageId returns the DealStageId field if non-nil, zero value otherwise.

### GetDealStageIdOk

`func (o *DealUpdate) GetDealStageIdOk() (*string, bool)`

GetDealStageIdOk returns a tuple with the DealStageId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDealStageId

`func (o *DealUpdate) SetDealStageId(v string)`

SetDealStageId sets DealStageId field to given value.

### HasDealStageId

`func (o *DealUpdate) HasDealStageId() bool`

HasDealStageId returns a boolean if a field has been set.

### GetExpectedCloseDate

`func (o *DealUpdate) GetExpectedCloseDate() string`

GetExpectedCloseDate returns the ExpectedCloseDate field if non-nil, zero value otherwise.

### GetExpectedCloseDateOk

`func (o *DealUpdate) GetExpectedCloseDateOk() (*string, bool)`

GetExpectedCloseDateOk returns a tuple with the ExpectedCloseDate field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetExpectedCloseDate

`func (o *DealUpdate) SetExpectedCloseDate(v string)`

SetExpectedCloseDate sets ExpectedCloseDate field to given value.

### HasExpectedCloseDate

`func (o *DealUpdate) HasExpectedCloseDate() bool`

HasExpectedCloseDate returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


