# \InteractionsAPI

All URIs are relative to *https://api.crm.example.com/v1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**InteractionsGet**](InteractionsAPI.md#InteractionsGet) | **Get** /interactions | List interactions
[**InteractionsIdDelete**](InteractionsAPI.md#InteractionsIdDelete) | **Delete** /interactions/{id} | Delete interaction
[**InteractionsIdGet**](InteractionsAPI.md#InteractionsIdGet) | **Get** /interactions/{id} | Get interaction
[**InteractionsIdPut**](InteractionsAPI.md#InteractionsIdPut) | **Put** /interactions/{id} | Update interaction
[**InteractionsPost**](InteractionsAPI.md#InteractionsPost) | **Post** /interactions | Create interaction



## InteractionsGet

> InteractionsGet200Response InteractionsGet(ctx).CompanyId(companyId).ContactId(contactId).InteractionType(interactionType).FromDate(fromDate).ToDate(toDate).Limit(limit).Offset(offset).Execute()

List interactions



### Example

```go
package main

import (
	"context"
	"fmt"
	"os"
    "time"
	openapiclient "github.com/GIT_USER_ID/GIT_REPO_ID"
)

func main() {
	companyId := "38400000-8cf0-11bd-b23e-10b96e4ef00d" // string | Filter by company ID (optional)
	contactId := "38400000-8cf0-11bd-b23e-10b96e4ef00d" // string | Filter by contact ID (optional)
	interactionType := "interactionType_example" // string | Filter by interaction type (optional)
	fromDate := time.Now() // time.Time | Filter interactions from this date (optional)
	toDate := time.Now() // time.Time | Filter interactions to this date (optional)
	limit := int32(56) // int32 | Number of interactions to return (optional) (default to 20)
	offset := int32(56) // int32 | Number of interactions to skip (optional) (default to 0)

	configuration := openapiclient.NewConfiguration()
	apiClient := openapiclient.NewAPIClient(configuration)
	resp, r, err := apiClient.InteractionsAPI.InteractionsGet(context.Background()).CompanyId(companyId).ContactId(contactId).InteractionType(interactionType).FromDate(fromDate).ToDate(toDate).Limit(limit).Offset(offset).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `InteractionsAPI.InteractionsGet``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `InteractionsGet`: InteractionsGet200Response
	fmt.Fprintf(os.Stdout, "Response from `InteractionsAPI.InteractionsGet`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiInteractionsGetRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **companyId** | **string** | Filter by company ID | 
 **contactId** | **string** | Filter by contact ID | 
 **interactionType** | **string** | Filter by interaction type | 
 **fromDate** | **time.Time** | Filter interactions from this date | 
 **toDate** | **time.Time** | Filter interactions to this date | 
 **limit** | **int32** | Number of interactions to return | [default to 20]
 **offset** | **int32** | Number of interactions to skip | [default to 0]

### Return type

[**InteractionsGet200Response**](InteractionsGet200Response.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## InteractionsIdDelete

> InteractionsIdDelete(ctx, id).Execute()

Delete interaction



### Example

```go
package main

import (
	"context"
	"fmt"
	"os"
	openapiclient "github.com/GIT_USER_ID/GIT_REPO_ID"
)

func main() {
	id := "38400000-8cf0-11bd-b23e-10b96e4ef00d" // string | 

	configuration := openapiclient.NewConfiguration()
	apiClient := openapiclient.NewAPIClient(configuration)
	r, err := apiClient.InteractionsAPI.InteractionsIdDelete(context.Background(), id).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `InteractionsAPI.InteractionsIdDelete``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
}
```

### Path Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
**ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
**id** | **string** |  | 

### Other Parameters

Other parameters are passed through a pointer to a apiInteractionsIdDeleteRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


### Return type

 (empty response body)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## InteractionsIdGet

> InteractionDetails InteractionsIdGet(ctx, id).Execute()

Get interaction



### Example

```go
package main

import (
	"context"
	"fmt"
	"os"
	openapiclient "github.com/GIT_USER_ID/GIT_REPO_ID"
)

func main() {
	id := "38400000-8cf0-11bd-b23e-10b96e4ef00d" // string | 

	configuration := openapiclient.NewConfiguration()
	apiClient := openapiclient.NewAPIClient(configuration)
	resp, r, err := apiClient.InteractionsAPI.InteractionsIdGet(context.Background(), id).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `InteractionsAPI.InteractionsIdGet``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `InteractionsIdGet`: InteractionDetails
	fmt.Fprintf(os.Stdout, "Response from `InteractionsAPI.InteractionsIdGet`: %v\n", resp)
}
```

### Path Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
**ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
**id** | **string** |  | 

### Other Parameters

Other parameters are passed through a pointer to a apiInteractionsIdGetRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


### Return type

[**InteractionDetails**](InteractionDetails.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## InteractionsIdPut

> Interaction InteractionsIdPut(ctx, id).InteractionUpdate(interactionUpdate).Execute()

Update interaction



### Example

```go
package main

import (
	"context"
	"fmt"
	"os"
	openapiclient "github.com/GIT_USER_ID/GIT_REPO_ID"
)

func main() {
	id := "38400000-8cf0-11bd-b23e-10b96e4ef00d" // string | 
	interactionUpdate := *openapiclient.NewInteractionUpdate() // InteractionUpdate | 

	configuration := openapiclient.NewConfiguration()
	apiClient := openapiclient.NewAPIClient(configuration)
	resp, r, err := apiClient.InteractionsAPI.InteractionsIdPut(context.Background(), id).InteractionUpdate(interactionUpdate).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `InteractionsAPI.InteractionsIdPut``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `InteractionsIdPut`: Interaction
	fmt.Fprintf(os.Stdout, "Response from `InteractionsAPI.InteractionsIdPut`: %v\n", resp)
}
```

### Path Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
**ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
**id** | **string** |  | 

### Other Parameters

Other parameters are passed through a pointer to a apiInteractionsIdPutRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------

 **interactionUpdate** | [**InteractionUpdate**](InteractionUpdate.md) |  | 

### Return type

[**Interaction**](Interaction.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## InteractionsPost

> Interaction InteractionsPost(ctx).InteractionCreate(interactionCreate).Execute()

Create interaction



### Example

```go
package main

import (
	"context"
	"fmt"
	"os"
    "time"
	openapiclient "github.com/GIT_USER_ID/GIT_REPO_ID"
)

func main() {
	interactionCreate := *openapiclient.NewInteractionCreate("InteractionType_example", "Notes_example", time.Now()) // InteractionCreate | 

	configuration := openapiclient.NewConfiguration()
	apiClient := openapiclient.NewAPIClient(configuration)
	resp, r, err := apiClient.InteractionsAPI.InteractionsPost(context.Background()).InteractionCreate(interactionCreate).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `InteractionsAPI.InteractionsPost``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `InteractionsPost`: Interaction
	fmt.Fprintf(os.Stdout, "Response from `InteractionsAPI.InteractionsPost`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiInteractionsPostRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **interactionCreate** | [**InteractionCreate**](InteractionCreate.md) |  | 

### Return type

[**Interaction**](Interaction.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)

