/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"time"
)

// checks if the CompanyStatus type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &CompanyStatus{}

// CompanyStatus struct for CompanyStatus
type CompanyStatus struct {
	Id *string `json:"id,omitempty"`
	Name *string `json:"name,omitempty"`
	PipelineOrder *int32 `json:"pipeline_order,omitempty"`
	CreatedAt *time.Time `json:"created_at,omitempty"`
}

// NewCompanyStatus instantiates a new CompanyStatus object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewCompanyStatus() *CompanyStatus {
	this := CompanyStatus{}
	return &this
}

// NewCompanyStatusWithDefaults instantiates a new CompanyStatus object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewCompanyStatusWithDefaults() *CompanyStatus {
	this := CompanyStatus{}
	return &this
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *CompanyStatus) GetId() string {
	if o == nil || IsNil(o.Id) {
		var ret string
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyStatus) GetIdOk() (*string, bool) {
	if o == nil || IsNil(o.Id) {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *CompanyStatus) HasId() bool {
	if o != nil && !IsNil(o.Id) {
		return true
	}

	return false
}

// SetId gets a reference to the given string and assigns it to the Id field.
func (o *CompanyStatus) SetId(v string) {
	o.Id = &v
}

// GetName returns the Name field value if set, zero value otherwise.
func (o *CompanyStatus) GetName() string {
	if o == nil || IsNil(o.Name) {
		var ret string
		return ret
	}
	return *o.Name
}

// GetNameOk returns a tuple with the Name field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyStatus) GetNameOk() (*string, bool) {
	if o == nil || IsNil(o.Name) {
		return nil, false
	}
	return o.Name, true
}

// HasName returns a boolean if a field has been set.
func (o *CompanyStatus) HasName() bool {
	if o != nil && !IsNil(o.Name) {
		return true
	}

	return false
}

// SetName gets a reference to the given string and assigns it to the Name field.
func (o *CompanyStatus) SetName(v string) {
	o.Name = &v
}

// GetPipelineOrder returns the PipelineOrder field value if set, zero value otherwise.
func (o *CompanyStatus) GetPipelineOrder() int32 {
	if o == nil || IsNil(o.PipelineOrder) {
		var ret int32
		return ret
	}
	return *o.PipelineOrder
}

// GetPipelineOrderOk returns a tuple with the PipelineOrder field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyStatus) GetPipelineOrderOk() (*int32, bool) {
	if o == nil || IsNil(o.PipelineOrder) {
		return nil, false
	}
	return o.PipelineOrder, true
}

// HasPipelineOrder returns a boolean if a field has been set.
func (o *CompanyStatus) HasPipelineOrder() bool {
	if o != nil && !IsNil(o.PipelineOrder) {
		return true
	}

	return false
}

// SetPipelineOrder gets a reference to the given int32 and assigns it to the PipelineOrder field.
func (o *CompanyStatus) SetPipelineOrder(v int32) {
	o.PipelineOrder = &v
}

// GetCreatedAt returns the CreatedAt field value if set, zero value otherwise.
func (o *CompanyStatus) GetCreatedAt() time.Time {
	if o == nil || IsNil(o.CreatedAt) {
		var ret time.Time
		return ret
	}
	return *o.CreatedAt
}

// GetCreatedAtOk returns a tuple with the CreatedAt field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyStatus) GetCreatedAtOk() (*time.Time, bool) {
	if o == nil || IsNil(o.CreatedAt) {
		return nil, false
	}
	return o.CreatedAt, true
}

// HasCreatedAt returns a boolean if a field has been set.
func (o *CompanyStatus) HasCreatedAt() bool {
	if o != nil && !IsNil(o.CreatedAt) {
		return true
	}

	return false
}

// SetCreatedAt gets a reference to the given time.Time and assigns it to the CreatedAt field.
func (o *CompanyStatus) SetCreatedAt(v time.Time) {
	o.CreatedAt = &v
}

func (o CompanyStatus) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o CompanyStatus) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Id) {
		toSerialize["id"] = o.Id
	}
	if !IsNil(o.Name) {
		toSerialize["name"] = o.Name
	}
	if !IsNil(o.PipelineOrder) {
		toSerialize["pipeline_order"] = o.PipelineOrder
	}
	if !IsNil(o.CreatedAt) {
		toSerialize["created_at"] = o.CreatedAt
	}
	return toSerialize, nil
}

type NullableCompanyStatus struct {
	value *CompanyStatus
	isSet bool
}

func (v NullableCompanyStatus) Get() *CompanyStatus {
	return v.value
}

func (v *NullableCompanyStatus) Set(val *CompanyStatus) {
	v.value = val
	v.isSet = true
}

func (v NullableCompanyStatus) IsSet() bool {
	return v.isSet
}

func (v *NullableCompanyStatus) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableCompanyStatus(val *CompanyStatus) *NullableCompanyStatus {
	return &NullableCompanyStatus{value: val, isSet: true}
}

func (v NullableCompanyStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableCompanyStatus) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


