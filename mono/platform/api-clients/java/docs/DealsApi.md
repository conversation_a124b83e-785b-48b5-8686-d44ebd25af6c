# DealsApi

All URIs are relative to *https://api.crm.example.com/v1*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**dealStagesGet**](DealsApi.md#dealStagesGet) | **GET** /deal-stages | List deal stages |
| [**dealsGet**](DealsApi.md#dealsGet) | **GET** /deals | List deals |
| [**dealsIdDelete**](DealsApi.md#dealsIdDelete) | **DELETE** /deals/{id} | Delete deal |
| [**dealsIdGet**](DealsApi.md#dealsIdGet) | **GET** /deals/{id} | Get deal |
| [**dealsIdPut**](DealsApi.md#dealsIdPut) | **PUT** /deals/{id} | Update deal |
| [**dealsPost**](DealsApi.md#dealsPost) | **POST** /deals | Create deal |


<a id="dealStagesGet"></a>
# **dealStagesGet**
> List&lt;DealStage&gt; dealStagesGet()

List deal stages

Retrieve all deal stages ordered by pipeline order

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.DealsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    DealsApi apiInstance = new DealsApi(defaultClient);
    try {
      List<DealStage> result = apiInstance.dealStagesGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling DealsApi#dealStagesGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;DealStage&gt;**](DealStage.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | List of deal stages |  -  |

<a id="dealsGet"></a>
# **dealsGet**
> List&lt;DealWithDetails&gt; dealsGet(stageId, companyId)

List deals

Retrieve a list of all deals with company and stage information

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.DealsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    DealsApi apiInstance = new DealsApi(defaultClient);
    UUID stageId = UUID.randomUUID(); // UUID | Filter by deal stage ID
    UUID companyId = UUID.randomUUID(); // UUID | Filter by company ID
    try {
      List<DealWithDetails> result = apiInstance.dealsGet(stageId, companyId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling DealsApi#dealsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **stageId** | **UUID**| Filter by deal stage ID | [optional] |
| **companyId** | **UUID**| Filter by company ID | [optional] |

### Return type

[**List&lt;DealWithDetails&gt;**](DealWithDetails.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | List of deals |  -  |

<a id="dealsIdDelete"></a>
# **dealsIdDelete**
> dealsIdDelete(id)

Delete deal

Delete a deal

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.DealsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    DealsApi apiInstance = new DealsApi(defaultClient);
    UUID id = UUID.randomUUID(); // UUID | 
    try {
      apiInstance.dealsIdDelete(id);
    } catch (ApiException e) {
      System.err.println("Exception when calling DealsApi#dealsIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **id** | **UUID**|  | |

### Return type

null (empty response body)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **204** | Deal deleted successfully |  -  |
| **404** | Deal not found |  -  |

<a id="dealsIdGet"></a>
# **dealsIdGet**
> DealDetails dealsIdGet(id)

Get deal

Retrieve a specific deal by ID

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.DealsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    DealsApi apiInstance = new DealsApi(defaultClient);
    UUID id = UUID.randomUUID(); // UUID | 
    try {
      DealDetails result = apiInstance.dealsIdGet(id);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling DealsApi#dealsIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **id** | **UUID**|  | |

### Return type

[**DealDetails**](DealDetails.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Deal details |  -  |
| **404** | Deal not found |  -  |

<a id="dealsIdPut"></a>
# **dealsIdPut**
> Deal dealsIdPut(id, dealUpdate)

Update deal

Update an existing deal

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.DealsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    DealsApi apiInstance = new DealsApi(defaultClient);
    UUID id = UUID.randomUUID(); // UUID | 
    DealUpdate dealUpdate = new DealUpdate(); // DealUpdate | 
    try {
      Deal result = apiInstance.dealsIdPut(id, dealUpdate);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling DealsApi#dealsIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **id** | **UUID**|  | |
| **dealUpdate** | [**DealUpdate**](DealUpdate.md)|  | |

### Return type

[**Deal**](Deal.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Deal updated successfully |  -  |
| **404** | Deal not found |  -  |

<a id="dealsPost"></a>
# **dealsPost**
> Deal dealsPost(dealCreate)

Create deal

Create a new deal

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.DealsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    DealsApi apiInstance = new DealsApi(defaultClient);
    DealCreate dealCreate = new DealCreate(); // DealCreate | 
    try {
      Deal result = apiInstance.dealsPost(dealCreate);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling DealsApi#dealsPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **dealCreate** | [**DealCreate**](DealCreate.md)|  | |

### Return type

[**Deal**](Deal.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **201** | Deal created successfully |  -  |
| **400** | Invalid input |  -  |

