import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireRole, canAccessCompany } from '../middleware/auth';

const router = Router();
const prisma = new PrismaClient();

// Apply company access middleware to all routes
router.use(canAccessCompany);

// Validation schemas
const createContactSchema = z.object({
  company_id: z.string().uuid('Invalid company ID'),
  name: z.string().min(1, 'Contact name is required'),
  email: z.string().email('Invalid email format'),
  phone: z.string().optional(),
  role: z.enum(['primary', 'technical', 'business', 'stakeholder']),
  title: z.string().optional(),
  department: z.string().optional(),
  is_primary: z.boolean().default(false),
  preferred_contact_method: z.enum(['email', 'phone', 'slack']).optional(),
  timezone: z.string().optional(),
});

/**
 * @route GET /api/contacts
 * @desc Get all contacts
 * @access Private (No customers)
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { page = '1', limit = '10', company_id, role, search } = req.query;
  const skip = (parseInt(page as string) - 1) * parseInt(limit as string);

  let whereClause: any = {
    is_active: true,
  };

  // Apply filters
  if (company_id) {
    whereClause.company_id = company_id;
  }
  if (role) {
    whereClause.role = role;
  }
  if (search) {
    whereClause.OR = [
      { name: { contains: search as string, mode: 'insensitive' } },
      { email: { contains: search as string, mode: 'insensitive' } },
      { title: { contains: search as string, mode: 'insensitive' } },
    ];
  }

  const [contacts, total] = await Promise.all([
    prisma.contact.findMany({
      where: whereClause,
      include: {
        company: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
        customer_projects: {
          select: {
            id: true,
            name: true,
            status: true,
          },
          take: 5, // Latest 5 projects
        },
        _count: {
          select: {
            interactions: true,
          },
        },
      },
      skip,
      take: parseInt(limit as string),
      orderBy: [
        { is_primary: 'desc' },
        { name: 'asc' },
      ],
    }),
    prisma.contact.count({ where: whereClause }),
  ]);

  res.json({
    status: 'success',
    data: {
      contacts,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        pages: Math.ceil(total / parseInt(limit as string)),
      },
    },
  });
}));

/**
 * @route GET /api/contacts/:id
 * @desc Get contact by ID
 * @access Private (No customers)
 */
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const contact = await prisma.contact.findUnique({
    where: { id },
    include: {
      company: true,
      customer_projects: {
        include: {
          project_lead: {
            select: { id: true, name: true, email: true, avatar_url: true },
          },
        },
        orderBy: { updated_at: 'desc' },
      },
      interactions: {
        include: {
          created_by: {
            select: { id: true, name: true, email: true },
          },
          project: {
            select: { id: true, name: true },
          },
        },
        orderBy: { interaction_date: 'desc' },
        take: 20, // Latest 20 interactions
      },
    },
  });

  if (!contact) {
    return res.status(404).json({
      status: 'error',
      message: 'Contact not found',
    });
  }

  res.json({
    status: 'success',
    data: { contact },
  });
}));

/**
 * @route POST /api/contacts
 * @desc Create new contact
 * @access Private (Admin, Project Manager)
 */
router.post('/', requireRole('admin', 'project_manager'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createContactSchema.parse(req.body);

  // If this is set as primary, unset other primary contacts for the company
  if (validatedData.is_primary) {
    await prisma.contact.updateMany({
      where: {
        company_id: validatedData.company_id,
        is_primary: true,
      },
      data: {
        is_primary: false,
      },
    });
  }

  const contact = await prisma.contact.create({
    data: validatedData,
    include: {
      company: {
        select: {
          id: true,
          name: true,
          status: true,
        },
      },
    },
  });

  res.status(201).json({
    status: 'success',
    message: 'Contact created successfully',
    data: { contact },
  });
}));

/**
 * @route PUT /api/contacts/:id
 * @desc Update contact
 * @access Private (Admin, Project Manager)
 */
router.put('/:id', requireRole('admin', 'project_manager'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const updateData = createContactSchema.partial().parse(req.body);

  // If this is set as primary, unset other primary contacts for the company
  if (updateData.is_primary) {
    const existingContact = await prisma.contact.findUnique({
      where: { id },
      select: { company_id: true },
    });

    if (existingContact) {
      await prisma.contact.updateMany({
        where: {
          company_id: existingContact.company_id,
          is_primary: true,
          id: { not: id },
        },
        data: {
          is_primary: false,
        },
      });
    }
  }

  const contact = await prisma.contact.update({
    where: { id },
    data: updateData,
    include: {
      company: {
        select: {
          id: true,
          name: true,
          status: true,
        },
      },
    },
  });

  res.json({
    status: 'success',
    message: 'Contact updated successfully',
    data: { contact },
  });
}));

/**
 * @route DELETE /api/contacts/:id
 * @desc Delete contact (soft delete)
 * @access Private (Admin, Project Manager)
 */
router.delete('/:id', requireRole('admin', 'project_manager'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if contact is linked to active projects
  const activeProjects = await prisma.project.count({
    where: {
      customer_contact_id: id,
      status: { in: ['in_progress', 'not_started'] },
    },
  });

  if (activeProjects > 0) {
    return res.status(400).json({
      status: 'error',
      message: 'Cannot delete contact linked to active projects',
    });
  }

  // Soft delete by setting is_active to false
  await prisma.contact.update({
    where: { id },
    data: { is_active: false },
  });

  res.json({
    status: 'success',
    message: 'Contact deleted successfully',
  });
}));

export default router;
