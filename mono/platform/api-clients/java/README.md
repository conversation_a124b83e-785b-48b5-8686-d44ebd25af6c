# crm-api-client

CRM API
- API version: 1.0.0
  - Build date: 2025-07-14T08:13:23.820009+10:00[Australia/Brisbane]
  - Generator version: 7.9.0

Complete API specification for the CRM system


*Automatically generated by the [OpenAPI Generator](https://openapi-generator.tech)*


## Requirements

Building the API client library requires:
1. Java 1.8+
2. <PERSON><PERSON> (3.8.3+)/<PERSON>radle (7.2+)

## Installation

To install the API client library to your local Maven repository, simply execute:

```shell
mvn clean install
```

To deploy it to a remote Maven repository instead, configure the settings of the repository and execute:

```shell
mvn clean deploy
```

Refer to the [OSSRH Guide](http://central.sonatype.org/pages/ossrh-guide.html) for more information.

### Maven users

Add this dependency to your project's POM:

```xml
<dependency>
  <groupId>com.crm</groupId>
  <artifactId>crm-api-client</artifactId>
  <version>1.0.0</version>
  <scope>compile</scope>
</dependency>
```

### Gradle users

Add this dependency to your project's build file:

```groovy
  repositories {
    mavenCentral()     // Needed if the 'crm-api-client' jar has been published to maven central.
    mavenLocal()       // Needed if the 'crm-api-client' jar has been published to the local maven repo.
  }

  dependencies {
     implementation "com.crm:crm-api-client:1.0.0"
  }
```

### Others

At first generate the JAR by executing:

```shell
mvn clean package
```

Then manually install the following JARs:

* `target/crm-api-client-1.0.0.jar`
* `target/lib/*.jar`

## Getting Started

Please follow the [installation](#installation) instruction and execute the following Java code:

```java

// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.model.*;
import org.openapitools.client.api.ArliApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    ArliApi apiInstance = new ArliApi(defaultClient);
    List<String> filterTags = Arrays.asList(); // List<String> | Filter by tags
    Object metadataFilter = null; // Object | Filter by metadata properties
    Integer limit = 20; // Integer | 
    Integer offset = 0; // Integer | 
    try {
      ArliDocumentsGet200Response result = apiInstance.arliDocumentsGet(filterTags, metadataFilter, limit, offset);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ArliApi#arliDocumentsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```

## Documentation for API Endpoints

All URIs are relative to *https://api.crm.example.com/v1*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*ArliApi* | [**arliDocumentsGet**](docs/ArliApi.md#arliDocumentsGet) | **GET** /arli/documents | List documents
*ArliApi* | [**arliDocumentsIdDelete**](docs/ArliApi.md#arliDocumentsIdDelete) | **DELETE** /arli/documents/{id} | Delete document
*ArliApi* | [**arliDocumentsIdGet**](docs/ArliApi.md#arliDocumentsIdGet) | **GET** /arli/documents/{id} | Get document
*ArliApi* | [**arliDocumentsIdPut**](docs/ArliApi.md#arliDocumentsIdPut) | **PUT** /arli/documents/{id} | Update document
*ArliApi* | [**arliDocumentsPost**](docs/ArliApi.md#arliDocumentsPost) | **POST** /arli/documents | Create document
*ArliApi* | [**arliDocumentsVectorizePost**](docs/ArliApi.md#arliDocumentsVectorizePost) | **POST** /arli/documents/vectorize | Vectorize document
*ArliApi* | [**arliFilterTagsGet**](docs/ArliApi.md#arliFilterTagsGet) | **GET** /arli/filter-tags | List filter tags
*ArliApi* | [**arliFilterTagsPost**](docs/ArliApi.md#arliFilterTagsPost) | **POST** /arli/filter-tags | Create filter tag
*AuthenticationApi* | [**authSessionGet**](docs/AuthenticationApi.md#authSessionGet) | **GET** /auth/session | Get current session
*AuthenticationApi* | [**authSigninOauthPost**](docs/AuthenticationApi.md#authSigninOauthPost) | **POST** /auth/signin/oauth | Sign in with OAuth
*AuthenticationApi* | [**authSigninPost**](docs/AuthenticationApi.md#authSigninPost) | **POST** /auth/signin | Sign in with email/password
*AuthenticationApi* | [**authSignoutPost**](docs/AuthenticationApi.md#authSignoutPost) | **POST** /auth/signout | Sign out
*AuthenticationApi* | [**authUserGet**](docs/AuthenticationApi.md#authUserGet) | **GET** /auth/user | Get current user
*CompaniesApi* | [**companiesGet**](docs/CompaniesApi.md#companiesGet) | **GET** /companies | List companies
*CompaniesApi* | [**companiesIdDelete**](docs/CompaniesApi.md#companiesIdDelete) | **DELETE** /companies/{id} | Delete company
*CompaniesApi* | [**companiesIdGet**](docs/CompaniesApi.md#companiesIdGet) | **GET** /companies/{id} | Get company
*CompaniesApi* | [**companiesIdPut**](docs/CompaniesApi.md#companiesIdPut) | **PUT** /companies/{id} | Update company
*CompaniesApi* | [**companiesIdStatusPut**](docs/CompaniesApi.md#companiesIdStatusPut) | **PUT** /companies/{id}/status | Update company status
*CompaniesApi* | [**companiesPost**](docs/CompaniesApi.md#companiesPost) | **POST** /companies | Create company
*CompaniesApi* | [**companyStatusesGet**](docs/CompaniesApi.md#companyStatusesGet) | **GET** /company-statuses | List company statuses
*ContactsApi* | [**contactsGet**](docs/ContactsApi.md#contactsGet) | **GET** /contacts | List contacts
*ContactsApi* | [**contactsIdCompanyDelete**](docs/ContactsApi.md#contactsIdCompanyDelete) | **DELETE** /contacts/{id}/company | Unlink contact from company
*ContactsApi* | [**contactsIdCompanyPut**](docs/ContactsApi.md#contactsIdCompanyPut) | **PUT** /contacts/{id}/company | Link contact to company
*ContactsApi* | [**contactsIdDelete**](docs/ContactsApi.md#contactsIdDelete) | **DELETE** /contacts/{id} | Delete contact
*ContactsApi* | [**contactsIdGet**](docs/ContactsApi.md#contactsIdGet) | **GET** /contacts/{id} | Get contact
*ContactsApi* | [**contactsIdPut**](docs/ContactsApi.md#contactsIdPut) | **PUT** /contacts/{id} | Update contact
*ContactsApi* | [**contactsPost**](docs/ContactsApi.md#contactsPost) | **POST** /contacts | Create contact
*DealsApi* | [**dealStagesGet**](docs/DealsApi.md#dealStagesGet) | **GET** /deal-stages | List deal stages
*DealsApi* | [**dealsGet**](docs/DealsApi.md#dealsGet) | **GET** /deals | List deals
*DealsApi* | [**dealsIdDelete**](docs/DealsApi.md#dealsIdDelete) | **DELETE** /deals/{id} | Delete deal
*DealsApi* | [**dealsIdGet**](docs/DealsApi.md#dealsIdGet) | **GET** /deals/{id} | Get deal
*DealsApi* | [**dealsIdPut**](docs/DealsApi.md#dealsIdPut) | **PUT** /deals/{id} | Update deal
*DealsApi* | [**dealsPost**](docs/DealsApi.md#dealsPost) | **POST** /deals | Create deal
*InteractionsApi* | [**interactionsGet**](docs/InteractionsApi.md#interactionsGet) | **GET** /interactions | List interactions
*InteractionsApi* | [**interactionsIdDelete**](docs/InteractionsApi.md#interactionsIdDelete) | **DELETE** /interactions/{id} | Delete interaction
*InteractionsApi* | [**interactionsIdGet**](docs/InteractionsApi.md#interactionsIdGet) | **GET** /interactions/{id} | Get interaction
*InteractionsApi* | [**interactionsIdPut**](docs/InteractionsApi.md#interactionsIdPut) | **PUT** /interactions/{id} | Update interaction
*InteractionsApi* | [**interactionsPost**](docs/InteractionsApi.md#interactionsPost) | **POST** /interactions | Create interaction
*UserManagementApi* | [**usersProfileGet**](docs/UserManagementApi.md#usersProfileGet) | **GET** /users/profile | Get user profile
*UserManagementApi* | [**usersProfilePut**](docs/UserManagementApi.md#usersProfilePut) | **PUT** /users/profile | Update user profile


## Documentation for Models

 - [ArliDocumentsGet200Response](docs/ArliDocumentsGet200Response.md)
 - [ArliDocumentsVectorizePost200Response](docs/ArliDocumentsVectorizePost200Response.md)
 - [ArliDocumentsVectorizePostRequest](docs/ArliDocumentsVectorizePostRequest.md)
 - [AuthResponse](docs/AuthResponse.md)
 - [Company](docs/Company.md)
 - [CompanyBasicInfo](docs/CompanyBasicInfo.md)
 - [CompanyCreate](docs/CompanyCreate.md)
 - [CompanyDetails](docs/CompanyDetails.md)
 - [CompanyInfo](docs/CompanyInfo.md)
 - [CompanyStatus](docs/CompanyStatus.md)
 - [CompanyStatusUpdateRequest](docs/CompanyStatusUpdateRequest.md)
 - [CompanyUpdate](docs/CompanyUpdate.md)
 - [Contact](docs/Contact.md)
 - [ContactCompanyUpdateRequest](docs/ContactCompanyUpdateRequest.md)
 - [ContactCreate](docs/ContactCreate.md)
 - [ContactDetails](docs/ContactDetails.md)
 - [ContactUpdate](docs/ContactUpdate.md)
 - [ContactWithCompany](docs/ContactWithCompany.md)
 - [Deal](docs/Deal.md)
 - [DealCompanyInfo](docs/DealCompanyInfo.md)
 - [DealCreate](docs/DealCreate.md)
 - [DealDetails](docs/DealDetails.md)
 - [DealStage](docs/DealStage.md)
 - [DealStageInfo](docs/DealStageInfo.md)
 - [DealUpdate](docs/DealUpdate.md)
 - [DealWithDetails](docs/DealWithDetails.md)
 - [Document](docs/Document.md)
 - [DocumentCreate](docs/DocumentCreate.md)
 - [DocumentDetails](docs/DocumentDetails.md)
 - [DocumentUpdate](docs/DocumentUpdate.md)
 - [Error](docs/Error.md)
 - [FilterTag](docs/FilterTag.md)
 - [FilterTagCreate](docs/FilterTagCreate.md)
 - [Interaction](docs/Interaction.md)
 - [InteractionCreate](docs/InteractionCreate.md)
 - [InteractionDetails](docs/InteractionDetails.md)
 - [InteractionDetailsAllOfCompany](docs/InteractionDetailsAllOfCompany.md)
 - [InteractionDetailsAllOfContact](docs/InteractionDetailsAllOfContact.md)
 - [InteractionUpdate](docs/InteractionUpdate.md)
 - [InteractionWithDetails](docs/InteractionWithDetails.md)
 - [InteractionWithDetailsAllOfCompany](docs/InteractionWithDetailsAllOfCompany.md)
 - [InteractionWithDetailsAllOfContact](docs/InteractionWithDetailsAllOfContact.md)
 - [InteractionsGet200Response](docs/InteractionsGet200Response.md)
 - [OAuthRedirectResponse](docs/OAuthRedirectResponse.md)
 - [OAuthSignInRequest](docs/OAuthSignInRequest.md)
 - [Session](docs/Session.md)
 - [SignInRequest](docs/SignInRequest.md)
 - [User](docs/User.md)
 - [UserProfile](docs/UserProfile.md)
 - [UserProfileUpdate](docs/UserProfileUpdate.md)
 - [ValidationError](docs/ValidationError.md)
 - [ValidationErrorDetails](docs/ValidationErrorDetails.md)


<a id="documentation-for-authorization"></a>
## Documentation for Authorization


Authentication schemes defined for the API:
<a id="bearerAuth"></a>
### bearerAuth

- **Type**: HTTP Bearer Token authentication (JWT)


## Recommendation

It's recommended to create an instance of `ApiClient` per thread in a multithreaded environment to avoid any potential issues.

## Author

<EMAIL>

