# API Configuration
VITE_API_BASE_URL=http://localhost:8000/api
VITE_WS_URL=ws://localhost:8000

# Application Configuration
VITE_APP_NAME=Unified Project Hub
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Unified project management system

# Environment
VITE_NODE_ENV=development

# Feature Flags
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=false

# File Upload Configuration
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,txt,md

# External Services
VITE_GOOGLE_ANALYTICS_ID=
VITE_SENTRY_DSN=

# UI Configuration
VITE_DEFAULT_THEME=light
VITE_ENABLE_DARK_MODE=true
VITE_DEFAULT_LANGUAGE=en

# Development Configuration
VITE_MOCK_API=false
VITE_DEBUG_MODE=true

# Production Configuration (override in production)
# VITE_NODE_ENV=production
# VITE_ENABLE_DEVTOOLS=false
# VITE_DEBUG_MODE=false
# VITE_MOCK_API=false
