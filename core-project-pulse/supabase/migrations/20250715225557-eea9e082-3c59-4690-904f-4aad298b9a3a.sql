-- First, update any existing data to map old values to new ones
UPDATE projects 
SET impact_type = CASE 
  WHEN impact_type = 'Users' THEN 'Platform'
  WHEN impact_type = 'Tech' THEN 'Bug Fix'
  WHEN impact_type = 'Compliance' THEN 'R&D'
  ELSE impact_type
END;

-- Drop the old enum and create a new one
DROP TYPE IF EXISTS impact_type CASCADE;
CREATE TYPE impact_type AS ENUM ('Revenue', 'Platform', 'Bug Fix', 'R&D');

-- Re-add the column with the new enum type
ALTER TABLE projects 
ALTER COLUMN impact_type TYPE impact_type 
USING impact_type::text::impact_type;