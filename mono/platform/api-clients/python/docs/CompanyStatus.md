# CompanyStatus


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **str** |  | [optional] 
**name** | **str** |  | [optional] 
**pipeline_order** | **int** |  | [optional] 
**created_at** | **datetime** |  | [optional] 

## Example

```python
from crm_api_client.models.company_status import CompanyStatus

# TODO update the JSON string below
json = "{}"
# create an instance of CompanyStatus from a JSON string
company_status_instance = CompanyStatus.from_json(json)
# print the JSON string representation of the object
print(CompanyStatus.to_json())

# convert the object into a dict
company_status_dict = company_status_instance.to_dict()
# create an instance of CompanyStatus from a dict
company_status_from_dict = CompanyStatus.from_dict(company_status_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


