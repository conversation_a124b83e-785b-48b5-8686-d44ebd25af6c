/auth/session:
  get:
    tags: [Authentication]
    summary: Get current session
    description: Retrieve the current user session
    security: []
    responses:
      '200':
        description: Session information
        content:
          application/json:
            schema:
              $ref: './auth/schemas.yaml#/Session'
      '401':
        description: Not authenticated

/auth/signin:
  post:
    tags: [Authentication]
    summary: Sign in with email/password
    description: Authenticate user with email and password
    security: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            title: SignInRequest
            properties:
              email:
                type: string
                format: email
              password:
                type: string
                minLength: 6
            required:
              - email
              - password
    responses:
      '200':
        description: Authentication successful
        content:
          application/json:
            schema:
              $ref: './auth/schemas.yaml#/AuthResponse'
      '401':
        description: Invalid credentials

/auth/signin/oauth:
  post:
    tags: [Authentication]
    summary: Sign in with OAuth
    description: Authenticate user with OAuth provider (Google)
    security: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            title: OAuthSignInRequest
            properties:
              provider:
                type: string
                enum: [google]
              redirectTo:
                type: string
                format: uri
            required:
              - provider
    responses:
      '200':
        description: OAuth redirect URL
        content:
          application/json:
            schema:
              type: object
              title: OAuthRedirectResponse
              properties:
                url:
                  type: string
                  format: uri

/auth/signout:
  post:
    tags: [Authentication]
    summary: Sign out
    description: End the current user session
    responses:
      '200':
        description: Successfully signed out
      '401':
        description: Not authenticated

/auth/user:
  get:
    tags: [Authentication]
    summary: Get current user
    description: Retrieve current authenticated user information
    responses:
      '200':
        description: User information
        content:
          application/json:
            schema:
              $ref: './auth/schemas.yaml#/User'
      '401':
        description: Not authenticated