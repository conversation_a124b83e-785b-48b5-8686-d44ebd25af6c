{"version": 3, "sources": ["../../@tanstack/query-devtools/build/DevtoolsPanelComponent/JZI2RDCT.js"], "sourcesContent": ["import { createLocalStorage, THEME_PREFERENCE, QueryDevtoolsContext, PiPProvider, ThemeContext, ParentPanel, ContentView } from '../chunk/L7Z3HDK6.js';\nimport { getPreferredColorScheme, createMemo, createComponent } from '../chunk/V5T5VJKG.js';\n\n// src/DevtoolsPanelComponent.tsx\nvar DevtoolsPanelComponent = (props) => {\n  const [localStore, setLocalStore] = createLocalStorage({\n    prefix: \"TanstackQueryDevtools\"\n  });\n  const colorScheme = getPreferredColorScheme();\n  const theme = createMemo(() => {\n    const preference = localStore.theme_preference || THEME_PREFERENCE;\n    if (preference !== \"system\") return preference;\n    return colorScheme();\n  });\n  return createComponent(QueryDevtoolsContext.Provider, {\n    value: props,\n    get children() {\n      return createComponent(PiPProvider, {\n        disabled: true,\n        localStore,\n        setLocalStore,\n        get children() {\n          return createComponent(ThemeContext.Provider, {\n            value: theme,\n            get children() {\n              return createComponent(ParentPanel, {\n                get children() {\n                  return createComponent(ContentView, {\n                    localStore,\n                    setLocalStore,\n                    get onClose() {\n                      return props.onClose;\n                    },\n                    showPanelViewOnly: true\n                  });\n                }\n              });\n            }\n          });\n        }\n      });\n    }\n  });\n};\nvar DevtoolsPanelComponent_default = DevtoolsPanelComponent;\n\nexport { DevtoolsPanelComponent_default as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAIA,IAAI,yBAAyB,CAAC,UAAU;AACtC,QAAM,CAAC,YAAY,aAAa,IAAI,mBAAmB;AAAA,IACrD,QAAQ;AAAA,EACV,CAAC;AACD,QAAM,cAAc,wBAAwB;AAC5C,QAAM,QAAQ,WAAW,MAAM;AAC7B,UAAM,aAAa,WAAW,oBAAoB;AAClD,QAAI,eAAe;AAAU,aAAO;AACpC,WAAO,YAAY;AAAA,EACrB,CAAC;AACD,SAAO,gBAAgB,qBAAqB,UAAU;AAAA,IACpD,OAAO;AAAA,IACP,IAAI,WAAW;AACb,aAAO,gBAAgB,aAAa;AAAA,QAClC,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA,IAAI,WAAW;AACb,iBAAO,gBAAgB,aAAa,UAAU;AAAA,YAC5C,OAAO;AAAA,YACP,IAAI,WAAW;AACb,qBAAO,gBAAgB,aAAa;AAAA,gBAClC,IAAI,WAAW;AACb,yBAAO,gBAAgB,aAAa;AAAA,oBAClC;AAAA,oBACA;AAAA,oBACA,IAAI,UAAU;AACZ,6BAAO,MAAM;AAAA,oBACf;AAAA,oBACA,mBAAmB;AAAA,kBACrB,CAAC;AAAA,gBACH;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;AACA,IAAI,iCAAiC;", "names": []}