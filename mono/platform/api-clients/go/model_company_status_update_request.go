/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"bytes"
	"fmt"
)

// checks if the CompanyStatusUpdateRequest type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &CompanyStatusUpdateRequest{}

// CompanyStatusUpdateRequest struct for CompanyStatusUpdateRequest
type CompanyStatusUpdateRequest struct {
	CompanyStatusId string `json:"company_status_id"`
}

type _CompanyStatusUpdateRequest CompanyStatusUpdateRequest

// NewCompanyStatusUpdateRequest instantiates a new CompanyStatusUpdateRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewCompanyStatusUpdateRequest(companyStatusId string) *CompanyStatusUpdateRequest {
	this := CompanyStatusUpdateRequest{}
	this.CompanyStatusId = companyStatusId
	return &this
}

// NewCompanyStatusUpdateRequestWithDefaults instantiates a new CompanyStatusUpdateRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewCompanyStatusUpdateRequestWithDefaults() *CompanyStatusUpdateRequest {
	this := CompanyStatusUpdateRequest{}
	return &this
}

// GetCompanyStatusId returns the CompanyStatusId field value
func (o *CompanyStatusUpdateRequest) GetCompanyStatusId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.CompanyStatusId
}

// GetCompanyStatusIdOk returns a tuple with the CompanyStatusId field value
// and a boolean to check if the value has been set.
func (o *CompanyStatusUpdateRequest) GetCompanyStatusIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.CompanyStatusId, true
}

// SetCompanyStatusId sets field value
func (o *CompanyStatusUpdateRequest) SetCompanyStatusId(v string) {
	o.CompanyStatusId = v
}

func (o CompanyStatusUpdateRequest) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o CompanyStatusUpdateRequest) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["company_status_id"] = o.CompanyStatusId
	return toSerialize, nil
}

func (o *CompanyStatusUpdateRequest) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"company_status_id",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varCompanyStatusUpdateRequest := _CompanyStatusUpdateRequest{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varCompanyStatusUpdateRequest)

	if err != nil {
		return err
	}

	*o = CompanyStatusUpdateRequest(varCompanyStatusUpdateRequest)

	return err
}

type NullableCompanyStatusUpdateRequest struct {
	value *CompanyStatusUpdateRequest
	isSet bool
}

func (v NullableCompanyStatusUpdateRequest) Get() *CompanyStatusUpdateRequest {
	return v.value
}

func (v *NullableCompanyStatusUpdateRequest) Set(val *CompanyStatusUpdateRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableCompanyStatusUpdateRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableCompanyStatusUpdateRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableCompanyStatusUpdateRequest(val *CompanyStatusUpdateRequest) *NullableCompanyStatusUpdateRequest {
	return &NullableCompanyStatusUpdateRequest{value: val, isSet: true}
}

func (v NullableCompanyStatusUpdateRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableCompanyStatusUpdateRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


