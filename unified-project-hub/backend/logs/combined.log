{"level":"info","message":"🚀 Server running on http://localhost:8000","service":"Unified Project Hub","timestamp":"2025-07-17 16:27:29","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8000/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 16:27:29","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 16:27:29","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 16:28:39","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 16:28:39","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 16:28:39","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-17 18:14:17","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-17 18:14:24","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 18:14:32","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 18:14:32","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 18:14:32","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /","service":"Unified Project Hub","timestamp":"2025-07-17 18:15:35","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /favicon.ico","service":"Unified Project Hub","timestamp":"2025-07-17 18:15:35","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-17 18:17:40","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 18:18:35","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 18:18:35","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 18:18:35","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /health","service":"Unified Project Hub","timestamp":"2025-07-17 18:18:49","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 18:19:14","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 18:20:36","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 18:29:32","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 18:29:53","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 18:31:26","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 18:38:58","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 18:56:03","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 18:57:46","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 19:05:17","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 19:17:30","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:17:53","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:17:53","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:17:53","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:18:13","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:18:13","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:18:13","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:19:26","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:19:26","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:19:26","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:04","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:04","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:04","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:42","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:42","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:42","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:54","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:54","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:54","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:04","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:04","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:04","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:15","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:15","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:15","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:29","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:29","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:29","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:39","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:39","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:39","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:49","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:49","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:49","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:00","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:00","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:00","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:35","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:35","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:35","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:56","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:56","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:56","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:07","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:07","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:07","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:17","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:17","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:17","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:27","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:27","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:27","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:37","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:37","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:37","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:47","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:47","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:47","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:58","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:58","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:58","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:09","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:35","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:35","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:35","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:46","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:46","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:46","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:57","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:57","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:57","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:25:08","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:25:08","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:25:08","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:32:52","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:32:52","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:32:52","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 21:32:54","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 21:33:13","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 21:33:15","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 21:34:20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 21:34:26","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 21:37:02","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:07","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:07","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:07","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:30","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:30","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:30","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:39","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:39","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:39","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:39:45","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:39:45","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:39:45","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 21:40:04","userAgent":"curl/8.7.1","version":"1.0.0"}
{"error":"Invalid token","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"Error: Invalid token\n    at authMiddleware (/Users/<USER>/workspaces/git/unified-project-hub/backend/src/middleware/auth.ts:71:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:328:13)","timestamp":"2025-07-17 21:40:04","url":"/api/projects","userAgent":"curl/8.7.1","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:40:52","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:40:52","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:40:52","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:02","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:02","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:02","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:13","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:13","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:13","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:17","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:25","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:25","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:25","version":"1.0.0"}
