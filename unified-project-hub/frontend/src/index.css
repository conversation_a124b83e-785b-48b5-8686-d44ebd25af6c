@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground)) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground));
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--foreground));
  }

  /* Loading animation */
  .animate-pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--muted)) 100%);
  }

  /* Status indicators */
  .status-dot {
    @apply inline-block w-2 h-2 rounded-full mr-2;
  }

  .status-active {
    @apply bg-green-500;
  }

  .status-inactive {
    @apply bg-gray-400;
  }

  .status-pending {
    @apply bg-yellow-500;
  }

  .status-error {
    @apply bg-red-500;
  }

  /* Priority indicators */
  .priority-p0 {
    @apply bg-red-600 text-white;
  }

  .priority-p1 {
    @apply bg-orange-500 text-white;
  }

  .priority-p2 {
    @apply bg-yellow-500 text-black;
  }

  .priority-p3 {
    @apply bg-blue-500 text-white;
  }

  .priority-p4 {
    @apply bg-gray-500 text-white;
  }

  /* Task status colors */
  .task-todo {
    @apply bg-gray-100 text-gray-800 border-gray-300;
  }

  .task-in-progress {
    @apply bg-blue-100 text-blue-800 border-blue-300;
  }

  .task-done {
    @apply bg-green-100 text-green-800 border-green-300;
  }

  .task-blocked {
    @apply bg-red-100 text-red-800 border-red-300;
  }

  /* Project status colors */
  .project-draft {
    @apply bg-gray-100 text-gray-800;
  }

  .project-requirements {
    @apply bg-purple-100 text-purple-800;
  }

  .project-backlog {
    @apply bg-yellow-100 text-yellow-800;
  }

  .project-not-started {
    @apply bg-orange-100 text-orange-800;
  }

  .project-in-progress {
    @apply bg-blue-100 text-blue-800;
  }

  .project-completed {
    @apply bg-green-100 text-green-800;
  }

  .project-archived {
    @apply bg-gray-100 text-gray-600;
  }

  .project-on-hold {
    @apply bg-red-100 text-red-800;
  }

  /* Dark mode variants */
  .dark .task-todo {
    @apply bg-gray-800 text-gray-200 border-gray-600;
  }

  .dark .task-in-progress {
    @apply bg-blue-900 text-blue-200 border-blue-700;
  }

  .dark .task-done {
    @apply bg-green-900 text-green-200 border-green-700;
  }

  .dark .task-blocked {
    @apply bg-red-900 text-red-200 border-red-700;
  }

  .dark .project-draft {
    @apply bg-gray-800 text-gray-200;
  }

  .dark .project-requirements {
    @apply bg-purple-900 text-purple-200;
  }

  .dark .project-backlog {
    @apply bg-yellow-900 text-yellow-200;
  }

  .dark .project-not-started {
    @apply bg-orange-900 text-orange-200;
  }

  .dark .project-in-progress {
    @apply bg-blue-900 text-blue-200;
  }

  .dark .project-completed {
    @apply bg-green-900 text-green-200;
  }

  .dark .project-archived {
    @apply bg-gray-800 text-gray-400;
  }

  .dark .project-on-hold {
    @apply bg-red-900 text-red-200;
  }
}

@layer utilities {
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Layout utilities */
  .container-fluid {
    @apply w-full max-w-none px-4 sm:px-6 lg:px-8;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
