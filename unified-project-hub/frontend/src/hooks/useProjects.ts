import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface ProjectFilters {
  page?: number;
  limit?: number;
  status?: string;
  type?: string;
  search?: string;
  priority_level?: string;
  lead_id?: string;
  company_id?: string;
  show_archived?: boolean;
  sort_by?: string;
  sort_order?: string;
  group_by?: string;
}

interface ProjectsResponse {
  projects: any[];
  grouped_projects?: Record<string, any[]>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  filters: any;
  sorting: any;
  grouping: any;
}

export function useProjects(filters: ProjectFilters = {}) {
  const { token } = useAuth();
  const [data, setData] = useState<ProjectsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProjects = async () => {
    if (!token) return;

    try {
      setLoading(true);
      setError(null);

      const queryParams = new URLSearchParams();
      
      // Add all filters to query params
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '' && value !== 'all') {
          queryParams.append(key, String(value));
        }
      });

      const response = await fetch(`/api/projects?${queryParams.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.status === 'success') {
        setData(result.data);
      } else {
        throw new Error(result.message || 'Failed to fetch projects');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching projects:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, [token, JSON.stringify(filters)]);

  const refetch = () => {
    fetchProjects();
  };

  return {
    data,
    loading,
    error,
    refetch,
  };
}

export function useProjectStats() {
  const { token } = useAuth();
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      if (!token) return;

      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/projects/stats/overview', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        
        if (result.status === 'success') {
          setStats(result.data);
        } else {
          throw new Error(result.message || 'Failed to fetch project stats');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        console.error('Error fetching project stats:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [token]);

  return {
    stats,
    loading,
    error,
  };
}

export function useProjectActions() {
  const { token } = useAuth();

  const updateProjectStatus = async (projectId: string, status: string, reason?: string) => {
    if (!token) throw new Error('No authentication token');

    const response = await fetch(`/api/projects/${projectId}/status`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status, reason }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.status !== 'success') {
      throw new Error(result.message || 'Failed to update project status');
    }

    return result.data;
  };

  const archiveProject = async (projectId: string, archive: boolean = true) => {
    if (!token) throw new Error('No authentication token');

    const response = await fetch(`/api/projects/${projectId}/archive`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ archive }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.status !== 'success') {
      throw new Error(result.message || 'Failed to archive project');
    }

    return result.data;
  };

  const updateProjectPriority = async (projectId: string, priority_level: string, reason?: string) => {
    if (!token) throw new Error('No authentication token');

    const response = await fetch(`/api/projects/${projectId}/priority`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ priority_level, reason }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.status !== 'success') {
      throw new Error(result.message || 'Failed to update project priority');
    }

    return result.data;
  };

  const deleteProject = async (projectId: string) => {
    if (!token) throw new Error('No authentication token');

    const response = await fetch(`/api/projects/${projectId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.status !== 'success') {
      throw new Error(result.message || 'Failed to delete project');
    }

    return result;
  };

  return {
    updateProjectStatus,
    archiveProject,
    updateProjectPriority,
    deleteProject,
  };
}
