import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireRole } from '../middleware/auth';

const router = Router();
const prisma = new PrismaClient();

// Validation schemas
const createRequirementSchema = z.object({
  project_id: z.string().uuid('Invalid project ID'),
  title: z.string().min(1, 'Requirement title is required'),
  description: z.string().min(1, 'Requirement description is required'),
  type: z.enum(['functional', 'non_functional', 'business', 'technical']),
  priority_level: z.enum(['P0', 'P1', 'P2', 'P3', 'P4']).default('P3'),
  detailed_specification: z.string().optional(),
  acceptance_criteria: z.array(z.string()).default([]),
  user_stories: z.array(z.string()).default([]),
  assigned_to_id: z.string().uuid().optional(),
  mockup_urls: z.array(z.string().url()).default([]),
  diagram_urls: z.array(z.string().url()).default([]),
  reference_urls: z.array(z.string().url()).default([]),
});

/**
 * @route GET /api/requirements
 * @desc Get requirements (filtered by user role and permissions)
 * @access Private
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { page = '1', limit = '10', status, type, project_id, assigned_to_id } = req.query;
  const skip = (parseInt(page as string) - 1) * parseInt(limit as string);

  let whereClause: any = {};

  // Filter based on user role
  if (req.user?.role === 'customer') {
    // Customers cannot access requirements directly
    return res.status(403).json({
      status: 'error',
      message: 'Access denied',
    });
  } else if (req.user?.role === 'team_member') {
    whereClause.OR = [
      { created_by_id: req.user.id },
      { assigned_to_id: req.user.id },
      { project: { project_lead_id: req.user.id } },
      { project: { team_members: { some: { user_id: req.user.id } } } },
    ];
  }

  // Apply filters
  if (status) whereClause.status = status;
  if (type) whereClause.type = type;
  if (project_id) whereClause.project_id = project_id;
  if (assigned_to_id) whereClause.assigned_to_id = assigned_to_id;

  const [requirements, total] = await Promise.all([
    prisma.requirement.findMany({
      where: whereClause,
      include: {
        project: {
          select: { id: true, name: true, company_name: true },
        },
        created_by: {
          select: { id: true, name: true, email: true, avatar_url: true },
        },
        assigned_to: {
          select: { id: true, name: true, email: true, avatar_url: true },
        },
        approved_by: {
          select: { id: true, name: true, email: true, avatar_url: true },
        },
        task_requirements: {
          include: {
            task: {
              select: { id: true, name: true, status: true },
            },
          },
        },
      },
      skip,
      take: parseInt(limit as string),
      orderBy: { updated_at: 'desc' },
    }),
    prisma.requirement.count({ where: whereClause }),
  ]);

  res.json({
    status: 'success',
    data: {
      requirements,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        pages: Math.ceil(total / parseInt(limit as string)),
      },
    },
  });
}));

/**
 * @route GET /api/requirements/:id
 * @desc Get requirement by ID
 * @access Private
 */
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const requirement = await prisma.requirement.findFirst({
    where: {
      id,
      ...(req.user?.role === 'team_member' ? {
        OR: [
          { created_by_id: req.user.id },
          { assigned_to_id: req.user.id },
          { project: { project_lead_id: req.user.id } },
          { project: { team_members: { some: { user_id: req.user.id } } } },
        ],
      } : {}),
    },
    include: {
      project: {
        select: { id: true, name: true, company_name: true },
      },
      created_by: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
      assigned_to: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
      approved_by: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
      task_requirements: {
        include: {
          task: {
            include: {
              assignee: {
                select: { id: true, name: true, email: true, avatar_url: true },
              },
            },
          },
        },
      },
    },
  });

  if (!requirement) {
    return res.status(404).json({
      status: 'error',
      message: 'Requirement not found or access denied',
    });
  }

  res.json({
    status: 'success',
    data: { requirement },
  });
}));

/**
 * @route POST /api/requirements
 * @desc Create new requirement
 * @access Private (Admin, Project Manager, Requirements Analyst)
 */
router.post('/', requireRole('admin', 'project_manager', 'requirements_analyst'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createRequirementSchema.parse(req.body);

  // Check if user can access the project
  const project = await prisma.project.findFirst({
    where: {
      id: validatedData.project_id,
      OR: [
        { project_lead_id: req.user!.id },
        { team_members: { some: { user_id: req.user!.id } } },
        ...(req.user!.role === 'admin' || req.user!.role === 'project_manager' ? [{}] : []),
      ],
    },
  });

  if (!project) {
    return res.status(404).json({
      status: 'error',
      message: 'Project not found or access denied',
    });
  }

  const requirement = await prisma.requirement.create({
    data: {
      ...validatedData,
      created_by_id: req.user!.id,
    },
    include: {
      project: {
        select: { id: true, name: true, company_name: true },
      },
      created_by: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
      assigned_to: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
    },
  });

  res.status(201).json({
    status: 'success',
    message: 'Requirement created successfully',
    data: { requirement },
  });
}));

/**
 * @route PUT /api/requirements/:id
 * @desc Update requirement
 * @access Private
 */
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const updateData = createRequirementSchema.partial().parse(req.body);

  // Check if user can update this requirement
  const existingRequirement = await prisma.requirement.findFirst({
    where: {
      id,
      OR: [
        { created_by_id: req.user!.id },
        { assigned_to_id: req.user!.id },
        { project: { project_lead_id: req.user!.id } },
        ...(req.user!.role === 'admin' || req.user!.role === 'project_manager' ? [{}] : []),
      ],
    },
  });

  if (!existingRequirement) {
    return res.status(404).json({
      status: 'error',
      message: 'Requirement not found or access denied',
    });
  }

  const requirement = await prisma.requirement.update({
    where: { id },
    data: updateData,
    include: {
      project: {
        select: { id: true, name: true, company_name: true },
      },
      created_by: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
      assigned_to: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
      approved_by: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
    },
  });

  res.json({
    status: 'success',
    message: 'Requirement updated successfully',
    data: { requirement },
  });
}));

/**
 * @route PUT /api/requirements/:id/approve
 * @desc Approve requirement
 * @access Private (Admin, Project Manager)
 */
router.put('/:id/approve', requireRole('admin', 'project_manager'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const requirement = await prisma.requirement.update({
    where: { id },
    data: {
      status: 'approved',
      approved_by_id: req.user!.id,
      approved_at: new Date(),
    },
    include: {
      project: {
        select: { id: true, name: true, company_name: true },
      },
      created_by: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
      approved_by: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
    },
  });

  res.json({
    status: 'success',
    message: 'Requirement approved successfully',
    data: { requirement },
  });
}));

/**
 * @route DELETE /api/requirements/:id
 * @desc Delete requirement
 * @access Private (Admin, Project Manager, Creator)
 */
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if user can delete this requirement
  const existingRequirement = await prisma.requirement.findFirst({
    where: {
      id,
      OR: [
        { created_by_id: req.user!.id },
        ...(req.user!.role === 'admin' || req.user!.role === 'project_manager' ? [{}] : []),
      ],
    },
  });

  if (!existingRequirement) {
    return res.status(404).json({
      status: 'error',
      message: 'Requirement not found or access denied',
    });
  }

  await prisma.requirement.delete({
    where: { id },
  });

  res.json({
    status: 'success',
    message: 'Requirement deleted successfully',
  });
}));

export default router;
