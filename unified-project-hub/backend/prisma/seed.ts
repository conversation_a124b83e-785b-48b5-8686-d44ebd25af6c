import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create users
  const hashedPassword = await bcrypt.hash('password123', 12);

  await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password_hash: hashedPassword,
      name: 'System Administrator',
      role: 'admin',
      title: 'System Administrator',
      department: 'IT',
      is_active: true,
      weekly_capacity_hours: 40,
      current_utilization_percentage: 75,
      skills: ['System Administration', 'Project Management', 'Leadership'],
    },
  });

  const projectManager = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password_hash: hashedPassword,
      name: '<PERSON>',
      role: 'project_manager',
      title: 'Senior Project Manager',
      department: 'Product',
      is_active: true,
      weekly_capacity_hours: 40,
      current_utilization_percentage: 85,
      skills: ['Project Management', 'Agile', 'Scrum', 'Leadership'],
    },
  });

  const developer1 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password_hash: hashedPassword,
      name: '<PERSON>',
      role: 'team_member',
      title: 'Senior Full Stack Developer',
      department: 'Engineering',
      is_active: true,
      weekly_capacity_hours: 40,
      current_utilization_percentage: 90,
      skills: ['React', 'Node.js', 'TypeScript', 'PostgreSQL', 'AWS'],
    },
  });

  const developer2 = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password_hash: hashedPassword,
      name: 'Carol Davis',
      role: 'team_member',
      title: 'Frontend Developer',
      department: 'Engineering',
      is_active: true,
      weekly_capacity_hours: 40,
      current_utilization_percentage: 70,
      skills: ['React', 'TypeScript', 'CSS', 'UI/UX', 'Figma'],
    },
  });

  const requirementsAnalyst = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password_hash: hashedPassword,
      name: 'David Wilson',
      role: 'requirements_analyst',
      title: 'Business Analyst',
      department: 'Product',
      is_active: true,
      weekly_capacity_hours: 40,
      current_utilization_percentage: 60,
      skills: ['Requirements Analysis', 'Documentation', 'User Stories', 'Process Mapping'],
    },
  });

  await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password_hash: hashedPassword,
      name: 'John Customer',
      role: 'customer',
      title: 'CTO',
      department: 'Technology',
      is_active: true,
      skills: ['Technology Strategy', 'Product Vision'],
    },
  });

  console.log('✅ Users created');

  // Create companies
  const acmeCorp = await prisma.company.create({
    data: {
      name: 'Acme Corporation',
      status: 'active',
      industry: 'Technology',
      size: 'Medium (50-200 employees)',
      website: 'https://acmecorp.com',
      email: '<EMAIL>',
      phone: '******-0123',
      description: 'Leading technology solutions provider',
      deal_value: 150000,
      deal_stage: 'Negotiation',
      active_projects_count: 2,
      total_projects_count: 5,
    },
  });

  const techStartup = await prisma.company.create({
    data: {
      name: 'TechStartup Inc',
      status: 'prospect',
      industry: 'Software',
      size: 'Small (1-50 employees)',
      website: 'https://techstartup.io',
      email: '<EMAIL>',
      description: 'Innovative startup building the future',
      deal_value: 75000,
      deal_stage: 'Discovery',
      active_projects_count: 1,
      total_projects_count: 1,
    },
  });

  console.log('✅ Companies created');

  // Create contacts
  const acmeContact = await prisma.contact.create({
    data: {
      company_id: acmeCorp.id,
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '******-0124',
      role: 'primary',
      title: 'Product Manager',
      department: 'Product',
      is_primary: true,
      is_active: true,
      preferred_contact_method: 'email',
      timezone: 'America/New_York',
    },
  });

  const techContact = await prisma.contact.create({
    data: {
      company_id: techStartup.id,
      name: 'Mike Johnson',
      email: '<EMAIL>',
      phone: '******-0125',
      role: 'technical',
      title: 'Lead Developer',
      department: 'Engineering',
      is_primary: true,
      is_active: true,
      preferred_contact_method: 'email',
      timezone: 'America/Los_Angeles',
    },
  });

  console.log('✅ Contacts created');

  // Create projects
  const ecommerceProject = await prisma.project.create({
    data: {
      name: 'E-commerce Platform Redesign',
      description: 'Complete overhaul of the existing e-commerce platform with modern UI/UX and improved performance',
      type: 'external',
      status: 'in_progress',
      company_id: acmeCorp.id,
      company_name: 'Acme Corporation',
      customer_contact_id: acmeContact.id,
      project_lead_id: projectManager.id,
      priority_level: 'P1',
      effort_estimate: 'XL',
      impact_type: 'Revenue',
      start_date: new Date('2024-01-15'),
      end_date: new Date('2024-06-30'),
      progress_percentage: 65,
      budget: 150000,
      actual_cost: 95000,
      is_customer_visible: true,
      customer_visible_fields: ['name', 'description', 'progress_percentage', 'start_date', 'end_date'],
      tags: ['e-commerce', 'redesign', 'ui/ux', 'performance'],
      requirements_completion_percentage: 80,
    },
  });

  const internalToolProject = await prisma.project.create({
    data: {
      name: 'Internal Analytics Dashboard',
      description: 'Build internal dashboard for team productivity analytics',
      type: 'internal',
      status: 'in_progress',
      company_name: 'ProjectHub Internal',
      project_lead_id: developer1.id,
      priority_level: 'P2',
      effort_estimate: 'L',
      impact_type: 'Platform',
      start_date: new Date('2024-02-01'),
      end_date: new Date('2024-04-30'),
      progress_percentage: 40,
      budget: 50000,
      actual_cost: 25000,
      is_customer_visible: false,
      tags: ['internal', 'analytics', 'dashboard'],
      requirements_completion_percentage: 60,
    },
  });

  const mobileAppProject = await prisma.project.create({
    data: {
      name: 'Mobile App Development',
      description: 'Native mobile application for iOS and Android',
      type: 'external',
      status: 'requirements',
      company_id: techStartup.id,
      company_name: 'TechStartup Inc',
      customer_contact_id: techContact.id,
      project_lead_id: projectManager.id,
      priority_level: 'P1',
      effort_estimate: 'XXL',
      impact_type: 'Revenue',
      start_date: new Date('2024-03-01'),
      end_date: new Date('2024-09-30'),
      progress_percentage: 15,
      budget: 200000,
      is_customer_visible: true,
      customer_visible_fields: ['name', 'description', 'progress_percentage', 'start_date', 'end_date'],
      tags: ['mobile', 'ios', 'android', 'native'],
      requirements_completion_percentage: 30,
    },
  });

  // Additional Core Project Pulse style projects
  const aiChatbotProject = await prisma.project.create({
    data: {
      name: 'AI Customer Support Chatbot',
      description: 'Intelligent chatbot integration for customer support with natural language processing and automated ticket routing',
      type: 'external',
      status: 'backlog',
      company_id: acmeCorp.id,
      company_name: 'Acme Corporation',
      customer_contact_id: acmeContact.id,
      project_lead_id: developer1.id,
      priority_level: 'P2',
      effort_estimate: 'L',
      impact_type: 'Customer_Success',
      start_date: new Date('2024-04-01'),
      end_date: new Date('2024-06-15'),
      progress_percentage: 5,
      budget: 80000,
      is_customer_visible: true,
      customer_visible_fields: ['name', 'description', 'progress_percentage'],
      tags: ['ai', 'chatbot', 'nlp', 'customer-support'],
      requirements_completion_percentage: 20,
    },
  });

  const securityAuditProject = await prisma.project.create({
    data: {
      name: 'Security Infrastructure Audit',
      description: 'Comprehensive security audit and implementation of enhanced security measures across all systems',
      type: 'internal',
      status: 'not_started',
      company_name: 'ProjectHub Internal',
      project_lead_id: developer2.id,
      priority_level: 'P0',
      effort_estimate: 'M',
      impact_type: 'Platform',
      start_date: new Date('2024-05-01'),
      end_date: new Date('2024-07-31'),
      progress_percentage: 0,
      budget: 60000,
      is_customer_visible: false,
      tags: ['security', 'audit', 'infrastructure', 'compliance'],
      requirements_completion_percentage: 0,
    },
  });

  await prisma.project.create({
    data: {
      name: 'Legacy API Migration',
      description: 'Migration from legacy REST APIs to modern GraphQL with improved performance and developer experience',
      type: 'internal',
      status: 'archived',
      company_name: 'ProjectHub Internal',
      project_lead_id: developer1.id,
      priority_level: 'P3',
      effort_estimate: 'M',
      impact_type: 'Platform',
      start_date: new Date('2023-06-01'),
      end_date: new Date('2023-08-31'),
      progress_percentage: 100,
      budget: 45000,
      actual_cost: 48000,
      is_customer_visible: false,
      tags: ['api', 'migration', 'graphql', 'legacy'],
      requirements_completion_percentage: 100,
    },
  });

  const dataAnalyticsProject = await prisma.project.create({
    data: {
      name: 'Real-time Data Analytics Platform',
      description: 'Comprehensive analytics platform with real-time data visualization, custom reports, and automated insights',
      type: 'external',
      status: 'in_progress',
      company_id: techStartup.id,
      company_name: 'TechStartup Inc',
      customer_contact_id: techContact.id,
      project_lead_id: requirementsAnalyst.id,
      priority_level: 'P2',
      effort_estimate: 'XL',
      impact_type: 'Revenue',
      start_date: new Date('2024-02-15'),
      end_date: new Date('2024-08-30'),
      progress_percentage: 45,
      budget: 120000,
      actual_cost: 65000,
      is_customer_visible: true,
      customer_visible_fields: ['name', 'description', 'progress_percentage', 'start_date'],
      tags: ['analytics', 'real-time', 'visualization', 'platform'],
      requirements_completion_percentage: 70,
    },
  });

  console.log('✅ Projects created');

  // Create project team members
  await prisma.projectTeamMember.createMany({
    data: [
      // E-commerce project team
      { project_id: ecommerceProject.id, user_id: developer1.id },
      { project_id: ecommerceProject.id, user_id: developer2.id },
      { project_id: ecommerceProject.id, user_id: requirementsAnalyst.id },

      // Internal analytics project team
      { project_id: internalToolProject.id, user_id: developer2.id },
      { project_id: internalToolProject.id, user_id: requirementsAnalyst.id },

      // Mobile app project team
      { project_id: mobileAppProject.id, user_id: developer1.id },
      { project_id: mobileAppProject.id, user_id: requirementsAnalyst.id },
      { project_id: mobileAppProject.id, user_id: projectManager.id },

      // AI chatbot project team
      { project_id: aiChatbotProject.id, user_id: developer1.id },
      { project_id: aiChatbotProject.id, user_id: developer2.id },

      // Security audit project team
      { project_id: securityAuditProject.id, user_id: developer2.id },
      { project_id: securityAuditProject.id, user_id: projectManager.id },

      // Data analytics project team
      { project_id: dataAnalyticsProject.id, user_id: requirementsAnalyst.id },
      { project_id: dataAnalyticsProject.id, user_id: developer1.id },
      { project_id: dataAnalyticsProject.id, user_id: developer2.id },
    ],
  });

  console.log('✅ Project team members assigned');

  // Create requirements
  const ecommerceReq1 = await prisma.requirement.create({
    data: {
      project_id: ecommerceProject.id,
      title: 'User Authentication System',
      description: 'Implement secure user authentication with OAuth support',
      type: 'functional',
      status: 'approved',
      priority_level: 'P1',
      detailed_specification: '# User Authentication\n\n## Overview\nImplement a secure authentication system...',
      acceptance_criteria: [
        'Users can register with email and password',
        'Users can login with OAuth providers (Google, Facebook)',
        'Password reset functionality works correctly',
        'Session management is secure'
      ],
      user_stories: [
        'As a user, I want to register with my email so that I can access the platform',
        'As a user, I want to login with my Google account for convenience'
      ],
      created_by_id: requirementsAnalyst.id,
      assigned_to_id: developer1.id,
      approved_by_id: projectManager.id,
      approved_at: new Date('2024-01-20'),
    },
  });

  console.log('✅ Requirements created');

  // Create tasks
  const authTask = await prisma.task.create({
    data: {
      project_id: ecommerceProject.id,
      name: 'Implement OAuth Authentication',
      description: 'Set up OAuth authentication with Google and Facebook providers',
      status: 'in_progress',
      priority_level: 'P1',
      effort_estimate: 'L',
      assignee_id: developer1.id,
      reviewer_id: projectManager.id,
      due_date: new Date('2024-02-15'),
      is_customer_visible: true,
      customer_description: 'Setting up secure login system',
      tags: ['authentication', 'oauth', 'security'],
      estimated_hours: 40,
      actual_hours: 25,
    },
  });

  const uiTask = await prisma.task.create({
    data: {
      project_id: ecommerceProject.id,
      name: 'Design Login/Register UI',
      description: 'Create responsive login and registration forms',
      status: 'done',
      priority_level: 'P2',
      effort_estimate: 'M',
      assignee_id: developer2.id,
      reviewer_id: projectManager.id,
      due_date: new Date('2024-02-01'),
      completed_at: new Date('2024-01-30'),
      is_customer_visible: true,
      customer_description: 'Login and registration interface',
      tags: ['ui', 'forms', 'responsive'],
      estimated_hours: 20,
      actual_hours: 18,
    },
  });

  // Additional tasks for other projects
  const mobileAuthTask = await prisma.task.create({
    data: {
      project_id: mobileAppProject.id,
      name: 'Mobile Authentication Flow',
      description: 'Implement biometric and social authentication for mobile app',
      status: 'in_progress',
      priority_level: 'P1',
      effort_estimate: 'M',
      assignee_id: developer1.id,
      due_date: new Date('2024-05-15'),
      is_customer_visible: true,
      customer_description: 'Secure mobile login with biometrics',
      tags: ['mobile', 'authentication', 'biometric'],
      estimated_hours: 50,
      actual_hours: 20,
    },
  });

  await prisma.task.create({
    data: {
      project_id: mobileAppProject.id,
      name: 'Offline Data Synchronization',
      description: 'Implement offline capabilities with data sync when online',
      status: 'to_do',
      priority_level: 'P2',
      effort_estimate: 'L',
      assignee_id: developer2.id,
      due_date: new Date('2024-06-01'),
      is_customer_visible: false,
      tags: ['offline', 'sync', 'data'],
      estimated_hours: 45,
    },
  });

  await prisma.task.create({
    data: {
      project_id: aiChatbotProject.id,
      name: 'Natural Language Processing Setup',
      description: 'Configure NLP engine for intent recognition and response generation',
      status: 'to_do',
      priority_level: 'P1',
      effort_estimate: 'M',
      assignee_id: developer1.id,
      due_date: new Date('2024-04-30'),
      is_customer_visible: true,
      customer_description: 'Smart conversation understanding',
      tags: ['nlp', 'ai', 'intent-recognition'],
      estimated_hours: 55,
    },
  });

  const dashboardTask = await prisma.task.create({
    data: {
      project_id: dataAnalyticsProject.id,
      name: 'Real-time Dashboard Development',
      description: 'Build interactive dashboard with real-time data visualization',
      status: 'in_progress',
      priority_level: 'P2',
      effort_estimate: 'XL',
      assignee_id: requirementsAnalyst.id,
      reviewer_id: projectManager.id,
      due_date: new Date('2024-06-15'),
      start_date: new Date('2024-03-01'),
      is_customer_visible: true,
      customer_description: 'Interactive analytics dashboard',
      tags: ['dashboard', 'visualization', 'real-time'],
      estimated_hours: 90,
      actual_hours: 45,
    },
  });

  await prisma.task.create({
    data: {
      project_id: securityAuditProject.id,
      name: 'Security Vulnerability Assessment',
      description: 'Comprehensive security scan and vulnerability assessment of all systems',
      status: 'to_do',
      priority_level: 'P0',
      effort_estimate: 'L',
      assignee_id: developer2.id,
      due_date: new Date('2024-05-15'),
      is_customer_visible: false,
      tags: ['security', 'vulnerability', 'assessment'],
      estimated_hours: 35,
    },
  });

  console.log('✅ Tasks created');

  // Link tasks to requirements
  await prisma.taskRequirement.createMany({
    data: [
      { task_id: authTask.id, requirement_id: ecommerceReq1.id },
      { task_id: uiTask.id, requirement_id: ecommerceReq1.id },
    ],
  });

  // Create sub-tasks (hierarchical task structure from Core Project Pulse)
  await prisma.task.create({
    data: {
      project_id: ecommerceProject.id,
      parent_task_id: authTask.id,
      name: 'Setup OAuth Providers',
      description: 'Configure Google and Facebook OAuth applications',
      status: 'done',
      priority_level: 'P1',
      effort_estimate: 'S',
      assignee_id: developer1.id,
      due_date: new Date('2024-02-05'),
      completed_at: new Date('2024-02-03'),
      is_customer_visible: false,
      tags: ['oauth', 'configuration'],
      estimated_hours: 8,
      actual_hours: 6,
    },
  });

  await prisma.task.create({
    data: {
      project_id: ecommerceProject.id,
      parent_task_id: authTask.id,
      name: 'Implement JWT Token Management',
      description: 'Create secure JWT token generation and validation',
      status: 'in_progress',
      priority_level: 'P1',
      effort_estimate: 'S',
      assignee_id: developer1.id,
      due_date: new Date('2024-02-10'),
      is_customer_visible: false,
      tags: ['jwt', 'security', 'tokens'],
      estimated_hours: 12,
      actual_hours: 8,
    },
  });

  await prisma.task.create({
    data: {
      project_id: ecommerceProject.id,
      parent_task_id: authTask.id,
      name: 'User Session Management',
      description: 'Implement secure session handling and logout functionality',
      status: 'to_do',
      priority_level: 'P2',
      effort_estimate: 'S',
      assignee_id: developer1.id,
      due_date: new Date('2024-02-15'),
      is_customer_visible: false,
      tags: ['session', 'logout'],
      estimated_hours: 10,
    },
  });

  // Mobile app sub-tasks
  const _mobileSubTask1 = await prisma.task.create({
    data: {
      project_id: mobileAppProject.id,
      parent_task_id: mobileAuthTask.id,
      name: 'Biometric Authentication Setup',
      description: 'Implement fingerprint and face recognition authentication',
      status: 'in_progress',
      priority_level: 'P1',
      effort_estimate: 'M',
      assignee_id: developer1.id,
      due_date: new Date('2024-05-01'),
      is_customer_visible: true,
      customer_description: 'Fingerprint and face unlock',
      tags: ['biometric', 'fingerprint', 'face-recognition'],
      estimated_hours: 25,
      actual_hours: 12,
    },
  });

  const _mobileSubTask2 = await prisma.task.create({
    data: {
      project_id: mobileAppProject.id,
      parent_task_id: mobileAuthTask.id,
      name: 'Social Login Integration',
      description: 'Add Google and Apple Sign-In for mobile app',
      status: 'to_do',
      priority_level: 'P2',
      effort_estimate: 'S',
      assignee_id: developer1.id,
      due_date: new Date('2024-05-10'),
      is_customer_visible: true,
      customer_description: 'Sign in with Google/Apple',
      tags: ['social-login', 'google', 'apple'],
      estimated_hours: 15,
    },
  });

  // Dashboard sub-tasks
  const _dashboardSubTask1 = await prisma.task.create({
    data: {
      project_id: dataAnalyticsProject.id,
      parent_task_id: dashboardTask.id,
      name: 'Chart Components Development',
      description: 'Create reusable chart components for data visualization',
      status: 'done',
      priority_level: 'P2',
      effort_estimate: 'M',
      assignee_id: requirementsAnalyst.id,
      due_date: new Date('2024-04-01'),
      completed_at: new Date('2024-03-28'),
      is_customer_visible: true,
      customer_description: 'Interactive charts and graphs',
      tags: ['charts', 'visualization', 'components'],
      estimated_hours: 30,
      actual_hours: 28,
    },
  });

  const _dashboardSubTask2 = await prisma.task.create({
    data: {
      project_id: dataAnalyticsProject.id,
      parent_task_id: dashboardTask.id,
      name: 'Real-time Data Streaming',
      description: 'Implement WebSocket connections for real-time data updates',
      status: 'in_progress',
      priority_level: 'P1',
      effort_estimate: 'L',
      assignee_id: developer2.id,
      due_date: new Date('2024-05-15'),
      is_customer_visible: false,
      tags: ['websocket', 'real-time', 'streaming'],
      estimated_hours: 35,
      actual_hours: 15,
    },
  });

  console.log('✅ Sub-tasks created');

  // Create interactions
  await prisma.interaction.create({
    data: {
      project_id: ecommerceProject.id,
      company_id: acmeCorp.id,
      contact_id: acmeContact.id,
      type: 'meeting',
      subject: 'Project Kickoff Meeting',
      content: 'Discussed project scope, timeline, and deliverables. Client confirmed requirements and budget.',
      created_by_id: projectManager.id,
      interaction_date: new Date('2024-01-15'),
      is_customer_visible: true,
      tags: ['kickoff', 'planning'],
    },
  });

  await prisma.interaction.create({
    data: {
      project_id: ecommerceProject.id,
      type: 'note',
      subject: 'Technical Architecture Review',
      content: 'Reviewed technical architecture with the team. Decided on microservices approach.',
      created_by_id: developer1.id,
      interaction_date: new Date('2024-01-18'),
      is_customer_visible: false,
      tags: ['architecture', 'technical'],
    },
  });

  console.log('✅ Interactions created');

  console.log('🎉 Database seed completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
