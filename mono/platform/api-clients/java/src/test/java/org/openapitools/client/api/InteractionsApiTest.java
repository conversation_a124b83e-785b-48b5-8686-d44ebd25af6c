/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.api;

import org.openapitools.client.ApiException;
import org.openapitools.client.model.Interaction;
import org.openapitools.client.model.InteractionCreate;
import org.openapitools.client.model.InteractionDetails;
import org.openapitools.client.model.InteractionUpdate;
import org.openapitools.client.model.InteractionsGet200Response;
import java.time.OffsetDateTime;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for InteractionsApi
 */
@Disabled
public class InteractionsApiTest {

    private final InteractionsApi api = new InteractionsApi();

    /**
     * List interactions
     *
     * Retrieve a list of all interactions
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void interactionsGetTest() throws ApiException {
        UUID companyId = null;
        UUID contactId = null;
        String interactionType = null;
        OffsetDateTime fromDate = null;
        OffsetDateTime toDate = null;
        Integer limit = null;
        Integer offset = null;
        InteractionsGet200Response response = api.interactionsGet(companyId, contactId, interactionType, fromDate, toDate, limit, offset);
        // TODO: test validations
    }

    /**
     * Delete interaction
     *
     * Delete an interaction record
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void interactionsIdDeleteTest() throws ApiException {
        UUID id = null;
        api.interactionsIdDelete(id);
        // TODO: test validations
    }

    /**
     * Get interaction
     *
     * Retrieve a specific interaction by ID
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void interactionsIdGetTest() throws ApiException {
        UUID id = null;
        InteractionDetails response = api.interactionsIdGet(id);
        // TODO: test validations
    }

    /**
     * Update interaction
     *
     * Update an existing interaction
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void interactionsIdPutTest() throws ApiException {
        UUID id = null;
        InteractionUpdate interactionUpdate = null;
        Interaction response = api.interactionsIdPut(id, interactionUpdate);
        // TODO: test validations
    }

    /**
     * Create interaction
     *
     * Create a new interaction record
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void interactionsPostTest() throws ApiException {
        InteractionCreate interactionCreate = null;
        Interaction response = api.interactionsPost(interactionCreate);
        // TODO: test validations
    }

}
