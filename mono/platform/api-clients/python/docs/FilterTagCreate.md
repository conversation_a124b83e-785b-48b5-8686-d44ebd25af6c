# FilterTagCreate


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**name** | **str** |  | 
**color** | **str** |  | [optional] [default to '#3b82f6']

## Example

```python
from crm_api_client.models.filter_tag_create import FilterTagCreate

# TODO update the JSON string below
json = "{}"
# create an instance of FilterTagCreate from a JSON string
filter_tag_create_instance = FilterTagCreate.from_json(json)
# print the JSON string representation of the object
print(FilterTagCreate.to_json())

# convert the object into a dict
filter_tag_create_dict = filter_tag_create_instance.to_dict()
# create an instance of FilterTagCreate from a dict
filter_tag_create_from_dict = FilterTagCreate.from_dict(filter_tag_create_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


