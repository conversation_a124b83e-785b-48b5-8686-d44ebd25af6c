/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.api;

import org.openapitools.client.ApiCallback;
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.ApiResponse;
import org.openapitools.client.Configuration;
import org.openapitools.client.Pair;
import org.openapitools.client.ProgressRequestBody;
import org.openapitools.client.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import org.openapitools.client.model.Contact;
import org.openapitools.client.model.ContactCompanyUpdateRequest;
import org.openapitools.client.model.ContactCreate;
import org.openapitools.client.model.ContactDetails;
import org.openapitools.client.model.ContactUpdate;
import org.openapitools.client.model.ContactWithCompany;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ContactsApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public ContactsApi() {
        this(Configuration.getDefaultApiClient());
    }

    public ContactsApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for contactsGet
     * @param companyId Filter by company ID (optional)
     * @param unlinked Only return contacts without a company (optional)
     * @param includeDeleted Include soft-deleted contacts (optional, default to false)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of contacts </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call contactsGetCall(UUID companyId, Boolean unlinked, Boolean includeDeleted, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/contacts";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (companyId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("company_id", companyId));
        }

        if (unlinked != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("unlinked", unlinked));
        }

        if (includeDeleted != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("include_deleted", includeDeleted));
        }

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call contactsGetValidateBeforeCall(UUID companyId, Boolean unlinked, Boolean includeDeleted, final ApiCallback _callback) throws ApiException {
        return contactsGetCall(companyId, unlinked, includeDeleted, _callback);

    }

    /**
     * List contacts
     * Retrieve a list of all active contacts with their associated companies
     * @param companyId Filter by company ID (optional)
     * @param unlinked Only return contacts without a company (optional)
     * @param includeDeleted Include soft-deleted contacts (optional, default to false)
     * @return List&lt;ContactWithCompany&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of contacts </td><td>  -  </td></tr>
     </table>
     */
    public List<ContactWithCompany> contactsGet(UUID companyId, Boolean unlinked, Boolean includeDeleted) throws ApiException {
        ApiResponse<List<ContactWithCompany>> localVarResp = contactsGetWithHttpInfo(companyId, unlinked, includeDeleted);
        return localVarResp.getData();
    }

    /**
     * List contacts
     * Retrieve a list of all active contacts with their associated companies
     * @param companyId Filter by company ID (optional)
     * @param unlinked Only return contacts without a company (optional)
     * @param includeDeleted Include soft-deleted contacts (optional, default to false)
     * @return ApiResponse&lt;List&lt;ContactWithCompany&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of contacts </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<ContactWithCompany>> contactsGetWithHttpInfo(UUID companyId, Boolean unlinked, Boolean includeDeleted) throws ApiException {
        okhttp3.Call localVarCall = contactsGetValidateBeforeCall(companyId, unlinked, includeDeleted, null);
        Type localVarReturnType = new TypeToken<List<ContactWithCompany>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * List contacts (asynchronously)
     * Retrieve a list of all active contacts with their associated companies
     * @param companyId Filter by company ID (optional)
     * @param unlinked Only return contacts without a company (optional)
     * @param includeDeleted Include soft-deleted contacts (optional, default to false)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of contacts </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call contactsGetAsync(UUID companyId, Boolean unlinked, Boolean includeDeleted, final ApiCallback<List<ContactWithCompany>> _callback) throws ApiException {

        okhttp3.Call localVarCall = contactsGetValidateBeforeCall(companyId, unlinked, includeDeleted, _callback);
        Type localVarReturnType = new TypeToken<List<ContactWithCompany>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for contactsIdCompanyDelete
     * @param id  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Contact unlinked from company successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call contactsIdCompanyDeleteCall(UUID id, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/contacts/{id}/company"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call contactsIdCompanyDeleteValidateBeforeCall(UUID id, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling contactsIdCompanyDelete(Async)");
        }

        return contactsIdCompanyDeleteCall(id, _callback);

    }

    /**
     * Unlink contact from company
     * Remove the association between a contact and company
     * @param id  (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Contact unlinked from company successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact not found </td><td>  -  </td></tr>
     </table>
     */
    public void contactsIdCompanyDelete(UUID id) throws ApiException {
        contactsIdCompanyDeleteWithHttpInfo(id);
    }

    /**
     * Unlink contact from company
     * Remove the association between a contact and company
     * @param id  (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Contact unlinked from company successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> contactsIdCompanyDeleteWithHttpInfo(UUID id) throws ApiException {
        okhttp3.Call localVarCall = contactsIdCompanyDeleteValidateBeforeCall(id, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Unlink contact from company (asynchronously)
     * Remove the association between a contact and company
     * @param id  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Contact unlinked from company successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call contactsIdCompanyDeleteAsync(UUID id, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = contactsIdCompanyDeleteValidateBeforeCall(id, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for contactsIdCompanyPut
     * @param id  (required)
     * @param contactCompanyUpdateRequest  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Contact linked to company successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact or company not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call contactsIdCompanyPutCall(UUID id, ContactCompanyUpdateRequest contactCompanyUpdateRequest, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = contactCompanyUpdateRequest;

        // create path and map variables
        String localVarPath = "/contacts/{id}/company"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call contactsIdCompanyPutValidateBeforeCall(UUID id, ContactCompanyUpdateRequest contactCompanyUpdateRequest, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling contactsIdCompanyPut(Async)");
        }

        // verify the required parameter 'contactCompanyUpdateRequest' is set
        if (contactCompanyUpdateRequest == null) {
            throw new ApiException("Missing the required parameter 'contactCompanyUpdateRequest' when calling contactsIdCompanyPut(Async)");
        }

        return contactsIdCompanyPutCall(id, contactCompanyUpdateRequest, _callback);

    }

    /**
     * Link contact to company
     * Associate a contact with a company
     * @param id  (required)
     * @param contactCompanyUpdateRequest  (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Contact linked to company successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact or company not found </td><td>  -  </td></tr>
     </table>
     */
    public void contactsIdCompanyPut(UUID id, ContactCompanyUpdateRequest contactCompanyUpdateRequest) throws ApiException {
        contactsIdCompanyPutWithHttpInfo(id, contactCompanyUpdateRequest);
    }

    /**
     * Link contact to company
     * Associate a contact with a company
     * @param id  (required)
     * @param contactCompanyUpdateRequest  (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Contact linked to company successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact or company not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> contactsIdCompanyPutWithHttpInfo(UUID id, ContactCompanyUpdateRequest contactCompanyUpdateRequest) throws ApiException {
        okhttp3.Call localVarCall = contactsIdCompanyPutValidateBeforeCall(id, contactCompanyUpdateRequest, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Link contact to company (asynchronously)
     * Associate a contact with a company
     * @param id  (required)
     * @param contactCompanyUpdateRequest  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Contact linked to company successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact or company not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call contactsIdCompanyPutAsync(UUID id, ContactCompanyUpdateRequest contactCompanyUpdateRequest, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = contactsIdCompanyPutValidateBeforeCall(id, contactCompanyUpdateRequest, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for contactsIdDelete
     * @param id  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Contact deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call contactsIdDeleteCall(UUID id, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/contacts/{id}"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call contactsIdDeleteValidateBeforeCall(UUID id, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling contactsIdDelete(Async)");
        }

        return contactsIdDeleteCall(id, _callback);

    }

    /**
     * Delete contact
     * Soft delete a contact (sets is_deleted to true)
     * @param id  (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Contact deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact not found </td><td>  -  </td></tr>
     </table>
     */
    public void contactsIdDelete(UUID id) throws ApiException {
        contactsIdDeleteWithHttpInfo(id);
    }

    /**
     * Delete contact
     * Soft delete a contact (sets is_deleted to true)
     * @param id  (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Contact deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> contactsIdDeleteWithHttpInfo(UUID id) throws ApiException {
        okhttp3.Call localVarCall = contactsIdDeleteValidateBeforeCall(id, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Delete contact (asynchronously)
     * Soft delete a contact (sets is_deleted to true)
     * @param id  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Contact deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call contactsIdDeleteAsync(UUID id, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = contactsIdDeleteValidateBeforeCall(id, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for contactsIdGet
     * @param id  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Contact details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call contactsIdGetCall(UUID id, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/contacts/{id}"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call contactsIdGetValidateBeforeCall(UUID id, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling contactsIdGet(Async)");
        }

        return contactsIdGetCall(id, _callback);

    }

    /**
     * Get contact
     * Retrieve a specific contact by ID
     * @param id  (required)
     * @return ContactDetails
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Contact details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact not found </td><td>  -  </td></tr>
     </table>
     */
    public ContactDetails contactsIdGet(UUID id) throws ApiException {
        ApiResponse<ContactDetails> localVarResp = contactsIdGetWithHttpInfo(id);
        return localVarResp.getData();
    }

    /**
     * Get contact
     * Retrieve a specific contact by ID
     * @param id  (required)
     * @return ApiResponse&lt;ContactDetails&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Contact details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ContactDetails> contactsIdGetWithHttpInfo(UUID id) throws ApiException {
        okhttp3.Call localVarCall = contactsIdGetValidateBeforeCall(id, null);
        Type localVarReturnType = new TypeToken<ContactDetails>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get contact (asynchronously)
     * Retrieve a specific contact by ID
     * @param id  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Contact details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call contactsIdGetAsync(UUID id, final ApiCallback<ContactDetails> _callback) throws ApiException {

        okhttp3.Call localVarCall = contactsIdGetValidateBeforeCall(id, _callback);
        Type localVarReturnType = new TypeToken<ContactDetails>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for contactsIdPut
     * @param id  (required)
     * @param contactUpdate  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Contact updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call contactsIdPutCall(UUID id, ContactUpdate contactUpdate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = contactUpdate;

        // create path and map variables
        String localVarPath = "/contacts/{id}"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call contactsIdPutValidateBeforeCall(UUID id, ContactUpdate contactUpdate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling contactsIdPut(Async)");
        }

        // verify the required parameter 'contactUpdate' is set
        if (contactUpdate == null) {
            throw new ApiException("Missing the required parameter 'contactUpdate' when calling contactsIdPut(Async)");
        }

        return contactsIdPutCall(id, contactUpdate, _callback);

    }

    /**
     * Update contact
     * Update an existing contact
     * @param id  (required)
     * @param contactUpdate  (required)
     * @return Contact
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Contact updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact not found </td><td>  -  </td></tr>
     </table>
     */
    public Contact contactsIdPut(UUID id, ContactUpdate contactUpdate) throws ApiException {
        ApiResponse<Contact> localVarResp = contactsIdPutWithHttpInfo(id, contactUpdate);
        return localVarResp.getData();
    }

    /**
     * Update contact
     * Update an existing contact
     * @param id  (required)
     * @param contactUpdate  (required)
     * @return ApiResponse&lt;Contact&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Contact updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Contact> contactsIdPutWithHttpInfo(UUID id, ContactUpdate contactUpdate) throws ApiException {
        okhttp3.Call localVarCall = contactsIdPutValidateBeforeCall(id, contactUpdate, null);
        Type localVarReturnType = new TypeToken<Contact>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Update contact (asynchronously)
     * Update an existing contact
     * @param id  (required)
     * @param contactUpdate  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Contact updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Contact not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call contactsIdPutAsync(UUID id, ContactUpdate contactUpdate, final ApiCallback<Contact> _callback) throws ApiException {

        okhttp3.Call localVarCall = contactsIdPutValidateBeforeCall(id, contactUpdate, _callback);
        Type localVarReturnType = new TypeToken<Contact>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for contactsPost
     * @param contactCreate  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Contact created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call contactsPostCall(ContactCreate contactCreate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = contactCreate;

        // create path and map variables
        String localVarPath = "/contacts";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call contactsPostValidateBeforeCall(ContactCreate contactCreate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'contactCreate' is set
        if (contactCreate == null) {
            throw new ApiException("Missing the required parameter 'contactCreate' when calling contactsPost(Async)");
        }

        return contactsPostCall(contactCreate, _callback);

    }

    /**
     * Create contact
     * Create a new contact
     * @param contactCreate  (required)
     * @return Contact
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Contact created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public Contact contactsPost(ContactCreate contactCreate) throws ApiException {
        ApiResponse<Contact> localVarResp = contactsPostWithHttpInfo(contactCreate);
        return localVarResp.getData();
    }

    /**
     * Create contact
     * Create a new contact
     * @param contactCreate  (required)
     * @return ApiResponse&lt;Contact&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Contact created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Contact> contactsPostWithHttpInfo(ContactCreate contactCreate) throws ApiException {
        okhttp3.Call localVarCall = contactsPostValidateBeforeCall(contactCreate, null);
        Type localVarReturnType = new TypeToken<Contact>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Create contact (asynchronously)
     * Create a new contact
     * @param contactCreate  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Contact created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call contactsPostAsync(ContactCreate contactCreate, final ApiCallback<Contact> _callback) throws ApiException {

        okhttp3.Call localVarCall = contactsPostValidateBeforeCall(contactCreate, _callback);
        Type localVarReturnType = new TypeToken<Contact>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
