/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
)

// checks if the OAuthRedirectResponse type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &OAuthRedirectResponse{}

// OAuthRedirectResponse struct for OAuthRedirectResponse
type OAuthRedirectResponse struct {
	Url *string `json:"url,omitempty"`
}

// NewOAuthRedirectResponse instantiates a new OAuthRedirectResponse object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewOAuthRedirectResponse() *OAuthRedirectResponse {
	this := OAuthRedirectResponse{}
	return &this
}

// NewOAuthRedirectResponseWithDefaults instantiates a new OAuthRedirectResponse object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewOAuthRedirectResponseWithDefaults() *OAuthRedirectResponse {
	this := OAuthRedirectResponse{}
	return &this
}

// GetUrl returns the Url field value if set, zero value otherwise.
func (o *OAuthRedirectResponse) GetUrl() string {
	if o == nil || IsNil(o.Url) {
		var ret string
		return ret
	}
	return *o.Url
}

// GetUrlOk returns a tuple with the Url field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *OAuthRedirectResponse) GetUrlOk() (*string, bool) {
	if o == nil || IsNil(o.Url) {
		return nil, false
	}
	return o.Url, true
}

// HasUrl returns a boolean if a field has been set.
func (o *OAuthRedirectResponse) HasUrl() bool {
	if o != nil && !IsNil(o.Url) {
		return true
	}

	return false
}

// SetUrl gets a reference to the given string and assigns it to the Url field.
func (o *OAuthRedirectResponse) SetUrl(v string) {
	o.Url = &v
}

func (o OAuthRedirectResponse) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o OAuthRedirectResponse) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Url) {
		toSerialize["url"] = o.Url
	}
	return toSerialize, nil
}

type NullableOAuthRedirectResponse struct {
	value *OAuthRedirectResponse
	isSet bool
}

func (v NullableOAuthRedirectResponse) Get() *OAuthRedirectResponse {
	return v.value
}

func (v *NullableOAuthRedirectResponse) Set(val *OAuthRedirectResponse) {
	v.value = val
	v.isSet = true
}

func (v NullableOAuthRedirectResponse) IsSet() bool {
	return v.isSet
}

func (v *NullableOAuthRedirectResponse) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableOAuthRedirectResponse(val *OAuthRedirectResponse) *NullableOAuthRedirectResponse {
	return &NullableOAuthRedirectResponse{value: val, isSet: true}
}

func (v NullableOAuthRedirectResponse) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableOAuthRedirectResponse) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


