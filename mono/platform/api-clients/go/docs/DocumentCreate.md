# DocumentCreate

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Content** | **string** |  | 
**Metadata** | Pointer to **map[string]interface{}** |  | [optional] 
**FilterTags** | Pointer to **[]string** |  | [optional] 
**SourceId** | Pointer to **string** |  | [optional] 

## Methods

### NewDocumentCreate

`func NewDocumentCreate(content string, ) *DocumentCreate`

NewDocumentCreate instantiates a new DocumentCreate object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDocumentCreateWithDefaults

`func NewDocumentCreateWithDefaults() *DocumentCreate`

NewDocumentCreateWithDefaults instantiates a new DocumentCreate object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetContent

`func (o *DocumentCreate) GetContent() string`

GetContent returns the Content field if non-nil, zero value otherwise.

### GetContentOk

`func (o *DocumentCreate) GetContentOk() (*string, bool)`

GetContentOk returns a tuple with the Content field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetContent

`func (o *DocumentCreate) SetContent(v string)`

SetContent sets Content field to given value.


### GetMetadata

`func (o *DocumentCreate) GetMetadata() map[string]interface{}`

GetMetadata returns the Metadata field if non-nil, zero value otherwise.

### GetMetadataOk

`func (o *DocumentCreate) GetMetadataOk() (*map[string]interface{}, bool)`

GetMetadataOk returns a tuple with the Metadata field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMetadata

`func (o *DocumentCreate) SetMetadata(v map[string]interface{})`

SetMetadata sets Metadata field to given value.

### HasMetadata

`func (o *DocumentCreate) HasMetadata() bool`

HasMetadata returns a boolean if a field has been set.

### GetFilterTags

`func (o *DocumentCreate) GetFilterTags() []string`

GetFilterTags returns the FilterTags field if non-nil, zero value otherwise.

### GetFilterTagsOk

`func (o *DocumentCreate) GetFilterTagsOk() (*[]string, bool)`

GetFilterTagsOk returns a tuple with the FilterTags field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFilterTags

`func (o *DocumentCreate) SetFilterTags(v []string)`

SetFilterTags sets FilterTags field to given value.

### HasFilterTags

`func (o *DocumentCreate) HasFilterTags() bool`

HasFilterTags returns a boolean if a field has been set.

### GetSourceId

`func (o *DocumentCreate) GetSourceId() string`

GetSourceId returns the SourceId field if non-nil, zero value otherwise.

### GetSourceIdOk

`func (o *DocumentCreate) GetSourceIdOk() (*string, bool)`

GetSourceIdOk returns a tuple with the SourceId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSourceId

`func (o *DocumentCreate) SetSourceId(v string)`

SetSourceId sets SourceId field to given value.

### HasSourceId

`func (o *DocumentCreate) HasSourceId() bool`

HasSourceId returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


