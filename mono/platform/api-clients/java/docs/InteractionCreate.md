

# InteractionCreate


## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**companyId** | **UUID** |  |  [optional] |
|**contactId** | **UUID** |  |  [optional] |
|**interactionType** | [**InteractionTypeEnum**](#InteractionTypeEnum) |  |  |
|**notes** | **String** |  |  |
|**interactionDatetime** | **OffsetDateTime** |  |  |



## Enum: InteractionTypeEnum

| Name | Value |
|---- | -----|
| EMAIL | &quot;email&quot; |
| PHONE | &quot;phone&quot; |
| MEETING | &quot;meeting&quot; |
| DEMO | &quot;demo&quot; |
| PROPOSAL | &quot;proposal&quot; |
| FOLLOW_UP | &quot;follow-up&quot; |
| OTHER | &quot;other&quot; |



