# InteractionsGet200Response

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Interactions** | Pointer to [**[]InteractionWithDetails**](InteractionWithDetails.md) |  | [optional] 
**TotalCount** | Pointer to **int32** |  | [optional] 
**HasMore** | Pointer to **bool** |  | [optional] 

## Methods

### NewInteractionsGet200Response

`func NewInteractionsGet200Response() *InteractionsGet200Response`

NewInteractionsGet200Response instantiates a new InteractionsGet200Response object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewInteractionsGet200ResponseWithDefaults

`func NewInteractionsGet200ResponseWithDefaults() *InteractionsGet200Response`

NewInteractionsGet200ResponseWithDefaults instantiates a new InteractionsGet200Response object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetInteractions

`func (o *InteractionsGet200Response) GetInteractions() []InteractionWithDetails`

GetInteractions returns the Interactions field if non-nil, zero value otherwise.

### GetInteractionsOk

`func (o *InteractionsGet200Response) GetInteractionsOk() (*[]InteractionWithDetails, bool)`

GetInteractionsOk returns a tuple with the Interactions field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetInteractions

`func (o *InteractionsGet200Response) SetInteractions(v []InteractionWithDetails)`

SetInteractions sets Interactions field to given value.

### HasInteractions

`func (o *InteractionsGet200Response) HasInteractions() bool`

HasInteractions returns a boolean if a field has been set.

### GetTotalCount

`func (o *InteractionsGet200Response) GetTotalCount() int32`

GetTotalCount returns the TotalCount field if non-nil, zero value otherwise.

### GetTotalCountOk

`func (o *InteractionsGet200Response) GetTotalCountOk() (*int32, bool)`

GetTotalCountOk returns a tuple with the TotalCount field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTotalCount

`func (o *InteractionsGet200Response) SetTotalCount(v int32)`

SetTotalCount sets TotalCount field to given value.

### HasTotalCount

`func (o *InteractionsGet200Response) HasTotalCount() bool`

HasTotalCount returns a boolean if a field has been set.

### GetHasMore

`func (o *InteractionsGet200Response) GetHasMore() bool`

GetHasMore returns the HasMore field if non-nil, zero value otherwise.

### GetHasMoreOk

`func (o *InteractionsGet200Response) GetHasMoreOk() (*bool, bool)`

GetHasMoreOk returns a tuple with the HasMore field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetHasMore

`func (o *InteractionsGet200Response) SetHasMore(v bool)`

SetHasMore sets HasMore field to given value.

### HasHasMore

`func (o *InteractionsGet200Response) HasHasMore() bool`

HasHasMore returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


