/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.api;

import org.openapitools.client.ApiException;
import org.openapitools.client.model.Deal;
import org.openapitools.client.model.DealCreate;
import org.openapitools.client.model.DealDetails;
import org.openapitools.client.model.DealStage;
import org.openapitools.client.model.DealUpdate;
import org.openapitools.client.model.DealWithDetails;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for DealsApi
 */
@Disabled
public class DealsApiTest {

    private final DealsApi api = new DealsApi();

    /**
     * List deal stages
     *
     * Retrieve all deal stages ordered by pipeline order
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void dealStagesGetTest() throws ApiException {
        List<DealStage> response = api.dealStagesGet();
        // TODO: test validations
    }

    /**
     * List deals
     *
     * Retrieve a list of all deals with company and stage information
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void dealsGetTest() throws ApiException {
        UUID stageId = null;
        UUID companyId = null;
        List<DealWithDetails> response = api.dealsGet(stageId, companyId);
        // TODO: test validations
    }

    /**
     * Delete deal
     *
     * Delete a deal
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void dealsIdDeleteTest() throws ApiException {
        UUID id = null;
        api.dealsIdDelete(id);
        // TODO: test validations
    }

    /**
     * Get deal
     *
     * Retrieve a specific deal by ID
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void dealsIdGetTest() throws ApiException {
        UUID id = null;
        DealDetails response = api.dealsIdGet(id);
        // TODO: test validations
    }

    /**
     * Update deal
     *
     * Update an existing deal
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void dealsIdPutTest() throws ApiException {
        UUID id = null;
        DealUpdate dealUpdate = null;
        Deal response = api.dealsIdPut(id, dealUpdate);
        // TODO: test validations
    }

    /**
     * Create deal
     *
     * Create a new deal
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void dealsPostTest() throws ApiException {
        DealCreate dealCreate = null;
        Deal response = api.dealsPost(dealCreate);
        // TODO: test validations
    }

}
