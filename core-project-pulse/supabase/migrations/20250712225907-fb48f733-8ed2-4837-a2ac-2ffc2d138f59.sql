-- Create team_members table
CREATE TABLE public.team_members (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  role TEXT,
  department TEXT,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;

-- Create policies for team_members
CREATE POLICY "Team members are viewable by everyone" 
ON public.team_members 
FOR SELECT 
USING (true);

CREATE POLICY "Team members can be inserted by authenticated users" 
ON public.team_members 
FOR INSERT 
WITH CHECK (true);

CREATE POLICY "Team members can be updated by authenticated users" 
ON public.team_members 
FOR UPDATE 
USING (true);

CREATE POLICY "Team members can be deleted by authenticated users" 
ON public.team_members 
FOR DELETE 
USING (true);

-- Add foreign key columns to projects table
ALTER TABLE public.projects 
ADD COLUMN project_lead_id UUID REFERENCES public.team_members(id),
ADD COLUMN customer_lead_id UUID REFERENCES public.team_members(id);

-- Add foreign key columns to tasks table
ALTER TABLE public.tasks 
ADD COLUMN assignee_id UUID REFERENCES public.team_members(id);

-- Add foreign key columns to sub_tasks table
ALTER TABLE public.sub_tasks 
ADD COLUMN assignee_id UUID REFERENCES public.team_members(id);

-- Create trigger for automatic timestamp updates on team_members
CREATE TRIGGER update_team_members_updated_at
BEFORE UPDATE ON public.team_members
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX idx_team_members_email ON public.team_members(email);
CREATE INDEX idx_team_members_active ON public.team_members(is_active);
CREATE INDEX idx_projects_project_lead_id ON public.projects(project_lead_id);
CREATE INDEX idx_projects_customer_lead_id ON public.projects(customer_lead_id);
CREATE INDEX idx_tasks_assignee_id ON public.tasks(assignee_id);
CREATE INDEX idx_sub_tasks_assignee_id ON public.sub_tasks(assignee_id);

-- Insert seed data for existing team members (from TEAM_MEMBERS constant)
INSERT INTO public.team_members (name, email, role) VALUES
('John Smith', '<EMAIL>', 'Senior Developer'),
('Sarah Johnson', '<EMAIL>', 'Project Manager'),
('Mike Davis', '<EMAIL>', 'Developer'),
('Emily Chen', '<EMAIL>', 'Designer'),
('David Wilson', '<EMAIL>', 'Tech Lead'),
('Lisa Anderson', '<EMAIL>', 'QA Engineer');