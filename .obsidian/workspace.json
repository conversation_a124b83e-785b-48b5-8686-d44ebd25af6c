{"main": {"id": "38c984ecd137fe85", "type": "split", "children": [{"id": "4d839117ca68153f", "type": "tabs", "children": [{"id": "c3b3a620acdeda2c", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mono/docs/BAZEL_GUIDE.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "BAZEL_GUIDE"}}]}], "direction": "vertical"}, "left": {"id": "2ec0786b84aaa29d", "type": "split", "children": [{"id": "e1a7c0d07fcebc22", "type": "tabs", "children": [{"id": "8ce528aed50d231d", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "16bdc7865ff06ace", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "ca19a4369a9cae7b", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "c30ec40850d8ab73", "type": "split", "children": [{"id": "2dc4dc79d0fa7ad0", "type": "tabs", "children": [{"id": "2f75f99322335ad8", "type": "leaf", "state": {"type": "backlink", "state": {"file": "mono/MONOREPO_REORGANIZATION.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for MONOREPO_REORGANIZATION"}}, {"id": "1abbc22ad373324b", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "mono/MONOREPO_REORGANIZATION.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from MONOREPO_REORGANIZATION"}}, {"id": "11ed7072e4608442", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "7d1a9d83d4ced364", "type": "leaf", "state": {"type": "outline", "state": {"file": "mono/MONOREPO_REORGANIZATION.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of MONOREPO_REORGANIZATION"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "c3b3a620acdeda2c", "lastOpenFiles": ["unified-project-hub/frontend/postcss.config.js", "unified-project-hub/frontend/tailwind.config.js", "unified-project-hub/frontend/src/components/ui/tooltip.tsx", "unified-project-hub/frontend/src/components/ui/sonner.tsx", "unified-project-hub/frontend/src/components/ui/toaster.tsx", "unified-project-hub/frontend/src/components/layout/UnifiedNavigation.tsx", "unified-project-hub/frontend/src/pages/NotFoundPage.tsx", "unified-project-hub/frontend/src/pages/profile/ProfilePage.tsx", "unified-project-hub/frontend/src/pages/profile", "unified-project-hub/frontend/src/pages/settings/SettingsPage.tsx", "unified-project-hub/frontend/src/pages/settings", "unified-project-hub/README.md", "merged-ui-analysis.md", "core-project-pulse/public/lovable-uploads/d0fa832a-0c8f-413b-a7f0-c088cca3f404.png", "core-project-pulse/public/placeholder.svg", "core-project-pulse/README.md", "project-brief-buddy/node_modules/lovable-tagger/node_modules/@esbuild/darwin-arm64/README.md", "project-brief-buddy/node_modules/cmdk/node_modules/@radix-ui/react-slot/README.md", "project-brief-buddy/node_modules/@eslint-community/eslint-utils/README.md", "project-brief-buddy/node_modules/@radix-ui/react-collection/node_modules/@radix-ui/react-context/README.md", "project-brief-buddy/node_modules/@radix-ui/react-popper/node_modules/@radix-ui/react-context/README.md", "project-brief-buddy/node_modules/@radix-ui/react-toggle-group/node_modules/@radix-ui/react-context/README.md", "project-brief-buddy/node_modules/@radix-ui/react-progress/node_modules/@radix-ui/react-context/README.md", "project-brief-buddy/node_modules/cmdk/node_modules/@radix-ui/react-compose-refs/README.md", "project-brief-buddy/node_modules/cmdk/node_modules/@radix-ui/react-focus-scope/README.md", "project-brief-buddy/node_modules/@esbuild/darwin-arm64/README.md", "project-brief-buddy/node_modules/@radix-ui/react-roving-focus/node_modules/@radix-ui/react-context/README.md", "project-brief-buddy/node_modules/@tailwindcss/typography/node_modules/postcss-selector-parser/README.md", "project-brief-buddy/node_modules/@tailwindcss/typography/node_modules/postcss-selector-parser/CHANGELOG.md", "project-brief-buddy/node_modules/@tailwindcss/typography/node_modules/postcss-selector-parser/API.md", "project-brief-buddy/node_modules/cmdk/node_modules/@radix-ui/primitive/README.md", "project-brief-buddy/node_modules/cmdk/node_modules/@radix-ui/react-context/README.md", "project-brief-buddy/node_modules/cmdk/node_modules/@radix-ui/react-dismissable-layer/README.md", "project-brief-buddy/node_modules/cmdk/node_modules/@radix-ui/react-dialog/README.md", "project-brief-buddy/node_modules/@alloc/quick-lru/readme.md", "project-brief-buddy/node_modules/cmdk/node_modules/@radix-ui/react-portal/README.md", "project-brief-buddy/node_modules/cmdk/node_modules/@radix-ui/react-use-controllable-state/README.md", "project-brief-buddy/node_modules/@typescript-eslint/typescript-estree/node_modules/brace-expansion/README.md", "project-brief-buddy/node_modules/date-fns/docs/logotype.svg", "project-brief-buddy/node_modules/date-fns/docs/logo.svg", "project-brief-buddy/public/placeholder.svg"]}