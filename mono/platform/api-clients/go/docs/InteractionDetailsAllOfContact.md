# InteractionDetailsAllOfContact

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Id** | Pointer to **string** |  | [optional] 
**FirstName** | Pointer to **string** |  | [optional] 
**LastName** | Pointer to **string** |  | [optional] 
**Email** | Pointer to **string** |  | [optional] 
**Phone** | Pointer to **string** |  | [optional] 
**JobTitle** | Pointer to **string** |  | [optional] 

## Methods

### NewInteractionDetailsAllOfContact

`func NewInteractionDetailsAllOfContact() *InteractionDetailsAllOfContact`

NewInteractionDetailsAllOfContact instantiates a new InteractionDetailsAllOfContact object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewInteractionDetailsAllOfContactWithDefaults

`func NewInteractionDetailsAllOfContactWithDefaults() *InteractionDetailsAllOfContact`

NewInteractionDetailsAllOfContactWithDefaults instantiates a new InteractionDetailsAllOfContact object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetId

`func (o *InteractionDetailsAllOfContact) GetId() string`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *InteractionDetailsAllOfContact) GetIdOk() (*string, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *InteractionDetailsAllOfContact) SetId(v string)`

SetId sets Id field to given value.

### HasId

`func (o *InteractionDetailsAllOfContact) HasId() bool`

HasId returns a boolean if a field has been set.

### GetFirstName

`func (o *InteractionDetailsAllOfContact) GetFirstName() string`

GetFirstName returns the FirstName field if non-nil, zero value otherwise.

### GetFirstNameOk

`func (o *InteractionDetailsAllOfContact) GetFirstNameOk() (*string, bool)`

GetFirstNameOk returns a tuple with the FirstName field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFirstName

`func (o *InteractionDetailsAllOfContact) SetFirstName(v string)`

SetFirstName sets FirstName field to given value.

### HasFirstName

`func (o *InteractionDetailsAllOfContact) HasFirstName() bool`

HasFirstName returns a boolean if a field has been set.

### GetLastName

`func (o *InteractionDetailsAllOfContact) GetLastName() string`

GetLastName returns the LastName field if non-nil, zero value otherwise.

### GetLastNameOk

`func (o *InteractionDetailsAllOfContact) GetLastNameOk() (*string, bool)`

GetLastNameOk returns a tuple with the LastName field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetLastName

`func (o *InteractionDetailsAllOfContact) SetLastName(v string)`

SetLastName sets LastName field to given value.

### HasLastName

`func (o *InteractionDetailsAllOfContact) HasLastName() bool`

HasLastName returns a boolean if a field has been set.

### GetEmail

`func (o *InteractionDetailsAllOfContact) GetEmail() string`

GetEmail returns the Email field if non-nil, zero value otherwise.

### GetEmailOk

`func (o *InteractionDetailsAllOfContact) GetEmailOk() (*string, bool)`

GetEmailOk returns a tuple with the Email field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetEmail

`func (o *InteractionDetailsAllOfContact) SetEmail(v string)`

SetEmail sets Email field to given value.

### HasEmail

`func (o *InteractionDetailsAllOfContact) HasEmail() bool`

HasEmail returns a boolean if a field has been set.

### GetPhone

`func (o *InteractionDetailsAllOfContact) GetPhone() string`

GetPhone returns the Phone field if non-nil, zero value otherwise.

### GetPhoneOk

`func (o *InteractionDetailsAllOfContact) GetPhoneOk() (*string, bool)`

GetPhoneOk returns a tuple with the Phone field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPhone

`func (o *InteractionDetailsAllOfContact) SetPhone(v string)`

SetPhone sets Phone field to given value.

### HasPhone

`func (o *InteractionDetailsAllOfContact) HasPhone() bool`

HasPhone returns a boolean if a field has been set.

### GetJobTitle

`func (o *InteractionDetailsAllOfContact) GetJobTitle() string`

GetJobTitle returns the JobTitle field if non-nil, zero value otherwise.

### GetJobTitleOk

`func (o *InteractionDetailsAllOfContact) GetJobTitleOk() (*string, bool)`

GetJobTitleOk returns a tuple with the JobTitle field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetJobTitle

`func (o *InteractionDetailsAllOfContact) SetJobTitle(v string)`

SetJobTitle sets JobTitle field to given value.

### HasJobTitle

`func (o *InteractionDetailsAllOfContact) HasJobTitle() bool`

HasJobTitle returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


