{"level":"info","message":"🚀 Server running on http://localhost:8000","service":"Unified Project Hub","timestamp":"2025-07-17 16:27:29","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8000/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 16:27:29","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 16:27:29","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 16:28:39","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 16:28:39","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 16:28:39","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-17 18:14:17","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-17 18:14:24","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 18:14:32","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 18:14:32","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 18:14:32","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /","service":"Unified Project Hub","timestamp":"2025-07-17 18:15:35","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /favicon.ico","service":"Unified Project Hub","timestamp":"2025-07-17 18:15:35","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-17 18:17:40","version":"1.0.0"}
