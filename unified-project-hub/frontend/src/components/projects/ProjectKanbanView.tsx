import React from 'react';
import { ProjectCard } from './ProjectCard';
import { Badge } from '@/components/ui/badge';

interface ProjectKanbanViewProps {
  projects: any[];
  onEdit?: (project: any) => void;
  onArchive?: (project: any) => void;
  onDelete?: (project: any) => void;
  showActions?: boolean;
}

const KANBAN_COLUMNS = [
  { 
    id: 'draft', 
    title: 'Draft', 
    statuses: ['draft'],
    color: 'bg-gray-100'
  },
  { 
    id: 'requirements', 
    title: 'Requirements', 
    statuses: ['requirements'],
    color: 'bg-blue-100'
  },
  { 
    id: 'backlog', 
    title: 'Backlog', 
    statuses: ['backlog'],
    color: 'bg-purple-100'
  },
  { 
    id: 'not_started', 
    title: 'Not Started', 
    statuses: ['not_started'],
    color: 'bg-yellow-100'
  },
  { 
    id: 'in_progress', 
    title: 'In Progress', 
    statuses: ['in_progress'],
    color: 'bg-orange-100'
  },
  { 
    id: 'completed', 
    title: 'Completed', 
    statuses: ['completed'],
    color: 'bg-green-100'
  },
  { 
    id: 'on_hold', 
    title: 'On Hold', 
    statuses: ['on_hold'],
    color: 'bg-red-100'
  }
];

export function ProjectKanbanView({
  projects,
  onEdit,
  onArchive,
  onDelete,
  showActions = true
}: ProjectKanbanViewProps) {
  // Group projects by status
  const groupedProjects = KANBAN_COLUMNS.reduce((acc, column) => {
    acc[column.id] = projects.filter(project => 
      column.statuses.includes(project.status)
    );
    return acc;
  }, {} as Record<string, any[]>);

  return (
    <div className="flex gap-6 overflow-x-auto pb-4">
      {KANBAN_COLUMNS.map((column) => {
        const columnProjects = groupedProjects[column.id] || [];
        
        return (
          <div key={column.id} className="flex-shrink-0 w-80">
            <div className={`rounded-lg p-4 ${column.color}`}>
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900">{column.title}</h3>
                <Badge variant="secondary" className="bg-white">
                  {columnProjects.length}
                </Badge>
              </div>
              
              <div className="space-y-4 max-h-[calc(100vh-300px)] overflow-y-auto">
                {columnProjects.map((project) => (
                  <div key={project.id} className="bg-white rounded-lg shadow-sm">
                    <ProjectCard
                      project={project}
                      onEdit={onEdit}
                      onArchive={onArchive}
                      onDelete={onDelete}
                      showActions={showActions}
                    />
                  </div>
                ))}
                
                {columnProjects.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <p className="text-sm">No projects in {column.title.toLowerCase()}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
