import { Outlet } from 'react-router-dom'
import { UnifiedNavigation } from './UnifiedNavigation'
import { useAuth } from '@/contexts/AuthContext'

export function Layout() {
  const { user } = useAuth()

  const handleCreateProject = () => {
    // TODO: Implement create project modal
    console.log('Create project clicked')
  }

  const handleSearch = (query: string) => {
    // TODO: Implement search functionality
    console.log('Search query:', query)
  }

  return (
    <div className="min-h-screen bg-background">
      <UnifiedNavigation
        currentUser={user || undefined}
        onCreateProject={handleCreateProject}
        onSearch={handleSearch}
      />
      <main className="container mx-auto px-4 py-6">
        <Outlet />
      </main>
    </div>
  )
}
