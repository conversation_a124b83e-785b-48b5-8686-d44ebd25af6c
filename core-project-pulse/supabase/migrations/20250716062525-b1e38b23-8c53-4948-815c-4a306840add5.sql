-- Create function to convert a project to a task under another project
CREATE OR REPLACE FUNCTION public.convert_project_to_task(
  source_project_id UUID,
  target_project_id UUID
) 
RETURNS UUID
LANGUAGE plpgsql
AS $$
DECLARE
  source_project RECORD;
  new_task_id UUID;
  existing_task RECORD;
  existing_subtask RECORD;
BEGIN
  -- Get the source project details
  SELECT * INTO source_project 
  FROM public.projects 
  WHERE id = source_project_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Source project not found';
  END IF;
  
  -- Verify target project exists
  IF NOT EXISTS (SELECT 1 FROM public.projects WHERE id = target_project_id) THEN
    RAISE EXCEPTION 'Target project not found';
  END IF;
  
  -- Cannot convert project to itself
  IF source_project_id = target_project_id THEN
    RAISE EXCEPTION 'Cannot convert project to task under itself';
  END IF;
  
  -- Create new task from project data
  INSERT INTO public.tasks (
    project_id,
    name,
    description,
    assignee,
    assignee_id,
    due_date,
    status
  ) VALUES (
    target_project_id,
    source_project.name,
    source_project.description,
    source_project.project_lead,
    source_project.project_lead_id,
    source_project.end_date,
    CASE 
      WHEN source_project.status = 'completed' THEN 'done'
      WHEN source_project.status = 'in-progress' THEN 'in-progress'
      ELSE 'to-do'
    END
  ) RETURNING id INTO new_task_id;
  
  -- Convert existing tasks to subtasks under the new main task
  FOR existing_task IN 
    SELECT * FROM public.tasks WHERE project_id = source_project_id
  LOOP
    INSERT INTO public.sub_tasks (
      task_id,
      name,
      description,
      assignee,
      assignee_id,
      due_date,
      status,
      completed_at
    ) VALUES (
      new_task_id,
      existing_task.name,
      existing_task.description,
      existing_task.assignee,
      existing_task.assignee_id,
      existing_task.due_date,
      existing_task.status,
      existing_task.completed_at
    );
    
    -- Convert existing subtasks to also be subtasks under the new main task
    FOR existing_subtask IN
      SELECT * FROM public.sub_tasks WHERE task_id = existing_task.id
    LOOP
      INSERT INTO public.sub_tasks (
        task_id,
        name,
        description,
        assignee,
        assignee_id,
        due_date,
        status,
        completed_at
      ) VALUES (
        new_task_id,
        existing_subtask.name,
        existing_subtask.description,
        existing_subtask.assignee,
        existing_subtask.assignee_id,
        existing_subtask.due_date,
        existing_subtask.status,
        existing_subtask.completed_at
      );
    END LOOP;
    
    -- Delete the old subtasks
    DELETE FROM public.sub_tasks WHERE task_id = existing_task.id;
  END LOOP;
  
  -- Delete existing tasks from the source project
  DELETE FROM public.tasks WHERE project_id = source_project_id;
  
  -- Delete project integrations
  DELETE FROM public.project_integrations WHERE project_id = source_project_id;
  
  -- Delete the source project
  DELETE FROM public.projects WHERE id = source_project_id;
  
  RETURN new_task_id;
END;
$$;