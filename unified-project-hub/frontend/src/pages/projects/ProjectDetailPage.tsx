import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { formatDate, getStatusColor } from '@/lib/utils'

export default function ProjectDetailPage() {
  const { id } = useParams<{ id: string }>()
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')
  const [isLoading, setIsLoading] = useState(false)

  // Mock data - replace with actual API call
  const mockProject = {
    id: '1',
    name: 'E-commerce Platform Redesign',
    description: 'Complete overhaul of the existing e-commerce platform with modern UI/UX and improved performance',
    status: 'in-progress',
    type: 'external',
    company_name: 'Acme Corporation',
    progress_percentage: 65,
    priority_level: 'P1',
    impact_type: 'Revenue',
    start_date: '2024-01-15',
    end_date: '2024-06-30',
    budget: 150000,
    actual_cost: 95000,
    project_lead: { 
      id: '1', 
      name: '<PERSON>', 
      email: '<EMAIL>',
      avatar_url: null,
      title: 'Senior Project Manager'
    },
    team_members: [
      { user: { id: '2', name: '<PERSON>', email: '<EMAIL>', avatar_url: null, title: 'Senior Developer' } },
      { user: { id: '3', name: 'Carol Davis', email: '<EMAIL>', avatar_url: null, title: 'Frontend Developer' } },
    ],
    tasks: [
      {
        id: '1',
        name: 'Implement OAuth Authentication',
        status: 'in-progress',
        priority_level: 'P1',
        assignee: { name: 'Bob Smith', avatar_url: null },
        due_date: '2024-02-15',
        is_customer_visible: true,
      },
      {
        id: '2',
        name: 'Design Login/Register UI',
        status: 'done',
        priority_level: 'P2',
        assignee: { name: 'Carol Davis', avatar_url: null },
        due_date: '2024-02-01',
        is_customer_visible: true,
      },
    ],
    requirements: [
      {
        id: '1',
        title: 'User Authentication System',
        status: 'approved',
        priority_level: 'P1',
        created_by: { name: 'David Wilson' },
      },
    ],
    interactions: [
      {
        id: '1',
        type: 'meeting',
        subject: 'Project Kickoff Meeting',
        content: 'Discussed project scope, timeline, and deliverables.',
        created_by: { name: 'Alice Johnson' },
        interaction_date: '2024-01-15',
      },
    ],
  }

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'tasks', label: 'Tasks' },
    { id: 'requirements', label: 'Requirements' },
    { id: 'team', label: 'Team' },
    { id: 'activity', label: 'Activity' },
  ]

  const canEdit = user?.role === 'admin' || 
                 user?.role === 'project_manager' || 
                 user?.id === mockProject.project_lead.id

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <nav className="flex" aria-label="Breadcrumb">
        <ol className="flex items-center space-x-4">
          <li>
            <Link to="/projects" className="text-gray-400 hover:text-gray-500">
              Projects
            </Link>
          </li>
          <li>
            <div className="flex items-center">
              <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              <span className="ml-4 text-sm font-medium text-gray-500">{mockProject.name}</span>
            </div>
          </li>
        </ol>
      </nav>

      {/* Header */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-2xl font-bold text-gray-900">{mockProject.name}</h1>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(mockProject.status)}`}>
                  {mockProject.status.replace('-', ' ')}
                </span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(mockProject.priority_level)}`}>
                  {mockProject.priority_level}
                </span>
              </div>
              <p className="text-gray-600 mb-4">{mockProject.description}</p>
              <div className="flex items-center space-x-6 text-sm text-gray-500">
                <span>Company: {mockProject.company_name}</span>
                <span>Type: {mockProject.type}</span>
                <span>Impact: {mockProject.impact_type}</span>
              </div>
            </div>
            {canEdit && (
              <button className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/90 transition-colors">
                Edit Project
              </button>
            )}
          </div>

          {/* Progress Bar */}
          <div className="mt-6">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Overall Progress</span>
              <span>{mockProject.progress_percentage}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-primary h-3 rounded-full transition-all duration-300"
                style={{ width: `${mockProject.progress_percentage}%` }}
              />
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-t border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow">
        {activeTab === 'overview' && (
          <div className="p-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Project Details */}
              <div className="lg:col-span-2 space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Project Details</h3>
                  <dl className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Start Date</dt>
                      <dd className="text-sm text-gray-900">{formatDate(mockProject.start_date)}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">End Date</dt>
                      <dd className="text-sm text-gray-900">{formatDate(mockProject.end_date)}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Budget</dt>
                      <dd className="text-sm text-gray-900">${mockProject.budget?.toLocaleString()}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Actual Cost</dt>
                      <dd className="text-sm text-gray-900">${mockProject.actual_cost?.toLocaleString()}</dd>
                    </div>
                  </dl>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Tasks</h3>
                  <div className="space-y-3">
                    {mockProject.tasks.slice(0, 3).map((task) => (
                      <div key={task.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={task.status === 'done'}
                            readOnly
                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                          />
                          <div>
                            <p className="text-sm font-medium text-gray-900">{task.name}</p>
                            <p className="text-xs text-gray-500">
                              Assigned to {task.assignee.name} • Due {formatDate(task.due_date)}
                            </p>
                          </div>
                        </div>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                          {task.status.replace('-', ' ')}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Project Lead</h3>
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center text-sm font-medium text-gray-600">
                      {mockProject.project_lead.name.split(' ').map(n => n[0]).join('')}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{mockProject.project_lead.name}</p>
                      <p className="text-xs text-gray-500">{mockProject.project_lead.title}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Team Members</h3>
                  <div className="space-y-3">
                    {mockProject.team_members.map((member) => (
                      <div key={member.user.id} className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-xs font-medium text-gray-600">
                          {member.user.name.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">{member.user.name}</p>
                          <p className="text-xs text-gray-500">{member.user.title}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Stats</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Tasks Completed</span>
                      <span className="text-sm font-medium text-gray-900">
                        {mockProject.tasks.filter(t => t.status === 'done').length}/{mockProject.tasks.length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Requirements</span>
                      <span className="text-sm font-medium text-gray-900">{mockProject.requirements.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Team Size</span>
                      <span className="text-sm font-medium text-gray-900">{mockProject.team_members.length + 1}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'tasks' && (
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-medium text-gray-900">Project Tasks</h3>
              {canEdit && (
                <button className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/90 transition-colors">
                  Add Task
                </button>
              )}
            </div>
            <div className="space-y-4">
              {mockProject.tasks.map((task) => (
                <div key={task.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <input
                        type="checkbox"
                        checked={task.status === 'done'}
                        readOnly
                        className="mt-1 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                      />
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">{task.name}</h4>
                        <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                          <span>Assigned to {task.assignee.name}</span>
                          <span>Due {formatDate(task.due_date)}</span>
                          {task.is_customer_visible && (
                            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">Customer Visible</span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.priority_level)}`}>
                        {task.priority_level}
                      </span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                        {task.status.replace('-', ' ')}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Other tab contents would go here */}
        {activeTab !== 'overview' && activeTab !== 'tasks' && (
          <div className="p-6">
            <p className="text-gray-500">Content for {activeTab} tab coming soon...</p>
          </div>
        )}
      </div>
    </div>
  )
}
