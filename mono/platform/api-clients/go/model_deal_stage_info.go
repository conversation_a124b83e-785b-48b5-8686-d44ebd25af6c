/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
)

// checks if the DealStageInfo type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &DealStageInfo{}

// DealStageInfo struct for DealStageInfo
type DealStageInfo struct {
	Id *string `json:"id,omitempty"`
	Name *string `json:"name,omitempty"`
	PipelineOrder *int32 `json:"pipeline_order,omitempty"`
}

// NewDealStageInfo instantiates a new DealStageInfo object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDealStageInfo() *DealStageInfo {
	this := DealStageInfo{}
	return &this
}

// NewDealStageInfoWithDefaults instantiates a new DealStageInfo object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDealStageInfoWithDefaults() *DealStageInfo {
	this := DealStageInfo{}
	return &this
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *DealStageInfo) GetId() string {
	if o == nil || IsNil(o.Id) {
		var ret string
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealStageInfo) GetIdOk() (*string, bool) {
	if o == nil || IsNil(o.Id) {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *DealStageInfo) HasId() bool {
	if o != nil && !IsNil(o.Id) {
		return true
	}

	return false
}

// SetId gets a reference to the given string and assigns it to the Id field.
func (o *DealStageInfo) SetId(v string) {
	o.Id = &v
}

// GetName returns the Name field value if set, zero value otherwise.
func (o *DealStageInfo) GetName() string {
	if o == nil || IsNil(o.Name) {
		var ret string
		return ret
	}
	return *o.Name
}

// GetNameOk returns a tuple with the Name field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealStageInfo) GetNameOk() (*string, bool) {
	if o == nil || IsNil(o.Name) {
		return nil, false
	}
	return o.Name, true
}

// HasName returns a boolean if a field has been set.
func (o *DealStageInfo) HasName() bool {
	if o != nil && !IsNil(o.Name) {
		return true
	}

	return false
}

// SetName gets a reference to the given string and assigns it to the Name field.
func (o *DealStageInfo) SetName(v string) {
	o.Name = &v
}

// GetPipelineOrder returns the PipelineOrder field value if set, zero value otherwise.
func (o *DealStageInfo) GetPipelineOrder() int32 {
	if o == nil || IsNil(o.PipelineOrder) {
		var ret int32
		return ret
	}
	return *o.PipelineOrder
}

// GetPipelineOrderOk returns a tuple with the PipelineOrder field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealStageInfo) GetPipelineOrderOk() (*int32, bool) {
	if o == nil || IsNil(o.PipelineOrder) {
		return nil, false
	}
	return o.PipelineOrder, true
}

// HasPipelineOrder returns a boolean if a field has been set.
func (o *DealStageInfo) HasPipelineOrder() bool {
	if o != nil && !IsNil(o.PipelineOrder) {
		return true
	}

	return false
}

// SetPipelineOrder gets a reference to the given int32 and assigns it to the PipelineOrder field.
func (o *DealStageInfo) SetPipelineOrder(v int32) {
	o.PipelineOrder = &v
}

func (o DealStageInfo) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o DealStageInfo) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Id) {
		toSerialize["id"] = o.Id
	}
	if !IsNil(o.Name) {
		toSerialize["name"] = o.Name
	}
	if !IsNil(o.PipelineOrder) {
		toSerialize["pipeline_order"] = o.PipelineOrder
	}
	return toSerialize, nil
}

type NullableDealStageInfo struct {
	value *DealStageInfo
	isSet bool
}

func (v NullableDealStageInfo) Get() *DealStageInfo {
	return v.value
}

func (v *NullableDealStageInfo) Set(val *DealStageInfo) {
	v.value = val
	v.isSet = true
}

func (v NullableDealStageInfo) IsSet() bool {
	return v.isSet
}

func (v *NullableDealStageInfo) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDealStageInfo(val *DealStageInfo) *NullableDealStageInfo {
	return &NullableDealStageInfo{value: val, isSet: true}
}

func (v NullableDealStageInfo) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDealStageInfo) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


