# CompanyStatusUpdateRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**CompanyStatusId** | **string** |  | 

## Methods

### NewCompanyStatusUpdateRequest

`func NewCompanyStatusUpdateRequest(companyStatusId string, ) *CompanyStatusUpdateRequest`

NewCompanyStatusUpdateRequest instantiates a new CompanyStatusUpdateRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewCompanyStatusUpdateRequestWithDefaults

`func NewCompanyStatusUpdateRequestWithDefaults() *CompanyStatusUpdateRequest`

NewCompanyStatusUpdateRequestWithDefaults instantiates a new CompanyStatusUpdateRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetCompanyStatusId

`func (o *CompanyStatusUpdateRequest) GetCompanyStatusId() string`

GetCompanyStatusId returns the CompanyStatusId field if non-nil, zero value otherwise.

### GetCompanyStatusIdOk

`func (o *CompanyStatusUpdateRequest) GetCompanyStatusIdOk() (*string, bool)`

GetCompanyStatusIdOk returns a tuple with the CompanyStatusId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCompanyStatusId

`func (o *CompanyStatusUpdateRequest) SetCompanyStatusId(v string)`

SetCompanyStatusId sets CompanyStatusId field to given value.



[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


