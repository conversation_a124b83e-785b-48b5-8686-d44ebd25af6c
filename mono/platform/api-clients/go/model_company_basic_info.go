/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
)

// checks if the CompanyBasicInfo type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &CompanyBasicInfo{}

// CompanyBasicInfo struct for CompanyBasicInfo
type CompanyBasicInfo struct {
	Id *string `json:"id,omitempty"`
	Name *string `json:"name,omitempty"`
}

// NewCompanyBasicInfo instantiates a new CompanyBasicInfo object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewCompanyBasicInfo() *CompanyBasicInfo {
	this := CompanyBasicInfo{}
	return &this
}

// NewCompanyBasicInfoWithDefaults instantiates a new CompanyBasicInfo object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewCompanyBasicInfoWithDefaults() *CompanyBasicInfo {
	this := CompanyBasicInfo{}
	return &this
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *CompanyBasicInfo) GetId() string {
	if o == nil || IsNil(o.Id) {
		var ret string
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyBasicInfo) GetIdOk() (*string, bool) {
	if o == nil || IsNil(o.Id) {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *CompanyBasicInfo) HasId() bool {
	if o != nil && !IsNil(o.Id) {
		return true
	}

	return false
}

// SetId gets a reference to the given string and assigns it to the Id field.
func (o *CompanyBasicInfo) SetId(v string) {
	o.Id = &v
}

// GetName returns the Name field value if set, zero value otherwise.
func (o *CompanyBasicInfo) GetName() string {
	if o == nil || IsNil(o.Name) {
		var ret string
		return ret
	}
	return *o.Name
}

// GetNameOk returns a tuple with the Name field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyBasicInfo) GetNameOk() (*string, bool) {
	if o == nil || IsNil(o.Name) {
		return nil, false
	}
	return o.Name, true
}

// HasName returns a boolean if a field has been set.
func (o *CompanyBasicInfo) HasName() bool {
	if o != nil && !IsNil(o.Name) {
		return true
	}

	return false
}

// SetName gets a reference to the given string and assigns it to the Name field.
func (o *CompanyBasicInfo) SetName(v string) {
	o.Name = &v
}

func (o CompanyBasicInfo) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o CompanyBasicInfo) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Id) {
		toSerialize["id"] = o.Id
	}
	if !IsNil(o.Name) {
		toSerialize["name"] = o.Name
	}
	return toSerialize, nil
}

type NullableCompanyBasicInfo struct {
	value *CompanyBasicInfo
	isSet bool
}

func (v NullableCompanyBasicInfo) Get() *CompanyBasicInfo {
	return v.value
}

func (v *NullableCompanyBasicInfo) Set(val *CompanyBasicInfo) {
	v.value = val
	v.isSet = true
}

func (v NullableCompanyBasicInfo) IsSet() bool {
	return v.isSet
}

func (v *NullableCompanyBasicInfo) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableCompanyBasicInfo(val *CompanyBasicInfo) *NullableCompanyBasicInfo {
	return &NullableCompanyBasicInfo{value: val, isSet: true}
}

func (v NullableCompanyBasicInfo) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableCompanyBasicInfo) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


