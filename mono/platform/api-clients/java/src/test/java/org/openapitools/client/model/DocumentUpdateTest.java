/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

/**
 * Model tests for DocumentUpdate
 */
public class DocumentUpdateTest {
    private final DocumentUpdate model = new DocumentUpdate();

    /**
     * Model tests for DocumentUpdate
     */
    @Test
    public void testDocumentUpdate() {
        // TODO: test DocumentUpdate
    }

    /**
     * Test the property 'content'
     */
    @Test
    public void contentTest() {
        // TODO: test content
    }

    /**
     * Test the property 'metadata'
     */
    @Test
    public void metadataTest() {
        // TODO: test metadata
    }

    /**
     * Test the property 'filterTags'
     */
    @Test
    public void filterTagsTest() {
        // TODO: test filterTags
    }

    /**
     * Test the property 'sourceId'
     */
    @Test
    public void sourceIdTest() {
        // TODO: test sourceId
    }

}
