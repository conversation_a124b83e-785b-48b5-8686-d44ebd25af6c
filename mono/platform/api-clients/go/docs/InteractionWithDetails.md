# InteractionWithDetails

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Id** | Pointer to **string** |  | [optional] 
**CompanyId** | Pointer to **NullableString** |  | [optional] 
**ContactId** | Pointer to **NullableString** |  | [optional] 
**InteractionType** | Pointer to **string** |  | [optional] 
**Notes** | Pointer to **string** |  | [optional] 
**InteractionDatetime** | Pointer to **time.Time** |  | [optional] 
**CreatedBy** | Pointer to **string** |  | [optional] 
**CreatedAt** | Pointer to **time.Time** |  | [optional] 
**UpdatedAt** | Pointer to **time.Time** |  | [optional] 
**Company** | Pointer to [**NullableInteractionWithDetailsAllOfCompany**](InteractionWithDetailsAllOfCompany.md) |  | [optional] 
**Contact** | Pointer to [**NullableInteractionWithDetailsAllOfContact**](InteractionWithDetailsAllOfContact.md) |  | [optional] 

## Methods

### NewInteractionWithDetails

`func NewInteractionWithDetails() *InteractionWithDetails`

NewInteractionWithDetails instantiates a new InteractionWithDetails object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewInteractionWithDetailsWithDefaults

`func NewInteractionWithDetailsWithDefaults() *InteractionWithDetails`

NewInteractionWithDetailsWithDefaults instantiates a new InteractionWithDetails object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetId

`func (o *InteractionWithDetails) GetId() string`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *InteractionWithDetails) GetIdOk() (*string, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *InteractionWithDetails) SetId(v string)`

SetId sets Id field to given value.

### HasId

`func (o *InteractionWithDetails) HasId() bool`

HasId returns a boolean if a field has been set.

### GetCompanyId

`func (o *InteractionWithDetails) GetCompanyId() string`

GetCompanyId returns the CompanyId field if non-nil, zero value otherwise.

### GetCompanyIdOk

`func (o *InteractionWithDetails) GetCompanyIdOk() (*string, bool)`

GetCompanyIdOk returns a tuple with the CompanyId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCompanyId

`func (o *InteractionWithDetails) SetCompanyId(v string)`

SetCompanyId sets CompanyId field to given value.

### HasCompanyId

`func (o *InteractionWithDetails) HasCompanyId() bool`

HasCompanyId returns a boolean if a field has been set.

### SetCompanyIdNil

`func (o *InteractionWithDetails) SetCompanyIdNil(b bool)`

 SetCompanyIdNil sets the value for CompanyId to be an explicit nil

### UnsetCompanyId
`func (o *InteractionWithDetails) UnsetCompanyId()`

UnsetCompanyId ensures that no value is present for CompanyId, not even an explicit nil
### GetContactId

`func (o *InteractionWithDetails) GetContactId() string`

GetContactId returns the ContactId field if non-nil, zero value otherwise.

### GetContactIdOk

`func (o *InteractionWithDetails) GetContactIdOk() (*string, bool)`

GetContactIdOk returns a tuple with the ContactId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetContactId

`func (o *InteractionWithDetails) SetContactId(v string)`

SetContactId sets ContactId field to given value.

### HasContactId

`func (o *InteractionWithDetails) HasContactId() bool`

HasContactId returns a boolean if a field has been set.

### SetContactIdNil

`func (o *InteractionWithDetails) SetContactIdNil(b bool)`

 SetContactIdNil sets the value for ContactId to be an explicit nil

### UnsetContactId
`func (o *InteractionWithDetails) UnsetContactId()`

UnsetContactId ensures that no value is present for ContactId, not even an explicit nil
### GetInteractionType

`func (o *InteractionWithDetails) GetInteractionType() string`

GetInteractionType returns the InteractionType field if non-nil, zero value otherwise.

### GetInteractionTypeOk

`func (o *InteractionWithDetails) GetInteractionTypeOk() (*string, bool)`

GetInteractionTypeOk returns a tuple with the InteractionType field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetInteractionType

`func (o *InteractionWithDetails) SetInteractionType(v string)`

SetInteractionType sets InteractionType field to given value.

### HasInteractionType

`func (o *InteractionWithDetails) HasInteractionType() bool`

HasInteractionType returns a boolean if a field has been set.

### GetNotes

`func (o *InteractionWithDetails) GetNotes() string`

GetNotes returns the Notes field if non-nil, zero value otherwise.

### GetNotesOk

`func (o *InteractionWithDetails) GetNotesOk() (*string, bool)`

GetNotesOk returns a tuple with the Notes field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetNotes

`func (o *InteractionWithDetails) SetNotes(v string)`

SetNotes sets Notes field to given value.

### HasNotes

`func (o *InteractionWithDetails) HasNotes() bool`

HasNotes returns a boolean if a field has been set.

### GetInteractionDatetime

`func (o *InteractionWithDetails) GetInteractionDatetime() time.Time`

GetInteractionDatetime returns the InteractionDatetime field if non-nil, zero value otherwise.

### GetInteractionDatetimeOk

`func (o *InteractionWithDetails) GetInteractionDatetimeOk() (*time.Time, bool)`

GetInteractionDatetimeOk returns a tuple with the InteractionDatetime field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetInteractionDatetime

`func (o *InteractionWithDetails) SetInteractionDatetime(v time.Time)`

SetInteractionDatetime sets InteractionDatetime field to given value.

### HasInteractionDatetime

`func (o *InteractionWithDetails) HasInteractionDatetime() bool`

HasInteractionDatetime returns a boolean if a field has been set.

### GetCreatedBy

`func (o *InteractionWithDetails) GetCreatedBy() string`

GetCreatedBy returns the CreatedBy field if non-nil, zero value otherwise.

### GetCreatedByOk

`func (o *InteractionWithDetails) GetCreatedByOk() (*string, bool)`

GetCreatedByOk returns a tuple with the CreatedBy field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreatedBy

`func (o *InteractionWithDetails) SetCreatedBy(v string)`

SetCreatedBy sets CreatedBy field to given value.

### HasCreatedBy

`func (o *InteractionWithDetails) HasCreatedBy() bool`

HasCreatedBy returns a boolean if a field has been set.

### GetCreatedAt

`func (o *InteractionWithDetails) GetCreatedAt() time.Time`

GetCreatedAt returns the CreatedAt field if non-nil, zero value otherwise.

### GetCreatedAtOk

`func (o *InteractionWithDetails) GetCreatedAtOk() (*time.Time, bool)`

GetCreatedAtOk returns a tuple with the CreatedAt field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreatedAt

`func (o *InteractionWithDetails) SetCreatedAt(v time.Time)`

SetCreatedAt sets CreatedAt field to given value.

### HasCreatedAt

`func (o *InteractionWithDetails) HasCreatedAt() bool`

HasCreatedAt returns a boolean if a field has been set.

### GetUpdatedAt

`func (o *InteractionWithDetails) GetUpdatedAt() time.Time`

GetUpdatedAt returns the UpdatedAt field if non-nil, zero value otherwise.

### GetUpdatedAtOk

`func (o *InteractionWithDetails) GetUpdatedAtOk() (*time.Time, bool)`

GetUpdatedAtOk returns a tuple with the UpdatedAt field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUpdatedAt

`func (o *InteractionWithDetails) SetUpdatedAt(v time.Time)`

SetUpdatedAt sets UpdatedAt field to given value.

### HasUpdatedAt

`func (o *InteractionWithDetails) HasUpdatedAt() bool`

HasUpdatedAt returns a boolean if a field has been set.

### GetCompany

`func (o *InteractionWithDetails) GetCompany() InteractionWithDetailsAllOfCompany`

GetCompany returns the Company field if non-nil, zero value otherwise.

### GetCompanyOk

`func (o *InteractionWithDetails) GetCompanyOk() (*InteractionWithDetailsAllOfCompany, bool)`

GetCompanyOk returns a tuple with the Company field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCompany

`func (o *InteractionWithDetails) SetCompany(v InteractionWithDetailsAllOfCompany)`

SetCompany sets Company field to given value.

### HasCompany

`func (o *InteractionWithDetails) HasCompany() bool`

HasCompany returns a boolean if a field has been set.

### SetCompanyNil

`func (o *InteractionWithDetails) SetCompanyNil(b bool)`

 SetCompanyNil sets the value for Company to be an explicit nil

### UnsetCompany
`func (o *InteractionWithDetails) UnsetCompany()`

UnsetCompany ensures that no value is present for Company, not even an explicit nil
### GetContact

`func (o *InteractionWithDetails) GetContact() InteractionWithDetailsAllOfContact`

GetContact returns the Contact field if non-nil, zero value otherwise.

### GetContactOk

`func (o *InteractionWithDetails) GetContactOk() (*InteractionWithDetailsAllOfContact, bool)`

GetContactOk returns a tuple with the Contact field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetContact

`func (o *InteractionWithDetails) SetContact(v InteractionWithDetailsAllOfContact)`

SetContact sets Contact field to given value.

### HasContact

`func (o *InteractionWithDetails) HasContact() bool`

HasContact returns a boolean if a field has been set.

### SetContactNil

`func (o *InteractionWithDetails) SetContactNil(b bool)`

 SetContactNil sets the value for Contact to be an explicit nil

### UnsetContact
`func (o *InteractionWithDetails) UnsetContact()`

UnsetContact ensures that no value is present for Contact, not even an explicit nil

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


