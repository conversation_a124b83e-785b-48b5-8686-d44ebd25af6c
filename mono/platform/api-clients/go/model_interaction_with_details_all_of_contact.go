/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
)

// checks if the InteractionWithDetailsAllOfContact type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &InteractionWithDetailsAllOfContact{}

// InteractionWithDetailsAllOfContact struct for InteractionWithDetailsAllOfContact
type InteractionWithDetailsAllOfContact struct {
	Id *string `json:"id,omitempty"`
	FirstName *string `json:"first_name,omitempty"`
	LastName *string `json:"last_name,omitempty"`
	Email *string `json:"email,omitempty"`
}

// NewInteractionWithDetailsAllOfContact instantiates a new InteractionWithDetailsAllOfContact object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewInteractionWithDetailsAllOfContact() *InteractionWithDetailsAllOfContact {
	this := InteractionWithDetailsAllOfContact{}
	return &this
}

// NewInteractionWithDetailsAllOfContactWithDefaults instantiates a new InteractionWithDetailsAllOfContact object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewInteractionWithDetailsAllOfContactWithDefaults() *InteractionWithDetailsAllOfContact {
	this := InteractionWithDetailsAllOfContact{}
	return &this
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *InteractionWithDetailsAllOfContact) GetId() string {
	if o == nil || IsNil(o.Id) {
		var ret string
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionWithDetailsAllOfContact) GetIdOk() (*string, bool) {
	if o == nil || IsNil(o.Id) {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *InteractionWithDetailsAllOfContact) HasId() bool {
	if o != nil && !IsNil(o.Id) {
		return true
	}

	return false
}

// SetId gets a reference to the given string and assigns it to the Id field.
func (o *InteractionWithDetailsAllOfContact) SetId(v string) {
	o.Id = &v
}

// GetFirstName returns the FirstName field value if set, zero value otherwise.
func (o *InteractionWithDetailsAllOfContact) GetFirstName() string {
	if o == nil || IsNil(o.FirstName) {
		var ret string
		return ret
	}
	return *o.FirstName
}

// GetFirstNameOk returns a tuple with the FirstName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionWithDetailsAllOfContact) GetFirstNameOk() (*string, bool) {
	if o == nil || IsNil(o.FirstName) {
		return nil, false
	}
	return o.FirstName, true
}

// HasFirstName returns a boolean if a field has been set.
func (o *InteractionWithDetailsAllOfContact) HasFirstName() bool {
	if o != nil && !IsNil(o.FirstName) {
		return true
	}

	return false
}

// SetFirstName gets a reference to the given string and assigns it to the FirstName field.
func (o *InteractionWithDetailsAllOfContact) SetFirstName(v string) {
	o.FirstName = &v
}

// GetLastName returns the LastName field value if set, zero value otherwise.
func (o *InteractionWithDetailsAllOfContact) GetLastName() string {
	if o == nil || IsNil(o.LastName) {
		var ret string
		return ret
	}
	return *o.LastName
}

// GetLastNameOk returns a tuple with the LastName field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionWithDetailsAllOfContact) GetLastNameOk() (*string, bool) {
	if o == nil || IsNil(o.LastName) {
		return nil, false
	}
	return o.LastName, true
}

// HasLastName returns a boolean if a field has been set.
func (o *InteractionWithDetailsAllOfContact) HasLastName() bool {
	if o != nil && !IsNil(o.LastName) {
		return true
	}

	return false
}

// SetLastName gets a reference to the given string and assigns it to the LastName field.
func (o *InteractionWithDetailsAllOfContact) SetLastName(v string) {
	o.LastName = &v
}

// GetEmail returns the Email field value if set, zero value otherwise.
func (o *InteractionWithDetailsAllOfContact) GetEmail() string {
	if o == nil || IsNil(o.Email) {
		var ret string
		return ret
	}
	return *o.Email
}

// GetEmailOk returns a tuple with the Email field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionWithDetailsAllOfContact) GetEmailOk() (*string, bool) {
	if o == nil || IsNil(o.Email) {
		return nil, false
	}
	return o.Email, true
}

// HasEmail returns a boolean if a field has been set.
func (o *InteractionWithDetailsAllOfContact) HasEmail() bool {
	if o != nil && !IsNil(o.Email) {
		return true
	}

	return false
}

// SetEmail gets a reference to the given string and assigns it to the Email field.
func (o *InteractionWithDetailsAllOfContact) SetEmail(v string) {
	o.Email = &v
}

func (o InteractionWithDetailsAllOfContact) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o InteractionWithDetailsAllOfContact) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Id) {
		toSerialize["id"] = o.Id
	}
	if !IsNil(o.FirstName) {
		toSerialize["first_name"] = o.FirstName
	}
	if !IsNil(o.LastName) {
		toSerialize["last_name"] = o.LastName
	}
	if !IsNil(o.Email) {
		toSerialize["email"] = o.Email
	}
	return toSerialize, nil
}

type NullableInteractionWithDetailsAllOfContact struct {
	value *InteractionWithDetailsAllOfContact
	isSet bool
}

func (v NullableInteractionWithDetailsAllOfContact) Get() *InteractionWithDetailsAllOfContact {
	return v.value
}

func (v *NullableInteractionWithDetailsAllOfContact) Set(val *InteractionWithDetailsAllOfContact) {
	v.value = val
	v.isSet = true
}

func (v NullableInteractionWithDetailsAllOfContact) IsSet() bool {
	return v.isSet
}

func (v *NullableInteractionWithDetailsAllOfContact) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableInteractionWithDetailsAllOfContact(val *InteractionWithDetailsAllOfContact) *NullableInteractionWithDetailsAllOfContact {
	return &NullableInteractionWithDetailsAllOfContact{value: val, isSet: true}
}

func (v NullableInteractionWithDetailsAllOfContact) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableInteractionWithDetailsAllOfContact) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


