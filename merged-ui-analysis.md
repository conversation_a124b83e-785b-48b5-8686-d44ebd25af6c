# Merged UI Analysis - Three Project Systems

## Overview
This document analyzes three distinct project management systems and proposes a unified UI design that combines their features while maintaining consistency and role-based access.

## Current Projects Analysis

### 1. Core Project Pulse (Internal Team Tracking)
**Purpose**: Internal project tracking for development teams
**Tech Stack**: React, TypeScript, Vite, Supabase, shadcn-ui, Tailwind CSS

**Key Features**:
- Project CRUD operations with status tracking (backlog, not-started, in-progress, completed, archived)
- Task and subtask management with assignees and due dates
- Team member management with role assignments
- Priority system (P0-P4) with effort estimates (S, M, L, XL)
- Impact categorization (Revenue, Platform, Bug Fix, R&D)
- Project progress calculation and metrics
- Integration support (Google Drive, Slack webhooks)
- Project completion tracking with incomplete task warnings

**Data Model**:
- Projects: id, name, company_name, type, status, dates, priority_level, effort_estimate, impact_type
- Tasks: id, project_id, name, assignee_id, status, due_date
- SubTasks: id, task_id, name, assignee_id, status, due_date
- Team Members: id, name, email, role
- Priority History: tracking priority changes and escalations

### 2. Project Brief Buddy (PRD Capturing System)
**Purpose**: Requirements documentation and PRD creation
**Tech Stack**: React, TypeScript, Vite, shadcn-ui, Tailwind CSS

**Key Features**:
- Project dashboard with status filtering (active, draft, completed)
- Meeting recording interface with audio capture
- Markdown-based PRD editor with templates
- Project checklist system for requirement completion
- File upload for workflow diagrams and mockups
- AI-powered transcription capabilities
- Export functionality for documentation

**Data Model**:
- Projects: id, name, company, description, status, progress, tags
- Checklist items: recording, transcription, brief, workflow, mockups, externals
- Meeting recordings with metadata
- PRD content in markdown format

### 3. Mono/Platform/Web (CRM System)
**Purpose**: Customer relationship management with project progress display
**Tech Stack**: React, TypeScript, Vite, Supabase, shadcn-ui, Tailwind CSS, Bazel build

**Key Features**:
- Company management with detailed profiles
- Contact management linked to companies
- Deal pipeline with stage tracking
- Interaction history and communication logs
- Kanban boards for workflow management
- Dashboard with analytics and charts
- Research notes and company intelligence
- Scheduling and availability management
- Arli AI document processing system

**Data Model**:
- Companies: id, name, status, contact_info, research_notes
- Contacts: id, company_id, name, email, phone, role
- Deals: id, company_id, stage, value, probability
- Interactions: id, company_id, contact_id, type, content, date
- Users: authentication and profile management

## Common Patterns Identified

### UI Components
- All three use shadcn-ui component library
- Consistent use of Tailwind CSS for styling
- Similar card-based layouts and data tables
- Common form patterns and modal dialogs
- Shared navigation and layout structures

### Data Patterns
- Project-centric data models across all systems
- Status-based workflow management
- User/team member associations
- Date tracking (created, updated, completed)
- Progress tracking and metrics

### Technical Stack
- React + TypeScript foundation
- Vite build system (except mono uses Bazel)
- Similar routing patterns with React Router
- Query management with TanStack Query
- Supabase for data persistence (where implemented)

## Proposed Unified Architecture

### Core Entities
1. **Unified Project**: Combines all three project types with role-based views
2. **Companies**: From CRM system, linked to external projects
3. **Contacts**: From CRM system, associated with projects and companies
4. **Tasks**: Enhanced task system supporting both internal and customer-visible tasks
5. **Requirements**: PRD and documentation management integrated into project lifecycle
6. **Team Members**: Unified user management across all project types
7. **Interactions**: Communication history across all project touchpoints

### User Roles
1. **Internal Team Member**: Full access to project tracking, tasks, and internal metrics
2. **Project Manager**: Cross-project visibility, resource allocation, priority management
3. **Customer**: Limited view of project progress, milestones, and deliverables
4. **Requirements Analyst**: PRD creation, documentation, and requirement tracking
5. **Admin**: System configuration, user management, and reporting

### Navigation Structure
```
Dashboard
├── Projects (unified view with role-based filtering)
├── Companies (CRM functionality)
├── Contacts (CRM functionality)  
├── Requirements (PRD management)
├── Tasks (cross-project task management)
├── Reports (analytics across all project types)
└── Settings (user preferences and system config)
```

## Next Steps
1. Design unified data model schema
2. Create merged navigation and layout components
3. Implement role-based access control
4. Build unified project dashboard
5. Integrate all three feature sets into cohesive workflows
