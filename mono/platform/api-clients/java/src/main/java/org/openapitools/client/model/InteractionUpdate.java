/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import java.util.Objects;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.openapitools.client.JSON;

/**
 * InteractionUpdate
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-07-14T08:13:23.820009+10:00[Australia/Brisbane]", comments = "Generator version: 7.9.0")
public class InteractionUpdate {
  /**
   * Gets or Sets interactionType
   */
  @JsonAdapter(InteractionTypeEnum.Adapter.class)
  public enum InteractionTypeEnum {
    EMAIL("email"),
    
    PHONE("phone"),
    
    MEETING("meeting"),
    
    DEMO("demo"),
    
    PROPOSAL("proposal"),
    
    FOLLOW_UP("follow-up"),
    
    OTHER("other");

    private String value;

    InteractionTypeEnum(String value) {
      this.value = value;
    }

    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    public static InteractionTypeEnum fromValue(String value) {
      for (InteractionTypeEnum b : InteractionTypeEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }

    public static class Adapter extends TypeAdapter<InteractionTypeEnum> {
      @Override
      public void write(final JsonWriter jsonWriter, final InteractionTypeEnum enumeration) throws IOException {
        jsonWriter.value(enumeration.getValue());
      }

      @Override
      public InteractionTypeEnum read(final JsonReader jsonReader) throws IOException {
        String value =  jsonReader.nextString();
        return InteractionTypeEnum.fromValue(value);
      }
    }

    public static void validateJsonElement(JsonElement jsonElement) throws IOException {
      String value = jsonElement.getAsString();
      InteractionTypeEnum.fromValue(value);
    }
  }

  public static final String SERIALIZED_NAME_INTERACTION_TYPE = "interaction_type";
  @SerializedName(SERIALIZED_NAME_INTERACTION_TYPE)
  private InteractionTypeEnum interactionType;

  public static final String SERIALIZED_NAME_NOTES = "notes";
  @SerializedName(SERIALIZED_NAME_NOTES)
  private String notes;

  public static final String SERIALIZED_NAME_INTERACTION_DATETIME = "interaction_datetime";
  @SerializedName(SERIALIZED_NAME_INTERACTION_DATETIME)
  private OffsetDateTime interactionDatetime;

  public static final String SERIALIZED_NAME_COMPANY_ID = "company_id";
  @SerializedName(SERIALIZED_NAME_COMPANY_ID)
  private UUID companyId;

  public static final String SERIALIZED_NAME_CONTACT_ID = "contact_id";
  @SerializedName(SERIALIZED_NAME_CONTACT_ID)
  private UUID contactId;

  public InteractionUpdate() {
  }

  public InteractionUpdate interactionType(InteractionTypeEnum interactionType) {
    this.interactionType = interactionType;
    return this;
  }

  /**
   * Get interactionType
   * @return interactionType
   */
  @javax.annotation.Nullable
  public InteractionTypeEnum getInteractionType() {
    return interactionType;
  }

  public void setInteractionType(InteractionTypeEnum interactionType) {
    this.interactionType = interactionType;
  }


  public InteractionUpdate notes(String notes) {
    this.notes = notes;
    return this;
  }

  /**
   * Get notes
   * @return notes
   */
  @javax.annotation.Nullable
  public String getNotes() {
    return notes;
  }

  public void setNotes(String notes) {
    this.notes = notes;
  }


  public InteractionUpdate interactionDatetime(OffsetDateTime interactionDatetime) {
    this.interactionDatetime = interactionDatetime;
    return this;
  }

  /**
   * Get interactionDatetime
   * @return interactionDatetime
   */
  @javax.annotation.Nullable
  public OffsetDateTime getInteractionDatetime() {
    return interactionDatetime;
  }

  public void setInteractionDatetime(OffsetDateTime interactionDatetime) {
    this.interactionDatetime = interactionDatetime;
  }


  public InteractionUpdate companyId(UUID companyId) {
    this.companyId = companyId;
    return this;
  }

  /**
   * Get companyId
   * @return companyId
   */
  @javax.annotation.Nullable
  public UUID getCompanyId() {
    return companyId;
  }

  public void setCompanyId(UUID companyId) {
    this.companyId = companyId;
  }


  public InteractionUpdate contactId(UUID contactId) {
    this.contactId = contactId;
    return this;
  }

  /**
   * Get contactId
   * @return contactId
   */
  @javax.annotation.Nullable
  public UUID getContactId() {
    return contactId;
  }

  public void setContactId(UUID contactId) {
    this.contactId = contactId;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InteractionUpdate interactionUpdate = (InteractionUpdate) o;
    return Objects.equals(this.interactionType, interactionUpdate.interactionType) &&
        Objects.equals(this.notes, interactionUpdate.notes) &&
        Objects.equals(this.interactionDatetime, interactionUpdate.interactionDatetime) &&
        Objects.equals(this.companyId, interactionUpdate.companyId) &&
        Objects.equals(this.contactId, interactionUpdate.contactId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(interactionType, notes, interactionDatetime, companyId, contactId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InteractionUpdate {\n");
    sb.append("    interactionType: ").append(toIndentedString(interactionType)).append("\n");
    sb.append("    notes: ").append(toIndentedString(notes)).append("\n");
    sb.append("    interactionDatetime: ").append(toIndentedString(interactionDatetime)).append("\n");
    sb.append("    companyId: ").append(toIndentedString(companyId)).append("\n");
    sb.append("    contactId: ").append(toIndentedString(contactId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("interaction_type");
    openapiFields.add("notes");
    openapiFields.add("interaction_datetime");
    openapiFields.add("company_id");
    openapiFields.add("contact_id");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

  /**
   * Validates the JSON Element and throws an exception if issues found
   *
   * @param jsonElement JSON Element
   * @throws IOException if the JSON Element is invalid with respect to InteractionUpdate
   */
  public static void validateJsonElement(JsonElement jsonElement) throws IOException {
      if (jsonElement == null) {
        if (!InteractionUpdate.openapiRequiredFields.isEmpty()) { // has required fields but JSON element is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in InteractionUpdate is not found in the empty JSON string", InteractionUpdate.openapiRequiredFields.toString()));
        }
      }

      Set<Map.Entry<String, JsonElement>> entries = jsonElement.getAsJsonObject().entrySet();
      // check to see if the JSON string contains additional fields
      for (Map.Entry<String, JsonElement> entry : entries) {
        if (!InteractionUpdate.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `InteractionUpdate` properties. JSON: %s", entry.getKey(), jsonElement.toString()));
        }
      }
        JsonObject jsonObj = jsonElement.getAsJsonObject();
      if ((jsonObj.get("interaction_type") != null && !jsonObj.get("interaction_type").isJsonNull()) && !jsonObj.get("interaction_type").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `interaction_type` to be a primitive type in the JSON string but got `%s`", jsonObj.get("interaction_type").toString()));
      }
      // validate the optional field `interaction_type`
      if (jsonObj.get("interaction_type") != null && !jsonObj.get("interaction_type").isJsonNull()) {
        InteractionTypeEnum.validateJsonElement(jsonObj.get("interaction_type"));
      }
      if ((jsonObj.get("notes") != null && !jsonObj.get("notes").isJsonNull()) && !jsonObj.get("notes").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `notes` to be a primitive type in the JSON string but got `%s`", jsonObj.get("notes").toString()));
      }
      if ((jsonObj.get("company_id") != null && !jsonObj.get("company_id").isJsonNull()) && !jsonObj.get("company_id").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `company_id` to be a primitive type in the JSON string but got `%s`", jsonObj.get("company_id").toString()));
      }
      if ((jsonObj.get("contact_id") != null && !jsonObj.get("contact_id").isJsonNull()) && !jsonObj.get("contact_id").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `contact_id` to be a primitive type in the JSON string but got `%s`", jsonObj.get("contact_id").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!InteractionUpdate.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'InteractionUpdate' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<InteractionUpdate> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(InteractionUpdate.class));

       return (TypeAdapter<T>) new TypeAdapter<InteractionUpdate>() {
           @Override
           public void write(JsonWriter out, InteractionUpdate value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public InteractionUpdate read(JsonReader in) throws IOException {
             JsonElement jsonElement = elementAdapter.read(in);
             validateJsonElement(jsonElement);
             return thisAdapter.fromJsonTree(jsonElement);
           }

       }.nullSafe();
    }
  }

  /**
   * Create an instance of InteractionUpdate given an JSON string
   *
   * @param jsonString JSON string
   * @return An instance of InteractionUpdate
   * @throws IOException if the JSON string is invalid with respect to InteractionUpdate
   */
  public static InteractionUpdate fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, InteractionUpdate.class);
  }

  /**
   * Convert an instance of InteractionUpdate to an JSON string
   *
   * @return JSON string
   */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

