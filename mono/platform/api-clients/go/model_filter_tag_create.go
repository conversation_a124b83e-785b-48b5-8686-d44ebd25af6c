/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"bytes"
	"fmt"
)

// checks if the FilterTagCreate type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &FilterTagCreate{}

// FilterTagCreate struct for FilterTagCreate
type FilterTagCreate struct {
	Name string `json:"name"`
	Color *string `json:"color,omitempty" validate:"regexp=^#[0-9a-fA-F]{6}$"`
}

type _FilterTagCreate FilterTagCreate

// NewFilterTagCreate instantiates a new FilterTagCreate object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewFilterTagCreate(name string) *FilterTagCreate {
	this := FilterTagCreate{}
	this.Name = name
	var color string = "#3b82f6"
	this.Color = &color
	return &this
}

// NewFilterTagCreateWithDefaults instantiates a new FilterTagCreate object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewFilterTagCreateWithDefaults() *FilterTagCreate {
	this := FilterTagCreate{}
	var color string = "#3b82f6"
	this.Color = &color
	return &this
}

// GetName returns the Name field value
func (o *FilterTagCreate) GetName() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Name
}

// GetNameOk returns a tuple with the Name field value
// and a boolean to check if the value has been set.
func (o *FilterTagCreate) GetNameOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Name, true
}

// SetName sets field value
func (o *FilterTagCreate) SetName(v string) {
	o.Name = v
}

// GetColor returns the Color field value if set, zero value otherwise.
func (o *FilterTagCreate) GetColor() string {
	if o == nil || IsNil(o.Color) {
		var ret string
		return ret
	}
	return *o.Color
}

// GetColorOk returns a tuple with the Color field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *FilterTagCreate) GetColorOk() (*string, bool) {
	if o == nil || IsNil(o.Color) {
		return nil, false
	}
	return o.Color, true
}

// HasColor returns a boolean if a field has been set.
func (o *FilterTagCreate) HasColor() bool {
	if o != nil && !IsNil(o.Color) {
		return true
	}

	return false
}

// SetColor gets a reference to the given string and assigns it to the Color field.
func (o *FilterTagCreate) SetColor(v string) {
	o.Color = &v
}

func (o FilterTagCreate) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o FilterTagCreate) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["name"] = o.Name
	if !IsNil(o.Color) {
		toSerialize["color"] = o.Color
	}
	return toSerialize, nil
}

func (o *FilterTagCreate) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"name",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varFilterTagCreate := _FilterTagCreate{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varFilterTagCreate)

	if err != nil {
		return err
	}

	*o = FilterTagCreate(varFilterTagCreate)

	return err
}

type NullableFilterTagCreate struct {
	value *FilterTagCreate
	isSet bool
}

func (v NullableFilterTagCreate) Get() *FilterTagCreate {
	return v.value
}

func (v *NullableFilterTagCreate) Set(val *FilterTagCreate) {
	v.value = val
	v.isSet = true
}

func (v NullableFilterTagCreate) IsSet() bool {
	return v.isSet
}

func (v *NullableFilterTagCreate) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableFilterTagCreate(val *FilterTagCreate) *NullableFilterTagCreate {
	return &NullableFilterTagCreate{value: val, isSet: true}
}

func (v NullableFilterTagCreate) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableFilterTagCreate) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


