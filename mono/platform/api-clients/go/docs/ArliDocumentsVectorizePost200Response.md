# ArliDocumentsVectorizePost200Response

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Success** | Pointer to **bool** |  | [optional] 
**Message** | Pointer to **string** |  | [optional] 
**VectorId** | Pointer to **string** |  | [optional] 

## Methods

### NewArliDocumentsVectorizePost200Response

`func NewArliDocumentsVectorizePost200Response() *ArliDocumentsVectorizePost200Response`

NewArliDocumentsVectorizePost200Response instantiates a new ArliDocumentsVectorizePost200Response object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewArliDocumentsVectorizePost200ResponseWithDefaults

`func NewArliDocumentsVectorizePost200ResponseWithDefaults() *ArliDocumentsVectorizePost200Response`

NewArliDocumentsVectorizePost200ResponseWithDefaults instantiates a new ArliDocumentsVectorizePost200Response object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetSuccess

`func (o *ArliDocumentsVectorizePost200Response) GetSuccess() bool`

GetSuccess returns the Success field if non-nil, zero value otherwise.

### GetSuccessOk

`func (o *ArliDocumentsVectorizePost200Response) GetSuccessOk() (*bool, bool)`

GetSuccessOk returns a tuple with the Success field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSuccess

`func (o *ArliDocumentsVectorizePost200Response) SetSuccess(v bool)`

SetSuccess sets Success field to given value.

### HasSuccess

`func (o *ArliDocumentsVectorizePost200Response) HasSuccess() bool`

HasSuccess returns a boolean if a field has been set.

### GetMessage

`func (o *ArliDocumentsVectorizePost200Response) GetMessage() string`

GetMessage returns the Message field if non-nil, zero value otherwise.

### GetMessageOk

`func (o *ArliDocumentsVectorizePost200Response) GetMessageOk() (*string, bool)`

GetMessageOk returns a tuple with the Message field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMessage

`func (o *ArliDocumentsVectorizePost200Response) SetMessage(v string)`

SetMessage sets Message field to given value.

### HasMessage

`func (o *ArliDocumentsVectorizePost200Response) HasMessage() bool`

HasMessage returns a boolean if a field has been set.

### GetVectorId

`func (o *ArliDocumentsVectorizePost200Response) GetVectorId() string`

GetVectorId returns the VectorId field if non-nil, zero value otherwise.

### GetVectorIdOk

`func (o *ArliDocumentsVectorizePost200Response) GetVectorIdOk() (*string, bool)`

GetVectorIdOk returns a tuple with the VectorId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetVectorId

`func (o *ArliDocumentsVectorizePost200Response) SetVectorId(v string)`

SetVectorId sets VectorId field to given value.

### HasVectorId

`func (o *ArliDocumentsVectorizePost200Response) HasVectorId() bool`

HasVectorId returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


