import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface ConvertTaskToProjectParams {
  taskId: string;
  projectName: string;
  projectDescription?: string;
  type?: string;
  companyName?: string;
  startDate?: string;
  endDate?: string;
  customerName?: string;
  customerLead?: string;
  customerContact?: string;
  prdDocumentLink?: string;
  pocUrl?: string;
  effortEstimate?: 'S' | 'M' | 'L' | 'XL';
  impactType?: 'Revenue' | 'Platform' | 'Bug Fix' | 'R&D';
  priorityLevel?: 'P0' | 'P1' | 'P2' | 'P3' | 'P4';
}

export const useConvertTaskToProject = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (params: ConvertTaskToProjectParams) => {
      const { data, error } = await supabase.rpc('convert_task_to_project', {
        task_id_param: params.taskId,
        project_name: params.projectName,
        project_description: params.projectDescription,
        project_type: params.type || 'internal',
        company_name: params.companyName || 'TwoDot AI',
        start_date: params.startDate,
        end_date: params.endDate,
        customer_name: params.customerName,
        customer_lead: params.customerLead,
        customer_contact: params.customerContact,
        prd_document_link: params.prdDocumentLink,
        poc_url: params.pocUrl,
        effort_estimate: params.effortEstimate || 'M',
        impact_type: params.impactType || 'Platform',
        priority_level: params.priorityLevel || 'P3',
      });

      if (error) {
        console.error('Error converting task to project:', error);
        throw new Error(error.message);
      }

      return data;
    },
    onSuccess: (newProjectId) => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ["projects"] });
      queryClient.invalidateQueries({ queryKey: ["tasks"] });
      
      toast({
        title: "Task converted successfully",
        description: "The task has been converted to a project.",
      });

      // Navigate to the new project
      navigate(`/projects/${newProjectId}`);
    },
    onError: (error: Error) => {
      toast({
        title: "Error converting task",
        description: error.message,
        variant: "destructive",
      });
    },
  });
};