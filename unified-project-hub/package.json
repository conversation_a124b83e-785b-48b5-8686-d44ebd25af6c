{"name": "unified-project-hub", "version": "1.0.0", "description": "Unified project management system combining internal tracking, CRM, and requirements documentation", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "cd backend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "test:coverage": "npm run test:backend -- --coverage && npm run test:frontend -- --coverage", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:backend -- --fix && npm run lint:frontend -- --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:reset": "cd backend && npm run db:reset", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:build": "docker-compose build", "postinstall": "npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "clean": "npm run clean:backend && npm run clean:frontend", "clean:backend": "cd backend && rm -rf node_modules dist", "clean:frontend": "cd frontend && rm -rf node_modules dist", "docs:generate": "cd backend && npm run docs:generate", "docs:serve": "cd backend && npm run docs:serve"}, "keywords": ["project-management", "crm", "requirements", "prd", "task-tracking", "team-collaboration"], "author": "Development Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "prettier": "^3.0.3", "husky": "^8.0.3", "lint-staged": "^15.0.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"], "*.{json,md}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/unified-project-hub.git"}, "bugs": {"url": "https://github.com/your-org/unified-project-hub/issues"}, "homepage": "https://github.com/your-org/unified-project-hub#readme"}