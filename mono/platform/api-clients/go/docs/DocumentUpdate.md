# DocumentUpdate

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Content** | Pointer to **string** |  | [optional] 
**Metadata** | Pointer to **map[string]interface{}** |  | [optional] 
**FilterTags** | Pointer to **[]string** |  | [optional] 
**SourceId** | Pointer to **string** |  | [optional] 

## Methods

### NewDocumentUpdate

`func NewDocumentUpdate() *DocumentUpdate`

NewDocumentUpdate instantiates a new DocumentUpdate object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDocumentUpdateWithDefaults

`func NewDocumentUpdateWithDefaults() *DocumentUpdate`

NewDocumentUpdateWithDefaults instantiates a new DocumentUpdate object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetContent

`func (o *DocumentUpdate) GetContent() string`

GetContent returns the Content field if non-nil, zero value otherwise.

### GetContentOk

`func (o *DocumentUpdate) GetContentOk() (*string, bool)`

GetContentOk returns a tuple with the Content field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetContent

`func (o *DocumentUpdate) SetContent(v string)`

SetContent sets Content field to given value.

### HasContent

`func (o *DocumentUpdate) HasContent() bool`

HasContent returns a boolean if a field has been set.

### GetMetadata

`func (o *DocumentUpdate) GetMetadata() map[string]interface{}`

GetMetadata returns the Metadata field if non-nil, zero value otherwise.

### GetMetadataOk

`func (o *DocumentUpdate) GetMetadataOk() (*map[string]interface{}, bool)`

GetMetadataOk returns a tuple with the Metadata field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMetadata

`func (o *DocumentUpdate) SetMetadata(v map[string]interface{})`

SetMetadata sets Metadata field to given value.

### HasMetadata

`func (o *DocumentUpdate) HasMetadata() bool`

HasMetadata returns a boolean if a field has been set.

### GetFilterTags

`func (o *DocumentUpdate) GetFilterTags() []string`

GetFilterTags returns the FilterTags field if non-nil, zero value otherwise.

### GetFilterTagsOk

`func (o *DocumentUpdate) GetFilterTagsOk() (*[]string, bool)`

GetFilterTagsOk returns a tuple with the FilterTags field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFilterTags

`func (o *DocumentUpdate) SetFilterTags(v []string)`

SetFilterTags sets FilterTags field to given value.

### HasFilterTags

`func (o *DocumentUpdate) HasFilterTags() bool`

HasFilterTags returns a boolean if a field has been set.

### GetSourceId

`func (o *DocumentUpdate) GetSourceId() string`

GetSourceId returns the SourceId field if non-nil, zero value otherwise.

### GetSourceIdOk

`func (o *DocumentUpdate) GetSourceIdOk() (*string, bool)`

GetSourceIdOk returns a tuple with the SourceId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSourceId

`func (o *DocumentUpdate) SetSourceId(v string)`

SetSourceId sets SourceId field to given value.

### HasSourceId

`func (o *DocumentUpdate) HasSourceId() bool`

HasSourceId returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


