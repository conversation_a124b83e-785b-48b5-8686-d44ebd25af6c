import React, { useState, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { Plus, Grid, List, Kanban, BarChart3 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { ProjectFilters } from '@/components/projects/ProjectFilters';
import { ProjectGridView } from '@/components/projects/ProjectGridView';
import { ProjectListView } from '@/components/projects/ProjectListView';
import { ProjectKanbanView } from '@/components/projects/ProjectKanbanView';
import { useProjects, useProjectStats, useProjectActions } from '@/hooks/useProjects';

interface ProjectsPageProps {
  filter?: 'my' | 'active' | 'archived';
}

export default function ProjectsPage({ filter }: ProjectsPageProps) {
  const { user } = useAuth();
  const { toast } = useToast();

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [leadFilter, setLeadFilter] = useState('all');
  const [companyFilter, setCompanyFilter] = useState('all');
  const [showArchivedProjects, setShowArchivedProjects] = useState(false);
  const [sortBy, setSortBy] = useState('updated_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [groupBy, setGroupBy] = useState('none');
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'kanban'>('grid');
  const [currentPage, setCurrentPage] = useState(1);

  // Fetch projects data
  const { data: projectsData, loading, error, refetch } = useProjects({
    page: currentPage,
    limit: 20,
    status: statusFilter,
    type: typeFilter,
    search: searchTerm,
    priority_level: priorityFilter,
    lead_id: leadFilter,
    company_id: companyFilter,
    show_archived: showArchivedProjects,
    sort_by: sortBy,
    sort_order: sortOrder,
    group_by: groupBy,
  });

  const { stats } = useProjectStats();
  const { updateProjectStatus, archiveProject, updateProjectPriority, deleteProject } = useProjectActions();

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    setSearchTerm('');
    setTypeFilter('all');
    setStatusFilter('all');
    setPriorityFilter('all');
    setLeadFilter('all');
    setCompanyFilter('all');
    setShowArchivedProjects(false);
    setSortBy('updated_at');
    setSortOrder('desc');
    setGroupBy('none');
    setCurrentPage(1);
  }, []);

  // Project actions
  const handleEditProject = useCallback((project: any) => {
    // TODO: Open edit dialog or navigate to edit page
    console.log('Edit project:', project);
  }, []);

  const handleArchiveProject = useCallback(async (project: any) => {
    try {
      const isArchived = project.status === 'archived';
      await archiveProject(project.id, !isArchived);

      toast({
        title: 'Success',
        description: `Project ${isArchived ? 'unarchived' : 'archived'} successfully`,
      });

      refetch();
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to archive project',
        variant: 'destructive',
      });
    }
  }, [archiveProject, refetch, toast]);

  const handleDeleteProject = useCallback(async (project: any) => {
    if (!confirm(`Are you sure you want to delete "${project.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteProject(project.id);

      toast({
        title: 'Success',
        description: 'Project deleted successfully',
      });

      refetch();
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete project',
        variant: 'destructive',
      });
    }
  }, [deleteProject, refetch, toast]);

  const getPageTitle = () => {
    switch (filter) {
      case 'my':
        return 'My Projects';
      case 'active':
        return 'Active Projects';
      case 'archived':
        return 'Archived Projects';
      default:
        return 'All Projects';
    }
  };

  const getPageDescription = () => {
    switch (filter) {
      case 'my':
        return 'Projects you are leading or participating in';
      case 'active':
        return 'Currently active projects across the organization';
      case 'archived':
        return 'Completed and archived projects';
      default:
        return 'Manage and track all projects across the organization';
    }
  };

  const canCreateProject = user?.role === 'admin' || user?.role === 'project_manager';

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading projects: {error}</p>
          <Button onClick={refetch}>Try Again</Button>
        </div>
      </div>
    );
  }

  const projects = projectsData?.projects || [];
  const groupedProjects = projectsData?.grouped_projects;
  const pagination = projectsData?.pagination;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{getPageTitle()}</h1>
          <p className="text-gray-600 mt-1">{getPageDescription()}</p>
          {stats && (
            <div className="flex gap-4 mt-2 text-sm text-muted-foreground">
              <span>{stats.overview.total_projects} total</span>
              <span>{stats.overview.active_projects} active</span>
              <span>{stats.overview.completed_projects} completed</span>
              {stats.overview.overdue_projects > 0 && (
                <span className="text-red-600">{stats.overview.overdue_projects} overdue</span>
              )}
            </div>
          )}
        </div>
        {canCreateProject && (
          <Button asChild>
            <Link to="/projects/new">
              <Plus className="mr-2 h-4 w-4" />
              New Project
            </Link>
          </Button>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ProjectFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            typeFilter={typeFilter}
            onTypeFilterChange={setTypeFilter}
            statusFilter={statusFilter}
            onStatusFilterChange={setStatusFilter}
            priorityFilter={priorityFilter}
            onPriorityFilterChange={setPriorityFilter}
            leadFilter={leadFilter}
            onLeadFilterChange={setLeadFilter}
            companyFilter={companyFilter}
            onCompanyFilterChange={setCompanyFilter}
            showArchivedProjects={showArchivedProjects}
            onShowArchivedProjectsChange={setShowArchivedProjects}
            sortBy={sortBy}
            onSortByChange={setSortBy}
            sortOrder={sortOrder}
            onSortOrderChange={setSortOrder}
            groupBy={groupBy}
            onGroupByChange={setGroupBy}
            onClearFilters={handleClearFilters}
            archivedProjectsCount={stats?.overview.archived_projects || 0}
            teamMembers={[]} // TODO: Fetch team members
            companies={[]} // TODO: Fetch companies
          />
        </CardContent>
      </Card>

      {/* View Mode Tabs */}
      <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as any)}>
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="grid" className="flex items-center gap-2">
              <Grid className="h-4 w-4" />
              Grid
            </TabsTrigger>
            <TabsTrigger value="list" className="flex items-center gap-2">
              <List className="h-4 w-4" />
              List
            </TabsTrigger>
            <TabsTrigger value="kanban" className="flex items-center gap-2">
              <Kanban className="h-4 w-4" />
              Kanban
            </TabsTrigger>
          </TabsList>

          {pagination && (
            <div className="text-sm text-muted-foreground">
              Showing {projects.length} of {pagination.total} projects
            </div>
          )}
        </div>

        <TabsContent value="grid" className="mt-6">
          <ProjectGridView
            projects={projects}
            groupedProjects={groupedProjects}
            groupBy={groupBy}
            onEdit={handleEditProject}
            onArchive={handleArchiveProject}
            onDelete={handleDeleteProject}
          />
        </TabsContent>

        <TabsContent value="list" className="mt-6">
          <ProjectListView
            projects={projects}
            onEdit={handleEditProject}
            onArchive={handleArchiveProject}
            onDelete={handleDeleteProject}
          />
        </TabsContent>

        <TabsContent value="kanban" className="mt-6">
          <ProjectKanbanView
            projects={projects}
            onEdit={handleEditProject}
            onArchive={handleArchiveProject}
            onDelete={handleDeleteProject}
          />
        </TabsContent>
      </Tabs>

      {/* Empty State */}
      {projects.length === 0 && !loading && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">No projects found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                  ? 'Try adjusting your filters to see more projects.'
                  : 'Get started by creating your first project.'
                }
              </p>
              {canCreateProject && !searchTerm && statusFilter === 'all' && typeFilter === 'all' && (
                <Button asChild>
                  <Link to="/projects/new">
                    <Plus className="mr-2 h-4 w-4" />
                    Create Project
                  </Link>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
