/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"bytes"
	"fmt"
)

// checks if the ArliDocumentsVectorizePostRequest type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ArliDocumentsVectorizePostRequest{}

// ArliDocumentsVectorizePostRequest struct for ArliDocumentsVectorizePostRequest
type ArliDocumentsVectorizePostRequest struct {
	DocumentId string `json:"document_id"`
	Content string `json:"content"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

type _ArliDocumentsVectorizePostRequest ArliDocumentsVectorizePostRequest

// NewArliDocumentsVectorizePostRequest instantiates a new ArliDocumentsVectorizePostRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewArliDocumentsVectorizePostRequest(documentId string, content string) *ArliDocumentsVectorizePostRequest {
	this := ArliDocumentsVectorizePostRequest{}
	this.DocumentId = documentId
	this.Content = content
	return &this
}

// NewArliDocumentsVectorizePostRequestWithDefaults instantiates a new ArliDocumentsVectorizePostRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewArliDocumentsVectorizePostRequestWithDefaults() *ArliDocumentsVectorizePostRequest {
	this := ArliDocumentsVectorizePostRequest{}
	return &this
}

// GetDocumentId returns the DocumentId field value
func (o *ArliDocumentsVectorizePostRequest) GetDocumentId() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.DocumentId
}

// GetDocumentIdOk returns a tuple with the DocumentId field value
// and a boolean to check if the value has been set.
func (o *ArliDocumentsVectorizePostRequest) GetDocumentIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.DocumentId, true
}

// SetDocumentId sets field value
func (o *ArliDocumentsVectorizePostRequest) SetDocumentId(v string) {
	o.DocumentId = v
}

// GetContent returns the Content field value
func (o *ArliDocumentsVectorizePostRequest) GetContent() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Content
}

// GetContentOk returns a tuple with the Content field value
// and a boolean to check if the value has been set.
func (o *ArliDocumentsVectorizePostRequest) GetContentOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Content, true
}

// SetContent sets field value
func (o *ArliDocumentsVectorizePostRequest) SetContent(v string) {
	o.Content = v
}

// GetMetadata returns the Metadata field value if set, zero value otherwise.
func (o *ArliDocumentsVectorizePostRequest) GetMetadata() map[string]interface{} {
	if o == nil || IsNil(o.Metadata) {
		var ret map[string]interface{}
		return ret
	}
	return o.Metadata
}

// GetMetadataOk returns a tuple with the Metadata field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ArliDocumentsVectorizePostRequest) GetMetadataOk() (map[string]interface{}, bool) {
	if o == nil || IsNil(o.Metadata) {
		return map[string]interface{}{}, false
	}
	return o.Metadata, true
}

// HasMetadata returns a boolean if a field has been set.
func (o *ArliDocumentsVectorizePostRequest) HasMetadata() bool {
	if o != nil && !IsNil(o.Metadata) {
		return true
	}

	return false
}

// SetMetadata gets a reference to the given map[string]interface{} and assigns it to the Metadata field.
func (o *ArliDocumentsVectorizePostRequest) SetMetadata(v map[string]interface{}) {
	o.Metadata = v
}

func (o ArliDocumentsVectorizePostRequest) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ArliDocumentsVectorizePostRequest) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["document_id"] = o.DocumentId
	toSerialize["content"] = o.Content
	if !IsNil(o.Metadata) {
		toSerialize["metadata"] = o.Metadata
	}
	return toSerialize, nil
}

func (o *ArliDocumentsVectorizePostRequest) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"document_id",
		"content",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varArliDocumentsVectorizePostRequest := _ArliDocumentsVectorizePostRequest{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varArliDocumentsVectorizePostRequest)

	if err != nil {
		return err
	}

	*o = ArliDocumentsVectorizePostRequest(varArliDocumentsVectorizePostRequest)

	return err
}

type NullableArliDocumentsVectorizePostRequest struct {
	value *ArliDocumentsVectorizePostRequest
	isSet bool
}

func (v NullableArliDocumentsVectorizePostRequest) Get() *ArliDocumentsVectorizePostRequest {
	return v.value
}

func (v *NullableArliDocumentsVectorizePostRequest) Set(val *ArliDocumentsVectorizePostRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableArliDocumentsVectorizePostRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableArliDocumentsVectorizePostRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableArliDocumentsVectorizePostRequest(val *ArliDocumentsVectorizePostRequest) *NullableArliDocumentsVectorizePostRequest {
	return &NullableArliDocumentsVectorizePostRequest{value: val, isSet: true}
}

func (v NullableArliDocumentsVectorizePostRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableArliDocumentsVectorizePostRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


