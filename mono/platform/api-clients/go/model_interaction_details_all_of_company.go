/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
)

// checks if the InteractionDetailsAllOfCompany type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &InteractionDetailsAllOfCompany{}

// InteractionDetailsAllOfCompany struct for InteractionDetailsAllOfCompany
type InteractionDetailsAllOfCompany struct {
	Id *string `json:"id,omitempty"`
	Name *string `json:"name,omitempty"`
	Website *string `json:"website,omitempty"`
	Phone *string `json:"phone,omitempty"`
}

// NewInteractionDetailsAllOfCompany instantiates a new InteractionDetailsAllOfCompany object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewInteractionDetailsAllOfCompany() *InteractionDetailsAllOfCompany {
	this := InteractionDetailsAllOfCompany{}
	return &this
}

// NewInteractionDetailsAllOfCompanyWithDefaults instantiates a new InteractionDetailsAllOfCompany object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewInteractionDetailsAllOfCompanyWithDefaults() *InteractionDetailsAllOfCompany {
	this := InteractionDetailsAllOfCompany{}
	return &this
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *InteractionDetailsAllOfCompany) GetId() string {
	if o == nil || IsNil(o.Id) {
		var ret string
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetailsAllOfCompany) GetIdOk() (*string, bool) {
	if o == nil || IsNil(o.Id) {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *InteractionDetailsAllOfCompany) HasId() bool {
	if o != nil && !IsNil(o.Id) {
		return true
	}

	return false
}

// SetId gets a reference to the given string and assigns it to the Id field.
func (o *InteractionDetailsAllOfCompany) SetId(v string) {
	o.Id = &v
}

// GetName returns the Name field value if set, zero value otherwise.
func (o *InteractionDetailsAllOfCompany) GetName() string {
	if o == nil || IsNil(o.Name) {
		var ret string
		return ret
	}
	return *o.Name
}

// GetNameOk returns a tuple with the Name field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetailsAllOfCompany) GetNameOk() (*string, bool) {
	if o == nil || IsNil(o.Name) {
		return nil, false
	}
	return o.Name, true
}

// HasName returns a boolean if a field has been set.
func (o *InteractionDetailsAllOfCompany) HasName() bool {
	if o != nil && !IsNil(o.Name) {
		return true
	}

	return false
}

// SetName gets a reference to the given string and assigns it to the Name field.
func (o *InteractionDetailsAllOfCompany) SetName(v string) {
	o.Name = &v
}

// GetWebsite returns the Website field value if set, zero value otherwise.
func (o *InteractionDetailsAllOfCompany) GetWebsite() string {
	if o == nil || IsNil(o.Website) {
		var ret string
		return ret
	}
	return *o.Website
}

// GetWebsiteOk returns a tuple with the Website field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetailsAllOfCompany) GetWebsiteOk() (*string, bool) {
	if o == nil || IsNil(o.Website) {
		return nil, false
	}
	return o.Website, true
}

// HasWebsite returns a boolean if a field has been set.
func (o *InteractionDetailsAllOfCompany) HasWebsite() bool {
	if o != nil && !IsNil(o.Website) {
		return true
	}

	return false
}

// SetWebsite gets a reference to the given string and assigns it to the Website field.
func (o *InteractionDetailsAllOfCompany) SetWebsite(v string) {
	o.Website = &v
}

// GetPhone returns the Phone field value if set, zero value otherwise.
func (o *InteractionDetailsAllOfCompany) GetPhone() string {
	if o == nil || IsNil(o.Phone) {
		var ret string
		return ret
	}
	return *o.Phone
}

// GetPhoneOk returns a tuple with the Phone field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *InteractionDetailsAllOfCompany) GetPhoneOk() (*string, bool) {
	if o == nil || IsNil(o.Phone) {
		return nil, false
	}
	return o.Phone, true
}

// HasPhone returns a boolean if a field has been set.
func (o *InteractionDetailsAllOfCompany) HasPhone() bool {
	if o != nil && !IsNil(o.Phone) {
		return true
	}

	return false
}

// SetPhone gets a reference to the given string and assigns it to the Phone field.
func (o *InteractionDetailsAllOfCompany) SetPhone(v string) {
	o.Phone = &v
}

func (o InteractionDetailsAllOfCompany) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o InteractionDetailsAllOfCompany) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Id) {
		toSerialize["id"] = o.Id
	}
	if !IsNil(o.Name) {
		toSerialize["name"] = o.Name
	}
	if !IsNil(o.Website) {
		toSerialize["website"] = o.Website
	}
	if !IsNil(o.Phone) {
		toSerialize["phone"] = o.Phone
	}
	return toSerialize, nil
}

type NullableInteractionDetailsAllOfCompany struct {
	value *InteractionDetailsAllOfCompany
	isSet bool
}

func (v NullableInteractionDetailsAllOfCompany) Get() *InteractionDetailsAllOfCompany {
	return v.value
}

func (v *NullableInteractionDetailsAllOfCompany) Set(val *InteractionDetailsAllOfCompany) {
	v.value = val
	v.isSet = true
}

func (v NullableInteractionDetailsAllOfCompany) IsSet() bool {
	return v.isSet
}

func (v *NullableInteractionDetailsAllOfCompany) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableInteractionDetailsAllOfCompany(val *InteractionDetailsAllOfCompany) *NullableInteractionDetailsAllOfCompany {
	return &NullableInteractionDetailsAllOfCompany{value: val, isSet: true}
}

func (v NullableInteractionDetailsAllOfCompany) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableInteractionDetailsAllOfCompany) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


