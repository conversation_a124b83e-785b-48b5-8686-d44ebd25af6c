# DocumentDetails

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Id** | Pointer to **string** |  | [optional] 
**Content** | Pointer to **string** |  | [optional] 
**Metadata** | Pointer to **map[string]interface{}** |  | [optional] 
**FilterTags** | Pointer to **[]string** |  | [optional] 
**SourceId** | Pointer to **string** |  | [optional] 
**CreatedAt** | Pointer to **time.Time** |  | [optional] 
**UpdatedAt** | Pointer to **time.Time** |  | [optional] 
**VectorEmbeddings** | Pointer to **[]float32** |  | [optional] 
**ProcessingStatus** | Pointer to **string** |  | [optional] 

## Methods

### NewDocumentDetails

`func NewDocumentDetails() *DocumentDetails`

NewDocumentDetails instantiates a new DocumentDetails object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDocumentDetailsWithDefaults

`func NewDocumentDetailsWithDefaults() *DocumentDetails`

NewDocumentDetailsWithDefaults instantiates a new DocumentDetails object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetId

`func (o *DocumentDetails) GetId() string`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *DocumentDetails) GetIdOk() (*string, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *DocumentDetails) SetId(v string)`

SetId sets Id field to given value.

### HasId

`func (o *DocumentDetails) HasId() bool`

HasId returns a boolean if a field has been set.

### GetContent

`func (o *DocumentDetails) GetContent() string`

GetContent returns the Content field if non-nil, zero value otherwise.

### GetContentOk

`func (o *DocumentDetails) GetContentOk() (*string, bool)`

GetContentOk returns a tuple with the Content field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetContent

`func (o *DocumentDetails) SetContent(v string)`

SetContent sets Content field to given value.

### HasContent

`func (o *DocumentDetails) HasContent() bool`

HasContent returns a boolean if a field has been set.

### GetMetadata

`func (o *DocumentDetails) GetMetadata() map[string]interface{}`

GetMetadata returns the Metadata field if non-nil, zero value otherwise.

### GetMetadataOk

`func (o *DocumentDetails) GetMetadataOk() (*map[string]interface{}, bool)`

GetMetadataOk returns a tuple with the Metadata field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMetadata

`func (o *DocumentDetails) SetMetadata(v map[string]interface{})`

SetMetadata sets Metadata field to given value.

### HasMetadata

`func (o *DocumentDetails) HasMetadata() bool`

HasMetadata returns a boolean if a field has been set.

### GetFilterTags

`func (o *DocumentDetails) GetFilterTags() []string`

GetFilterTags returns the FilterTags field if non-nil, zero value otherwise.

### GetFilterTagsOk

`func (o *DocumentDetails) GetFilterTagsOk() (*[]string, bool)`

GetFilterTagsOk returns a tuple with the FilterTags field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFilterTags

`func (o *DocumentDetails) SetFilterTags(v []string)`

SetFilterTags sets FilterTags field to given value.

### HasFilterTags

`func (o *DocumentDetails) HasFilterTags() bool`

HasFilterTags returns a boolean if a field has been set.

### GetSourceId

`func (o *DocumentDetails) GetSourceId() string`

GetSourceId returns the SourceId field if non-nil, zero value otherwise.

### GetSourceIdOk

`func (o *DocumentDetails) GetSourceIdOk() (*string, bool)`

GetSourceIdOk returns a tuple with the SourceId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetSourceId

`func (o *DocumentDetails) SetSourceId(v string)`

SetSourceId sets SourceId field to given value.

### HasSourceId

`func (o *DocumentDetails) HasSourceId() bool`

HasSourceId returns a boolean if a field has been set.

### GetCreatedAt

`func (o *DocumentDetails) GetCreatedAt() time.Time`

GetCreatedAt returns the CreatedAt field if non-nil, zero value otherwise.

### GetCreatedAtOk

`func (o *DocumentDetails) GetCreatedAtOk() (*time.Time, bool)`

GetCreatedAtOk returns a tuple with the CreatedAt field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreatedAt

`func (o *DocumentDetails) SetCreatedAt(v time.Time)`

SetCreatedAt sets CreatedAt field to given value.

### HasCreatedAt

`func (o *DocumentDetails) HasCreatedAt() bool`

HasCreatedAt returns a boolean if a field has been set.

### GetUpdatedAt

`func (o *DocumentDetails) GetUpdatedAt() time.Time`

GetUpdatedAt returns the UpdatedAt field if non-nil, zero value otherwise.

### GetUpdatedAtOk

`func (o *DocumentDetails) GetUpdatedAtOk() (*time.Time, bool)`

GetUpdatedAtOk returns a tuple with the UpdatedAt field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUpdatedAt

`func (o *DocumentDetails) SetUpdatedAt(v time.Time)`

SetUpdatedAt sets UpdatedAt field to given value.

### HasUpdatedAt

`func (o *DocumentDetails) HasUpdatedAt() bool`

HasUpdatedAt returns a boolean if a field has been set.

### GetVectorEmbeddings

`func (o *DocumentDetails) GetVectorEmbeddings() []float32`

GetVectorEmbeddings returns the VectorEmbeddings field if non-nil, zero value otherwise.

### GetVectorEmbeddingsOk

`func (o *DocumentDetails) GetVectorEmbeddingsOk() (*[]float32, bool)`

GetVectorEmbeddingsOk returns a tuple with the VectorEmbeddings field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetVectorEmbeddings

`func (o *DocumentDetails) SetVectorEmbeddings(v []float32)`

SetVectorEmbeddings sets VectorEmbeddings field to given value.

### HasVectorEmbeddings

`func (o *DocumentDetails) HasVectorEmbeddings() bool`

HasVectorEmbeddings returns a boolean if a field has been set.

### GetProcessingStatus

`func (o *DocumentDetails) GetProcessingStatus() string`

GetProcessingStatus returns the ProcessingStatus field if non-nil, zero value otherwise.

### GetProcessingStatusOk

`func (o *DocumentDetails) GetProcessingStatusOk() (*string, bool)`

GetProcessingStatusOk returns a tuple with the ProcessingStatus field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetProcessingStatus

`func (o *DocumentDetails) SetProcessingStatus(v string)`

SetProcessingStatus sets ProcessingStatus field to given value.

### HasProcessingStatus

`func (o *DocumentDetails) HasProcessingStatus() bool`

HasProcessingStatus returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


