# UserManagementApi

All URIs are relative to *https://api.crm.example.com/v1*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**usersProfileGet**](UserManagementApi.md#usersProfileGet) | **GET** /users/profile | Get user profile |
| [**usersProfilePut**](UserManagementApi.md#usersProfilePut) | **PUT** /users/profile | Update user profile |


<a id="usersProfileGet"></a>
# **usersProfileGet**
> UserProfile usersProfileGet()

Get user profile

Retrieve the current user&#39;s profile information

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.UserManagementApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    UserManagementApi apiInstance = new UserManagementApi(defaultClient);
    try {
      UserProfile result = apiInstance.usersProfileGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling UserManagementApi#usersProfileGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**UserProfile**](UserProfile.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | User profile |  -  |
| **401** | Not authenticated |  -  |

<a id="usersProfilePut"></a>
# **usersProfilePut**
> UserProfile usersProfilePut(userProfileUpdate)

Update user profile

Update the current user&#39;s profile information

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.UserManagementApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    UserManagementApi apiInstance = new UserManagementApi(defaultClient);
    UserProfileUpdate userProfileUpdate = new UserProfileUpdate(); // UserProfileUpdate | 
    try {
      UserProfile result = apiInstance.usersProfilePut(userProfileUpdate);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling UserManagementApi#usersProfilePut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **userProfileUpdate** | [**UserProfileUpdate**](UserProfileUpdate.md)|  | |

### Return type

[**UserProfile**](UserProfile.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Profile updated successfully |  -  |
| **400** | Invalid input |  -  |

