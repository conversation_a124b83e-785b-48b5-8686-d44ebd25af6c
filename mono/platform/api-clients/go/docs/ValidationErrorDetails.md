# ValidationErrorDetails

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**FieldErrors** | Pointer to **map[string][]string** |  | [optional] 

## Methods

### NewValidationErrorDetails

`func NewValidationErrorDetails() *ValidationErrorDetails`

NewValidationErrorDetails instantiates a new ValidationErrorDetails object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewValidationErrorDetailsWithDefaults

`func NewValidationErrorDetailsWithDefaults() *ValidationErrorDetails`

NewValidationErrorDetailsWithDefaults instantiates a new ValidationErrorDetails object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetFieldErrors

`func (o *ValidationErrorDetails) GetFieldErrors() map[string][]string`

GetFieldErrors returns the FieldErrors field if non-nil, zero value otherwise.

### GetFieldErrorsOk

`func (o *ValidationErrorDetails) GetFieldErrorsOk() (*map[string][]string, bool)`

GetFieldErrorsOk returns a tuple with the FieldErrors field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetFieldErrors

`func (o *ValidationErrorDetails) SetFieldErrors(v map[string][]string)`

SetFieldErrors sets FieldErrors field to given value.

### HasFieldErrors

`func (o *ValidationErrorDetails) HasFieldErrors() bool`

HasFieldErrors returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


