/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.api;

import org.openapitools.client.ApiException;
import org.openapitools.client.model.UserProfile;
import org.openapitools.client.model.UserProfileUpdate;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for UserManagementApi
 */
@Disabled
public class UserManagementApiTest {

    private final UserManagementApi api = new UserManagementApi();

    /**
     * Get user profile
     *
     * Retrieve the current user&#39;s profile information
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void usersProfileGetTest() throws ApiException {
        UserProfile response = api.usersProfileGet();
        // TODO: test validations
    }

    /**
     * Update user profile
     *
     * Update the current user&#39;s profile information
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void usersProfilePutTest() throws ApiException {
        UserProfileUpdate userProfileUpdate = null;
        UserProfile response = api.usersProfilePut(userProfileUpdate);
        // TODO: test validations
    }

}
