import React from 'react';
import { Link } from 'react-router-dom';
import { Plus } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';

interface TasksPageProps {
  filter?: 'my' | 'overdue';
}

export default function TasksPage({ filter }: TasksPageProps) {
  const { user } = useAuth();

  const getPageTitle = () => {
    switch (filter) {
      case 'my':
        return 'My Tasks';
      case 'overdue':
        return 'Overdue Tasks';
      default:
        return 'All Tasks';
    }
  };

  const getPageDescription = () => {
    switch (filter) {
      case 'my':
        return 'Tasks assigned to you across all projects';
      case 'overdue':
        return 'Tasks that are past their due date';
      default:
        return 'Manage and track tasks across all projects';
    }
  };

  const canCreateTask = user?.role === 'admin' || user?.role === 'project_manager' || user?.role === 'team_member';

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{getPageTitle()}</h1>
          <p className="text-gray-600 mt-1">{getPageDescription()}</p>
        </div>
        {canCreateTask && (
          <Button asChild>
            <Link to="/tasks/new">
              <Plus className="mr-2 h-4 w-4" />
              New Task
            </Link>
          </Button>
        )}
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">Core Project Pulse Tasks Integration</h3>
          <p className="mt-1 text-sm text-gray-500">
            Advanced task management with filtering, kanban boards, and progress tracking.
          </p>
          <p className="mt-2 text-xs text-gray-400">
            Backend server needs to be running for full functionality.
          </p>
        </div>
      </div>
    </div>
  );
}
