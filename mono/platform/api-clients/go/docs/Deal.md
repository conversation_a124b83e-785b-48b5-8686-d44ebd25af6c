# Deal

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Id** | Pointer to **string** |  | [optional] 
**Title** | Pointer to **string** |  | [optional] 
**Description** | Pointer to **string** |  | [optional] 
**EstimatedValue** | Pointer to **float64** |  | [optional] 
**CompanyId** | Pointer to **string** |  | [optional] 
**DealStageId** | Pointer to **string** |  | [optional] 
**ExpectedCloseDate** | Pointer to **string** |  | [optional] 
**CreatedBy** | Pointer to **string** |  | [optional] 
**CreatedAt** | Pointer to **time.Time** |  | [optional] 
**UpdatedAt** | Pointer to **time.Time** |  | [optional] 

## Methods

### NewDeal

`func NewDeal() *Deal`

NewDeal instantiates a new Deal object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDealWithDefaults

`func NewDealWithDefaults() *Deal`

NewDealWithDefaults instantiates a new Deal object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetId

`func (o *Deal) GetId() string`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *Deal) GetIdOk() (*string, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *Deal) SetId(v string)`

SetId sets Id field to given value.

### HasId

`func (o *Deal) HasId() bool`

HasId returns a boolean if a field has been set.

### GetTitle

`func (o *Deal) GetTitle() string`

GetTitle returns the Title field if non-nil, zero value otherwise.

### GetTitleOk

`func (o *Deal) GetTitleOk() (*string, bool)`

GetTitleOk returns a tuple with the Title field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTitle

`func (o *Deal) SetTitle(v string)`

SetTitle sets Title field to given value.

### HasTitle

`func (o *Deal) HasTitle() bool`

HasTitle returns a boolean if a field has been set.

### GetDescription

`func (o *Deal) GetDescription() string`

GetDescription returns the Description field if non-nil, zero value otherwise.

### GetDescriptionOk

`func (o *Deal) GetDescriptionOk() (*string, bool)`

GetDescriptionOk returns a tuple with the Description field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDescription

`func (o *Deal) SetDescription(v string)`

SetDescription sets Description field to given value.

### HasDescription

`func (o *Deal) HasDescription() bool`

HasDescription returns a boolean if a field has been set.

### GetEstimatedValue

`func (o *Deal) GetEstimatedValue() float64`

GetEstimatedValue returns the EstimatedValue field if non-nil, zero value otherwise.

### GetEstimatedValueOk

`func (o *Deal) GetEstimatedValueOk() (*float64, bool)`

GetEstimatedValueOk returns a tuple with the EstimatedValue field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetEstimatedValue

`func (o *Deal) SetEstimatedValue(v float64)`

SetEstimatedValue sets EstimatedValue field to given value.

### HasEstimatedValue

`func (o *Deal) HasEstimatedValue() bool`

HasEstimatedValue returns a boolean if a field has been set.

### GetCompanyId

`func (o *Deal) GetCompanyId() string`

GetCompanyId returns the CompanyId field if non-nil, zero value otherwise.

### GetCompanyIdOk

`func (o *Deal) GetCompanyIdOk() (*string, bool)`

GetCompanyIdOk returns a tuple with the CompanyId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCompanyId

`func (o *Deal) SetCompanyId(v string)`

SetCompanyId sets CompanyId field to given value.

### HasCompanyId

`func (o *Deal) HasCompanyId() bool`

HasCompanyId returns a boolean if a field has been set.

### GetDealStageId

`func (o *Deal) GetDealStageId() string`

GetDealStageId returns the DealStageId field if non-nil, zero value otherwise.

### GetDealStageIdOk

`func (o *Deal) GetDealStageIdOk() (*string, bool)`

GetDealStageIdOk returns a tuple with the DealStageId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDealStageId

`func (o *Deal) SetDealStageId(v string)`

SetDealStageId sets DealStageId field to given value.

### HasDealStageId

`func (o *Deal) HasDealStageId() bool`

HasDealStageId returns a boolean if a field has been set.

### GetExpectedCloseDate

`func (o *Deal) GetExpectedCloseDate() string`

GetExpectedCloseDate returns the ExpectedCloseDate field if non-nil, zero value otherwise.

### GetExpectedCloseDateOk

`func (o *Deal) GetExpectedCloseDateOk() (*string, bool)`

GetExpectedCloseDateOk returns a tuple with the ExpectedCloseDate field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetExpectedCloseDate

`func (o *Deal) SetExpectedCloseDate(v string)`

SetExpectedCloseDate sets ExpectedCloseDate field to given value.

### HasExpectedCloseDate

`func (o *Deal) HasExpectedCloseDate() bool`

HasExpectedCloseDate returns a boolean if a field has been set.

### GetCreatedBy

`func (o *Deal) GetCreatedBy() string`

GetCreatedBy returns the CreatedBy field if non-nil, zero value otherwise.

### GetCreatedByOk

`func (o *Deal) GetCreatedByOk() (*string, bool)`

GetCreatedByOk returns a tuple with the CreatedBy field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreatedBy

`func (o *Deal) SetCreatedBy(v string)`

SetCreatedBy sets CreatedBy field to given value.

### HasCreatedBy

`func (o *Deal) HasCreatedBy() bool`

HasCreatedBy returns a boolean if a field has been set.

### GetCreatedAt

`func (o *Deal) GetCreatedAt() time.Time`

GetCreatedAt returns the CreatedAt field if non-nil, zero value otherwise.

### GetCreatedAtOk

`func (o *Deal) GetCreatedAtOk() (*time.Time, bool)`

GetCreatedAtOk returns a tuple with the CreatedAt field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreatedAt

`func (o *Deal) SetCreatedAt(v time.Time)`

SetCreatedAt sets CreatedAt field to given value.

### HasCreatedAt

`func (o *Deal) HasCreatedAt() bool`

HasCreatedAt returns a boolean if a field has been set.

### GetUpdatedAt

`func (o *Deal) GetUpdatedAt() time.Time`

GetUpdatedAt returns the UpdatedAt field if non-nil, zero value otherwise.

### GetUpdatedAtOk

`func (o *Deal) GetUpdatedAtOk() (*time.Time, bool)`

GetUpdatedAtOk returns a tuple with the UpdatedAt field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUpdatedAt

`func (o *Deal) SetUpdatedAt(v time.Time)`

SetUpdatedAt sets UpdatedAt field to given value.

### HasUpdatedAt

`func (o *Deal) HasUpdatedAt() bool`

HasUpdatedAt returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


