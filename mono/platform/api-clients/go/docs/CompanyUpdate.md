# CompanyUpdate

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Name** | Pointer to **string** |  | [optional] 
**Website** | Pointer to **string** |  | [optional] 
**Phone** | Pointer to **string** |  | [optional] 
**Address** | Pointer to **string** |  | [optional] 
**Notes** | Pointer to **string** |  | [optional] 
**CompanyStatusId** | Pointer to **string** |  | [optional] 

## Methods

### NewCompanyUpdate

`func NewCompanyUpdate() *CompanyUpdate`

NewCompanyUpdate instantiates a new CompanyUpdate object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewCompanyUpdateWithDefaults

`func NewCompanyUpdateWithDefaults() *CompanyUpdate`

NewCompanyUpdateWithDefaults instantiates a new CompanyUpdate object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetName

`func (o *CompanyUpdate) GetName() string`

GetName returns the Name field if non-nil, zero value otherwise.

### GetNameOk

`func (o *CompanyUpdate) GetNameOk() (*string, bool)`

GetNameOk returns a tuple with the Name field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetName

`func (o *CompanyUpdate) SetName(v string)`

SetName sets Name field to given value.

### HasName

`func (o *CompanyUpdate) HasName() bool`

HasName returns a boolean if a field has been set.

### GetWebsite

`func (o *CompanyUpdate) GetWebsite() string`

GetWebsite returns the Website field if non-nil, zero value otherwise.

### GetWebsiteOk

`func (o *CompanyUpdate) GetWebsiteOk() (*string, bool)`

GetWebsiteOk returns a tuple with the Website field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetWebsite

`func (o *CompanyUpdate) SetWebsite(v string)`

SetWebsite sets Website field to given value.

### HasWebsite

`func (o *CompanyUpdate) HasWebsite() bool`

HasWebsite returns a boolean if a field has been set.

### GetPhone

`func (o *CompanyUpdate) GetPhone() string`

GetPhone returns the Phone field if non-nil, zero value otherwise.

### GetPhoneOk

`func (o *CompanyUpdate) GetPhoneOk() (*string, bool)`

GetPhoneOk returns a tuple with the Phone field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPhone

`func (o *CompanyUpdate) SetPhone(v string)`

SetPhone sets Phone field to given value.

### HasPhone

`func (o *CompanyUpdate) HasPhone() bool`

HasPhone returns a boolean if a field has been set.

### GetAddress

`func (o *CompanyUpdate) GetAddress() string`

GetAddress returns the Address field if non-nil, zero value otherwise.

### GetAddressOk

`func (o *CompanyUpdate) GetAddressOk() (*string, bool)`

GetAddressOk returns a tuple with the Address field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetAddress

`func (o *CompanyUpdate) SetAddress(v string)`

SetAddress sets Address field to given value.

### HasAddress

`func (o *CompanyUpdate) HasAddress() bool`

HasAddress returns a boolean if a field has been set.

### GetNotes

`func (o *CompanyUpdate) GetNotes() string`

GetNotes returns the Notes field if non-nil, zero value otherwise.

### GetNotesOk

`func (o *CompanyUpdate) GetNotesOk() (*string, bool)`

GetNotesOk returns a tuple with the Notes field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetNotes

`func (o *CompanyUpdate) SetNotes(v string)`

SetNotes sets Notes field to given value.

### HasNotes

`func (o *CompanyUpdate) HasNotes() bool`

HasNotes returns a boolean if a field has been set.

### GetCompanyStatusId

`func (o *CompanyUpdate) GetCompanyStatusId() string`

GetCompanyStatusId returns the CompanyStatusId field if non-nil, zero value otherwise.

### GetCompanyStatusIdOk

`func (o *CompanyUpdate) GetCompanyStatusIdOk() (*string, bool)`

GetCompanyStatusIdOk returns a tuple with the CompanyStatusId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCompanyStatusId

`func (o *CompanyUpdate) SetCompanyStatusId(v string)`

SetCompanyStatusId sets CompanyStatusId field to given value.

### HasCompanyStatusId

`func (o *CompanyUpdate) HasCompanyStatusId() bool`

HasCompanyStatusId returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


