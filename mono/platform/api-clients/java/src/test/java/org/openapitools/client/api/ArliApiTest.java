/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.api;

import org.openapitools.client.ApiException;
import org.openapitools.client.model.ArliDocumentsGet200Response;
import org.openapitools.client.model.ArliDocumentsVectorizePost200Response;
import org.openapitools.client.model.ArliDocumentsVectorizePostRequest;
import org.openapitools.client.model.Document;
import org.openapitools.client.model.DocumentCreate;
import org.openapitools.client.model.DocumentDetails;
import org.openapitools.client.model.DocumentUpdate;
import org.openapitools.client.model.FilterTag;
import org.openapitools.client.model.FilterTagCreate;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for ArliApi
 */
@Disabled
public class ArliApiTest {

    private final ArliApi api = new ArliApi();

    /**
     * List documents
     *
     * Retrieve a list of processed documents
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void arliDocumentsGetTest() throws ApiException {
        List<String> filterTags = null;
        Object metadataFilter = null;
        Integer limit = null;
        Integer offset = null;
        ArliDocumentsGet200Response response = api.arliDocumentsGet(filterTags, metadataFilter, limit, offset);
        // TODO: test validations
    }

    /**
     * Delete document
     *
     * Delete a document
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void arliDocumentsIdDeleteTest() throws ApiException {
        UUID id = null;
        api.arliDocumentsIdDelete(id);
        // TODO: test validations
    }

    /**
     * Get document
     *
     * Retrieve a specific document by ID
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void arliDocumentsIdGetTest() throws ApiException {
        UUID id = null;
        DocumentDetails response = api.arliDocumentsIdGet(id);
        // TODO: test validations
    }

    /**
     * Update document
     *
     * Update an existing document
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void arliDocumentsIdPutTest() throws ApiException {
        UUID id = null;
        DocumentUpdate documentUpdate = null;
        Document response = api.arliDocumentsIdPut(id, documentUpdate);
        // TODO: test validations
    }

    /**
     * Create document
     *
     * Create a new document for processing
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void arliDocumentsPostTest() throws ApiException {
        DocumentCreate documentCreate = null;
        Document response = api.arliDocumentsPost(documentCreate);
        // TODO: test validations
    }

    /**
     * Vectorize document
     *
     * Process a document through the AI vectorization pipeline
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void arliDocumentsVectorizePostTest() throws ApiException {
        ArliDocumentsVectorizePostRequest arliDocumentsVectorizePostRequest = null;
        ArliDocumentsVectorizePost200Response response = api.arliDocumentsVectorizePost(arliDocumentsVectorizePostRequest);
        // TODO: test validations
    }

    /**
     * List filter tags
     *
     * Retrieve all available filter tags
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void arliFilterTagsGetTest() throws ApiException {
        List<FilterTag> response = api.arliFilterTagsGet();
        // TODO: test validations
    }

    /**
     * Create filter tag
     *
     * Create a new filter tag
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void arliFilterTagsPostTest() throws ApiException {
        FilterTagCreate filterTagCreate = null;
        FilterTag response = api.arliFilterTagsPost(filterTagCreate);
        // TODO: test validations
    }

}
