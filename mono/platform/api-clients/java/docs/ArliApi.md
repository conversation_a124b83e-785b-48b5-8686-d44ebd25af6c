# ArliApi

All URIs are relative to *https://api.crm.example.com/v1*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**arliDocumentsGet**](ArliApi.md#arliDocumentsGet) | **GET** /arli/documents | List documents |
| [**arliDocumentsIdDelete**](ArliApi.md#arliDocumentsIdDelete) | **DELETE** /arli/documents/{id} | Delete document |
| [**arliDocumentsIdGet**](ArliApi.md#arliDocumentsIdGet) | **GET** /arli/documents/{id} | Get document |
| [**arliDocumentsIdPut**](ArliApi.md#arliDocumentsIdPut) | **PUT** /arli/documents/{id} | Update document |
| [**arliDocumentsPost**](ArliApi.md#arliDocumentsPost) | **POST** /arli/documents | Create document |
| [**arliDocumentsVectorizePost**](ArliApi.md#arliDocumentsVectorizePost) | **POST** /arli/documents/vectorize | Vectorize document |
| [**arliFilterTagsGet**](ArliApi.md#arliFilterTagsGet) | **GET** /arli/filter-tags | List filter tags |
| [**arliFilterTagsPost**](ArliApi.md#arliFilterTagsPost) | **POST** /arli/filter-tags | Create filter tag |


<a id="arliDocumentsGet"></a>
# **arliDocumentsGet**
> ArliDocumentsGet200Response arliDocumentsGet(filterTags, metadataFilter, limit, offset)

List documents

Retrieve a list of processed documents

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.ArliApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    ArliApi apiInstance = new ArliApi(defaultClient);
    List<String> filterTags = Arrays.asList(); // List<String> | Filter by tags
    Object metadataFilter = null; // Object | Filter by metadata properties
    Integer limit = 20; // Integer | 
    Integer offset = 0; // Integer | 
    try {
      ArliDocumentsGet200Response result = apiInstance.arliDocumentsGet(filterTags, metadataFilter, limit, offset);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ArliApi#arliDocumentsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **filterTags** | [**List&lt;String&gt;**](String.md)| Filter by tags | [optional] |
| **metadataFilter** | [**Object**](.md)| Filter by metadata properties | [optional] |
| **limit** | **Integer**|  | [optional] [default to 20] |
| **offset** | **Integer**|  | [optional] [default to 0] |

### Return type

[**ArliDocumentsGet200Response**](ArliDocumentsGet200Response.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | List of documents |  -  |

<a id="arliDocumentsIdDelete"></a>
# **arliDocumentsIdDelete**
> arliDocumentsIdDelete(id)

Delete document

Delete a document

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.ArliApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    ArliApi apiInstance = new ArliApi(defaultClient);
    UUID id = UUID.randomUUID(); // UUID | 
    try {
      apiInstance.arliDocumentsIdDelete(id);
    } catch (ApiException e) {
      System.err.println("Exception when calling ArliApi#arliDocumentsIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **id** | **UUID**|  | |

### Return type

null (empty response body)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **204** | Document deleted successfully |  -  |
| **404** | Document not found |  -  |

<a id="arliDocumentsIdGet"></a>
# **arliDocumentsIdGet**
> DocumentDetails arliDocumentsIdGet(id)

Get document

Retrieve a specific document by ID

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.ArliApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    ArliApi apiInstance = new ArliApi(defaultClient);
    UUID id = UUID.randomUUID(); // UUID | 
    try {
      DocumentDetails result = apiInstance.arliDocumentsIdGet(id);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ArliApi#arliDocumentsIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **id** | **UUID**|  | |

### Return type

[**DocumentDetails**](DocumentDetails.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Document details |  -  |
| **404** | Document not found |  -  |

<a id="arliDocumentsIdPut"></a>
# **arliDocumentsIdPut**
> Document arliDocumentsIdPut(id, documentUpdate)

Update document

Update an existing document

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.ArliApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    ArliApi apiInstance = new ArliApi(defaultClient);
    UUID id = UUID.randomUUID(); // UUID | 
    DocumentUpdate documentUpdate = new DocumentUpdate(); // DocumentUpdate | 
    try {
      Document result = apiInstance.arliDocumentsIdPut(id, documentUpdate);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ArliApi#arliDocumentsIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **id** | **UUID**|  | |
| **documentUpdate** | [**DocumentUpdate**](DocumentUpdate.md)|  | |

### Return type

[**Document**](Document.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Document updated successfully |  -  |
| **404** | Document not found |  -  |

<a id="arliDocumentsPost"></a>
# **arliDocumentsPost**
> Document arliDocumentsPost(documentCreate)

Create document

Create a new document for processing

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.ArliApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    ArliApi apiInstance = new ArliApi(defaultClient);
    DocumentCreate documentCreate = new DocumentCreate(); // DocumentCreate | 
    try {
      Document result = apiInstance.arliDocumentsPost(documentCreate);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ArliApi#arliDocumentsPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **documentCreate** | [**DocumentCreate**](DocumentCreate.md)|  | |

### Return type

[**Document**](Document.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **201** | Document created successfully |  -  |
| **400** | Invalid input |  -  |

<a id="arliDocumentsVectorizePost"></a>
# **arliDocumentsVectorizePost**
> ArliDocumentsVectorizePost200Response arliDocumentsVectorizePost(arliDocumentsVectorizePostRequest)

Vectorize document

Process a document through the AI vectorization pipeline

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.ArliApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    ArliApi apiInstance = new ArliApi(defaultClient);
    ArliDocumentsVectorizePostRequest arliDocumentsVectorizePostRequest = new ArliDocumentsVectorizePostRequest(); // ArliDocumentsVectorizePostRequest | 
    try {
      ArliDocumentsVectorizePost200Response result = apiInstance.arliDocumentsVectorizePost(arliDocumentsVectorizePostRequest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ArliApi#arliDocumentsVectorizePost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **arliDocumentsVectorizePostRequest** | [**ArliDocumentsVectorizePostRequest**](ArliDocumentsVectorizePostRequest.md)|  | |

### Return type

[**ArliDocumentsVectorizePost200Response**](ArliDocumentsVectorizePost200Response.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Document vectorized successfully |  -  |
| **400** | Invalid input |  -  |
| **500** | Vectorization failed |  -  |

<a id="arliFilterTagsGet"></a>
# **arliFilterTagsGet**
> List&lt;FilterTag&gt; arliFilterTagsGet()

List filter tags

Retrieve all available filter tags

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.ArliApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    ArliApi apiInstance = new ArliApi(defaultClient);
    try {
      List<FilterTag> result = apiInstance.arliFilterTagsGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ArliApi#arliFilterTagsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;FilterTag&gt;**](FilterTag.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | List of filter tags |  -  |

<a id="arliFilterTagsPost"></a>
# **arliFilterTagsPost**
> FilterTag arliFilterTagsPost(filterTagCreate)

Create filter tag

Create a new filter tag

### Example
```java
// Import classes:
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.Configuration;
import org.openapitools.client.auth.*;
import org.openapitools.client.models.*;
import org.openapitools.client.api.ArliApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("https://api.crm.example.com/v1");
    
    // Configure HTTP bearer authorization: bearerAuth
    HttpBearerAuth bearerAuth = (HttpBearerAuth) defaultClient.getAuthentication("bearerAuth");
    bearerAuth.setBearerToken("BEARER TOKEN");

    ArliApi apiInstance = new ArliApi(defaultClient);
    FilterTagCreate filterTagCreate = new FilterTagCreate(); // FilterTagCreate | 
    try {
      FilterTag result = apiInstance.arliFilterTagsPost(filterTagCreate);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ArliApi#arliFilterTagsPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **filterTagCreate** | [**FilterTagCreate**](FilterTagCreate.md)|  | |

### Return type

[**FilterTag**](FilterTag.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **201** | Filter tag created successfully |  -  |

