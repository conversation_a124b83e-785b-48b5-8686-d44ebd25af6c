/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
)

// checks if the ArliDocumentsGet200Response type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ArliDocumentsGet200Response{}

// ArliDocumentsGet200Response struct for ArliDocumentsGet200Response
type ArliDocumentsGet200Response struct {
	Documents []Document `json:"documents,omitempty"`
	TotalCount *int32 `json:"total_count,omitempty"`
	HasMore *bool `json:"has_more,omitempty"`
}

// NewArliDocumentsGet200Response instantiates a new ArliDocumentsGet200Response object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewArliDocumentsGet200Response() *ArliDocumentsGet200Response {
	this := ArliDocumentsGet200Response{}
	return &this
}

// NewArliDocumentsGet200ResponseWithDefaults instantiates a new ArliDocumentsGet200Response object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewArliDocumentsGet200ResponseWithDefaults() *ArliDocumentsGet200Response {
	this := ArliDocumentsGet200Response{}
	return &this
}

// GetDocuments returns the Documents field value if set, zero value otherwise.
func (o *ArliDocumentsGet200Response) GetDocuments() []Document {
	if o == nil || IsNil(o.Documents) {
		var ret []Document
		return ret
	}
	return o.Documents
}

// GetDocumentsOk returns a tuple with the Documents field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ArliDocumentsGet200Response) GetDocumentsOk() ([]Document, bool) {
	if o == nil || IsNil(o.Documents) {
		return nil, false
	}
	return o.Documents, true
}

// HasDocuments returns a boolean if a field has been set.
func (o *ArliDocumentsGet200Response) HasDocuments() bool {
	if o != nil && !IsNil(o.Documents) {
		return true
	}

	return false
}

// SetDocuments gets a reference to the given []Document and assigns it to the Documents field.
func (o *ArliDocumentsGet200Response) SetDocuments(v []Document) {
	o.Documents = v
}

// GetTotalCount returns the TotalCount field value if set, zero value otherwise.
func (o *ArliDocumentsGet200Response) GetTotalCount() int32 {
	if o == nil || IsNil(o.TotalCount) {
		var ret int32
		return ret
	}
	return *o.TotalCount
}

// GetTotalCountOk returns a tuple with the TotalCount field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ArliDocumentsGet200Response) GetTotalCountOk() (*int32, bool) {
	if o == nil || IsNil(o.TotalCount) {
		return nil, false
	}
	return o.TotalCount, true
}

// HasTotalCount returns a boolean if a field has been set.
func (o *ArliDocumentsGet200Response) HasTotalCount() bool {
	if o != nil && !IsNil(o.TotalCount) {
		return true
	}

	return false
}

// SetTotalCount gets a reference to the given int32 and assigns it to the TotalCount field.
func (o *ArliDocumentsGet200Response) SetTotalCount(v int32) {
	o.TotalCount = &v
}

// GetHasMore returns the HasMore field value if set, zero value otherwise.
func (o *ArliDocumentsGet200Response) GetHasMore() bool {
	if o == nil || IsNil(o.HasMore) {
		var ret bool
		return ret
	}
	return *o.HasMore
}

// GetHasMoreOk returns a tuple with the HasMore field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ArliDocumentsGet200Response) GetHasMoreOk() (*bool, bool) {
	if o == nil || IsNil(o.HasMore) {
		return nil, false
	}
	return o.HasMore, true
}

// HasHasMore returns a boolean if a field has been set.
func (o *ArliDocumentsGet200Response) HasHasMore() bool {
	if o != nil && !IsNil(o.HasMore) {
		return true
	}

	return false
}

// SetHasMore gets a reference to the given bool and assigns it to the HasMore field.
func (o *ArliDocumentsGet200Response) SetHasMore(v bool) {
	o.HasMore = &v
}

func (o ArliDocumentsGet200Response) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ArliDocumentsGet200Response) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Documents) {
		toSerialize["documents"] = o.Documents
	}
	if !IsNil(o.TotalCount) {
		toSerialize["total_count"] = o.TotalCount
	}
	if !IsNil(o.HasMore) {
		toSerialize["has_more"] = o.HasMore
	}
	return toSerialize, nil
}

type NullableArliDocumentsGet200Response struct {
	value *ArliDocumentsGet200Response
	isSet bool
}

func (v NullableArliDocumentsGet200Response) Get() *ArliDocumentsGet200Response {
	return v.value
}

func (v *NullableArliDocumentsGet200Response) Set(val *ArliDocumentsGet200Response) {
	v.value = val
	v.isSet = true
}

func (v NullableArliDocumentsGet200Response) IsSet() bool {
	return v.isSet
}

func (v *NullableArliDocumentsGet200Response) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableArliDocumentsGet200Response(val *ArliDocumentsGet200Response) *NullableArliDocumentsGet200Response {
	return &NullableArliDocumentsGet200Response{value: val, isSet: true}
}

func (v NullableArliDocumentsGet200Response) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableArliDocumentsGet200Response) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


