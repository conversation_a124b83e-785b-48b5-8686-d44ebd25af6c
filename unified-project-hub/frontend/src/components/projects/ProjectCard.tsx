import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Calendar, 
  Users, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Building, 
  User,
  MoreHorizontal,
  Archive,
  Edit,
  Trash2
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { formatDate, getStatusColor, getPriorityColor } from '@/lib/utils';

interface ProjectCardProps {
  project: {
    id: string;
    name: string;
    description?: string;
    status: string;
    priority_level: string;
    company_name: string;
    start_date?: string;
    end_date?: string;
    progress_percentage?: number;
    calculated_progress?: number;
    days_remaining?: number;
    is_overdue?: boolean;
    project_lead?: {
      id: string;
      name: string;
      avatar_url?: string;
    };
    team_members?: Array<{
      user: {
        id: string;
        name: string;
        avatar_url?: string;
      };
    }>;
    task_summary?: {
      total: number;
      completed: number;
      in_progress: number;
      to_do: number;
    };
    tags?: string[];
    is_customer_visible?: boolean;
  };
  onEdit?: (project: any) => void;
  onArchive?: (project: any) => void;
  onDelete?: (project: any) => void;
  showActions?: boolean;
}

export function ProjectCard({ 
  project, 
  onEdit, 
  onArchive, 
  onDelete, 
  showActions = true 
}: ProjectCardProps) {
  const progress = project.calculated_progress ?? project.progress_percentage ?? 0;
  const isOverdue = project.is_overdue && project.status !== 'completed';
  
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'default';
      case 'in_progress':
        return 'secondary';
      case 'archived':
        return 'outline';
      case 'on_hold':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case 'P0':
        return 'destructive';
      case 'P1':
        return 'destructive';
      case 'P2':
        return 'default';
      case 'P3':
        return 'secondary';
      case 'P4':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const formatStatusLabel = (status: string) => {
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <Card className={`hover:shadow-md transition-shadow ${isOverdue ? 'border-red-200' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg font-semibold truncate">
              <Link 
                to={`/projects/${project.id}`}
                className="hover:text-primary transition-colors"
              >
                {project.name}
              </Link>
            </CardTitle>
            {project.description && (
              <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                {project.description}
              </p>
            )}
          </div>
          {showActions && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onEdit?.(project)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onArchive?.(project)}>
                  <Archive className="mr-2 h-4 w-4" />
                  {project.status === 'archived' ? 'Unarchive' : 'Archive'}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => onDelete?.(project)}
                  className="text-destructive"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>

        {/* Status and Priority Badges */}
        <div className="flex items-center gap-2 mt-2">
          <Badge variant={getStatusBadgeVariant(project.status)}>
            {formatStatusLabel(project.status)}
          </Badge>
          <Badge variant={getPriorityBadgeVariant(project.priority_level)}>
            {project.priority_level}
          </Badge>
          {isOverdue && (
            <Badge variant="destructive" className="flex items-center gap-1">
              <AlertTriangle className="h-3 w-3" />
              Overdue
            </Badge>
          )}
          {project.is_customer_visible && (
            <Badge variant="outline" className="text-xs">
              Customer Visible
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span className="font-medium">{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
          {project.task_summary && (
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{project.task_summary.completed} of {project.task_summary.total} tasks completed</span>
              {project.task_summary.in_progress > 0 && (
                <span>{project.task_summary.in_progress} in progress</span>
              )}
            </div>
          )}
        </div>

        {/* Company and Dates */}
        <div className="space-y-2 text-sm">
          <div className="flex items-center gap-2">
            <Building className="h-4 w-4 text-muted-foreground" />
            <span>{project.company_name}</span>
          </div>
          
          {(project.start_date || project.end_date) && (
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>
                {project.start_date && formatDate(project.start_date)}
                {project.start_date && project.end_date && ' - '}
                {project.end_date && formatDate(project.end_date)}
              </span>
            </div>
          )}

          {project.days_remaining !== null && project.days_remaining !== undefined && (
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className={isOverdue ? 'text-red-600' : ''}>
                {isOverdue 
                  ? `${Math.abs(project.days_remaining)} days overdue`
                  : `${project.days_remaining} days remaining`
                }
              </span>
            </div>
          )}
        </div>

        {/* Team Members */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">
              {project.project_lead?.name || 'No lead assigned'}
            </span>
          </div>
          
          {project.team_members && project.team_members.length > 0 && (
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <div className="flex -space-x-2">
                {project.team_members.slice(0, 3).map((member, index) => (
                  <Avatar key={member.user.id} className="h-6 w-6 border-2 border-background">
                    <AvatarImage src={member.user.avatar_url} />
                    <AvatarFallback className="text-xs">
                      {member.user.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                ))}
                {project.team_members.length > 3 && (
                  <div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center">
                    <span className="text-xs font-medium">
                      +{project.team_members.length - 3}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Tags */}
        {project.tags && project.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {project.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {project.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{project.tags.length - 3} more
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
