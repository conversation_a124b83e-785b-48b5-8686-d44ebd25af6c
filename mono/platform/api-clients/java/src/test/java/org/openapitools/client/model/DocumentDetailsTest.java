/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

/**
 * Model tests for DocumentDetails
 */
public class DocumentDetailsTest {
    private final DocumentDetails model = new DocumentDetails();

    /**
     * Model tests for DocumentDetails
     */
    @Test
    public void testDocumentDetails() {
        // TODO: test DocumentDetails
    }

    /**
     * Test the property 'id'
     */
    @Test
    public void idTest() {
        // TODO: test id
    }

    /**
     * Test the property 'content'
     */
    @Test
    public void contentTest() {
        // TODO: test content
    }

    /**
     * Test the property 'metadata'
     */
    @Test
    public void metadataTest() {
        // TODO: test metadata
    }

    /**
     * Test the property 'filterTags'
     */
    @Test
    public void filterTagsTest() {
        // TODO: test filterTags
    }

    /**
     * Test the property 'sourceId'
     */
    @Test
    public void sourceIdTest() {
        // TODO: test sourceId
    }

    /**
     * Test the property 'createdAt'
     */
    @Test
    public void createdAtTest() {
        // TODO: test createdAt
    }

    /**
     * Test the property 'updatedAt'
     */
    @Test
    public void updatedAtTest() {
        // TODO: test updatedAt
    }

    /**
     * Test the property 'vectorEmbeddings'
     */
    @Test
    public void vectorEmbeddingsTest() {
        // TODO: test vectorEmbeddings
    }

    /**
     * Test the property 'processingStatus'
     */
    @Test
    public void processingStatusTest() {
        // TODO: test processingStatus
    }

}
