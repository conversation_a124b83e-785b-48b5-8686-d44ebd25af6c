import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  MoreHorizontal, 
  Calendar, 
  User, 
  AlertTriangle,
  Building,
  UserCheck,
  Edit,
  Trash2,
  Flag,
  Layers,
  Clock
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { formatDate } from '@/lib/utils';

interface TaskListViewProps {
  tasks: any[];
  onEdit?: (task: any) => void;
  onDelete?: (task: any) => void;
  onAssign?: (task: any) => void;
  onUpdateStatus?: (task: any, status: string) => void;
  onUpdatePriority?: (task: any, priority: string) => void;
  showActions?: boolean;
  showProject?: boolean;
}

export function TaskListView({
  tasks,
  onEdit,
  onDelete,
  onAssign,
  onUpdateStatus,
  onUpdatePriority,
  showActions = true,
  showProject = true
}: TaskListViewProps) {
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'done':
        return 'default';
      case 'in_progress':
        return 'secondary';
      case 'in_review':
        return 'outline';
      case 'blocked':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case 'P0':
        return 'destructive';
      case 'P1':
        return 'destructive';
      case 'P2':
        return 'default';
      case 'P3':
        return 'secondary';
      case 'P4':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const getEffortColor = (effort: string) => {
    switch (effort) {
      case 'XS':
        return 'text-green-600';
      case 'S':
        return 'text-blue-600';
      case 'M':
        return 'text-yellow-600';
      case 'L':
        return 'text-orange-600';
      case 'XL':
        return 'text-red-600';
      case 'XXL':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatStatusLabel = (status: string) => {
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <div className="border rounded-lg">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Task</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Priority</TableHead>
            <TableHead>Effort</TableHead>
            <TableHead>Assignee</TableHead>
            {showProject && <TableHead>Project</TableHead>}
            <TableHead>Due Date</TableHead>
            <TableHead>Progress</TableHead>
            {showActions && <TableHead className="w-[50px]"></TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          {tasks.map((task) => {
            const isOverdue = task.is_overdue && task.status !== 'done';
            
            return (
              <TableRow key={task.id} className={isOverdue ? 'bg-red-50' : ''}>
                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      {task.parent_task && (
                        <Layers className="h-3 w-3 text-muted-foreground" />
                      )}
                      <Link 
                        to={`/tasks/${task.id}`}
                        className="font-medium hover:text-primary transition-colors"
                      >
                        {task.name}
                      </Link>
                    </div>
                    {task.description && (
                      <p className="text-sm text-muted-foreground line-clamp-1">
                        {task.description}
                      </p>
                    )}
                    {task.parent_task && (
                      <p className="text-xs text-muted-foreground">
                        Subtask of: {task.parent_task.name}
                      </p>
                    )}
                    {task.tags && task.tags.length > 0 && (
                      <div className="flex gap-1">
                        {task.tags.slice(0, 2).map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {task.tags.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{task.tags.length - 2}
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Badge variant={getStatusBadgeVariant(task.status)}>
                      {formatStatusLabel(task.status)}
                    </Badge>
                    {isOverdue && (
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  <Badge variant={getPriorityBadgeVariant(task.priority_level)}>
                    <Flag className="h-3 w-3 mr-1" />
                    {task.priority_level}
                  </Badge>
                </TableCell>
                
                <TableCell>
                  <Badge variant="outline" className={getEffortColor(task.effort_estimate)}>
                    {task.effort_estimate}
                  </Badge>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center gap-2">
                    {task.assignee ? (
                      <>
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={task.assignee.avatar_url} />
                          <AvatarFallback className="text-xs">
                            {task.assignee.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <span className="truncate">{task.assignee.name}</span>
                      </>
                    ) : (
                      <span className="text-muted-foreground">Unassigned</span>
                    )}
                  </div>
                </TableCell>
                
                {showProject && (
                  <TableCell>
                    {task.project ? (
                      <div className="space-y-1">
                        <Link 
                          to={`/projects/${task.project.id}`}
                          className="text-primary hover:underline text-sm"
                        >
                          {task.project.name}
                        </Link>
                        <p className="text-xs text-muted-foreground">
                          {task.project.company_name}
                        </p>
                      </div>
                    ) : (
                      <span className="text-muted-foreground">No project</span>
                    )}
                  </TableCell>
                )}
                
                <TableCell>
                  <div className="space-y-1">
                    {task.due_date && (
                      <div className="flex items-center gap-1 text-sm">
                        <Calendar className="h-3 w-3" />
                        <span className={isOverdue ? 'text-red-600' : ''}>
                          {formatDate(task.due_date)}
                        </span>
                      </div>
                    )}
                    {task.days_remaining !== null && task.days_remaining !== undefined && (
                      <div className={`text-xs flex items-center gap-1 ${isOverdue ? 'text-red-600' : 'text-muted-foreground'}`}>
                        <Clock className="h-3 w-3" />
                        {isOverdue 
                          ? `${Math.abs(task.days_remaining)} days overdue`
                          : `${task.days_remaining} days left`
                        }
                      </div>
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  {task.sub_task_summary && task.sub_task_summary.total > 0 ? (
                    <div className="space-y-1 min-w-[120px]">
                      <div className="flex justify-between text-sm">
                        <span>{task.sub_task_summary.progress_percentage}%</span>
                        <span className="text-muted-foreground">
                          {task.sub_task_summary.completed}/{task.sub_task_summary.total}
                        </span>
                      </div>
                      <Progress value={task.sub_task_summary.progress_percentage} className="h-2" />
                    </div>
                  ) : (
                    <span className="text-muted-foreground text-sm">No subtasks</span>
                  )}
                </TableCell>
                
                {showActions && (
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEdit?.(task)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onAssign?.(task)}>
                          <UserCheck className="mr-2 h-4 w-4" />
                          Assign
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => onDelete?.(task)}
                          className="text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                )}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
