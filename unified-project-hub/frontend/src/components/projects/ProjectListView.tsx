import React from 'react';
import { Link } from 'react-router-dom';
import { 
  MoreHorizontal, 
  Calendar, 
  Users, 
  AlertTriangle,
  Building,
  User,
  Archive,
  Edit,
  Trash2
} from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { formatDate } from '@/lib/utils';

interface ProjectListViewProps {
  projects: any[];
  onEdit?: (project: any) => void;
  onArchive?: (project: any) => void;
  onDelete?: (project: any) => void;
  showActions?: boolean;
}

export function ProjectListView({
  projects,
  onEdit,
  onArchive,
  onDelete,
  showActions = true
}: ProjectListViewProps) {
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'default';
      case 'in_progress':
        return 'secondary';
      case 'archived':
        return 'outline';
      case 'on_hold':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case 'P0':
        return 'destructive';
      case 'P1':
        return 'destructive';
      case 'P2':
        return 'default';
      case 'P3':
        return 'secondary';
      case 'P4':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const formatStatusLabel = (status: string) => {
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <div className="border rounded-lg">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Project</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Priority</TableHead>
            <TableHead>Progress</TableHead>
            <TableHead>Company</TableHead>
            <TableHead>Lead</TableHead>
            <TableHead>Team</TableHead>
            <TableHead>Due Date</TableHead>
            {showActions && <TableHead className="w-[50px]"></TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          {projects.map((project) => {
            const progress = project.calculated_progress ?? project.progress_percentage ?? 0;
            const isOverdue = project.is_overdue && project.status !== 'completed';
            
            return (
              <TableRow key={project.id} className={isOverdue ? 'bg-red-50' : ''}>
                <TableCell>
                  <div className="space-y-1">
                    <Link 
                      to={`/projects/${project.id}`}
                      className="font-medium hover:text-primary transition-colors"
                    >
                      {project.name}
                    </Link>
                    {project.description && (
                      <p className="text-sm text-muted-foreground line-clamp-1">
                        {project.description}
                      </p>
                    )}
                    {project.tags && project.tags.length > 0 && (
                      <div className="flex gap-1">
                        {project.tags.slice(0, 2).map((tag) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {project.tags.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{project.tags.length - 2}
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Badge variant={getStatusBadgeVariant(project.status)}>
                      {formatStatusLabel(project.status)}
                    </Badge>
                    {isOverdue && (
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  <Badge variant={getPriorityBadgeVariant(project.priority_level)}>
                    {project.priority_level}
                  </Badge>
                </TableCell>
                
                <TableCell>
                  <div className="space-y-1 min-w-[120px]">
                    <div className="flex justify-between text-sm">
                      <span>{progress}%</span>
                      {project.task_summary && (
                        <span className="text-muted-foreground">
                          {project.task_summary.completed}/{project.task_summary.total}
                        </span>
                      )}
                    </div>
                    <Progress value={progress} className="h-2" />
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-muted-foreground" />
                    <span className="truncate">{project.company_name}</span>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center gap-2">
                    {project.project_lead ? (
                      <>
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={project.project_lead.avatar_url} />
                          <AvatarFallback className="text-xs">
                            {project.project_lead.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <span className="truncate">{project.project_lead.name}</span>
                      </>
                    ) : (
                      <span className="text-muted-foreground">No lead</span>
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  {project.team_members && project.team_members.length > 0 ? (
                    <div className="flex items-center gap-1">
                      <div className="flex -space-x-1">
                        {project.team_members.slice(0, 3).map((member) => (
                          <Avatar key={member.user.id} className="h-6 w-6 border-2 border-background">
                            <AvatarImage src={member.user.avatar_url} />
                            <AvatarFallback className="text-xs">
                              {member.user.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                        ))}
                      </div>
                      {project.team_members.length > 3 && (
                        <span className="text-xs text-muted-foreground ml-1">
                          +{project.team_members.length - 3}
                        </span>
                      )}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">No team</span>
                  )}
                </TableCell>
                
                <TableCell>
                  <div className="space-y-1">
                    {project.end_date && (
                      <div className="flex items-center gap-1 text-sm">
                        <Calendar className="h-3 w-3" />
                        <span className={isOverdue ? 'text-red-600' : ''}>
                          {formatDate(project.end_date)}
                        </span>
                      </div>
                    )}
                    {project.days_remaining !== null && project.days_remaining !== undefined && (
                      <div className={`text-xs ${isOverdue ? 'text-red-600' : 'text-muted-foreground'}`}>
                        {isOverdue 
                          ? `${Math.abs(project.days_remaining)} days overdue`
                          : `${project.days_remaining} days left`
                        }
                      </div>
                    )}
                  </div>
                </TableCell>
                
                {showActions && (
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEdit?.(project)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onArchive?.(project)}>
                          <Archive className="mr-2 h-4 w-4" />
                          {project.status === 'archived' ? 'Unarchive' : 'Archive'}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => onDelete?.(project)}
                          className="text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                )}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
