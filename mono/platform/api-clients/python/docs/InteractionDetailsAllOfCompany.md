# InteractionDetailsAllOfCompany


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **str** |  | [optional] 
**name** | **str** |  | [optional] 
**website** | **str** |  | [optional] 
**phone** | **str** |  | [optional] 

## Example

```python
from crm_api_client.models.interaction_details_all_of_company import InteractionDetailsAllOfCompany

# TODO update the JSON string below
json = "{}"
# create an instance of InteractionDetailsAllOfCompany from a JSON string
interaction_details_all_of_company_instance = InteractionDetailsAllOfCompany.from_json(json)
# print the JSON string representation of the object
print(InteractionDetailsAllOfCompany.to_json())

# convert the object into a dict
interaction_details_all_of_company_dict = interaction_details_all_of_company_instance.to_dict()
# create an instance of InteractionDetailsAllOfCompany from a dict
interaction_details_all_of_company_from_dict = InteractionDetailsAllOfCompany.from_dict(interaction_details_all_of_company_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


