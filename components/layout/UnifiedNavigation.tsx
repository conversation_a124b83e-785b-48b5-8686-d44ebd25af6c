import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  FolderKanban, 
  Building2, 
  Users, 
  FileText, 
  CheckSquare, 
  BarChart3, 
  Settings, 
  User,
  Bell,
  Search,
  Plus,
  ChevronDown
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import { UserRole } from '@/types/unified-data-model';

interface NavigationItem {
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: number;
  roles: UserRole[];
  children?: NavigationItem[];
}

interface UnifiedNavigationProps {
  currentUser?: {
    id: string;
    name: string;
    email: string;
    role: UserRole;
    avatar_url?: string;
  };
  onCreateProject?: () => void;
  onSearch?: (query: string) => void;
}

const navigationItems: NavigationItem[] = [
  {
    label: 'Dashboard',
    href: '/',
    icon: LayoutDashboard,
    roles: ['admin', 'project_manager', 'team_member', 'requirements_analyst'],
  },
  {
    label: 'Projects',
    href: '/projects',
    icon: FolderKanban,
    roles: ['admin', 'project_manager', 'team_member', 'customer', 'requirements_analyst'],
    children: [
      {
        label: 'All Projects',
        href: '/projects',
        icon: FolderKanban,
        roles: ['admin', 'project_manager', 'team_member', 'requirements_analyst'],
      },
      {
        label: 'My Projects',
        href: '/projects/my',
        icon: User,
        roles: ['project_manager', 'team_member', 'customer'],
      },
      {
        label: 'Active Projects',
        href: '/projects/active',
        icon: FolderKanban,
        roles: ['admin', 'project_manager', 'team_member'],
      },
      {
        label: 'Archived Projects',
        href: '/projects/archived',
        icon: FolderKanban,
        roles: ['admin', 'project_manager'],
      },
    ],
  },
  {
    label: 'Companies',
    href: '/companies',
    icon: Building2,
    roles: ['admin', 'project_manager', 'team_member'],
  },
  {
    label: 'Contacts',
    href: '/contacts',
    icon: Users,
    roles: ['admin', 'project_manager', 'team_member'],
  },
  {
    label: 'Requirements',
    href: '/requirements',
    icon: FileText,
    roles: ['admin', 'project_manager', 'requirements_analyst', 'team_member'],
    children: [
      {
        label: 'All Requirements',
        href: '/requirements',
        icon: FileText,
        roles: ['admin', 'project_manager', 'requirements_analyst'],
      },
      {
        label: 'Pending Review',
        href: '/requirements/pending',
        icon: FileText,
        badge: 3,
        roles: ['admin', 'project_manager', 'requirements_analyst'],
      },
      {
        label: 'PRD Templates',
        href: '/requirements/templates',
        icon: FileText,
        roles: ['admin', 'requirements_analyst'],
      },
    ],
  },
  {
    label: 'Tasks',
    href: '/tasks',
    icon: CheckSquare,
    roles: ['admin', 'project_manager', 'team_member'],
    children: [
      {
        label: 'My Tasks',
        href: '/tasks/my',
        icon: User,
        badge: 5,
        roles: ['project_manager', 'team_member'],
      },
      {
        label: 'All Tasks',
        href: '/tasks',
        icon: CheckSquare,
        roles: ['admin', 'project_manager'],
      },
      {
        label: 'Overdue Tasks',
        href: '/tasks/overdue',
        icon: CheckSquare,
        badge: 2,
        roles: ['admin', 'project_manager', 'team_member'],
      },
    ],
  },
  {
    label: 'Reports',
    href: '/reports',
    icon: BarChart3,
    roles: ['admin', 'project_manager'],
    children: [
      {
        label: 'Project Analytics',
        href: '/reports/projects',
        icon: BarChart3,
        roles: ['admin', 'project_manager'],
      },
      {
        label: 'Team Performance',
        href: '/reports/team',
        icon: BarChart3,
        roles: ['admin', 'project_manager'],
      },
      {
        label: 'Customer Reports',
        href: '/reports/customers',
        icon: BarChart3,
        roles: ['admin', 'project_manager'],
      },
    ],
  },
  {
    label: 'Settings',
    href: '/settings',
    icon: Settings,
    roles: ['admin', 'project_manager', 'team_member', 'customer', 'requirements_analyst'],
  },
];

export function UnifiedNavigation({ 
  currentUser, 
  onCreateProject, 
  onSearch 
}: UnifiedNavigationProps) {
  const location = useLocation();
  const [searchQuery, setSearchQuery] = React.useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch?.(searchQuery);
  };

  const filteredNavItems = navigationItems.filter(item => 
    currentUser?.role && item.roles.includes(currentUser.role)
  );

  const isActiveRoute = (href: string) => {
    if (href === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(href);
  };

  const renderNavigationItem = (item: NavigationItem, isChild = false) => {
    const isActive = isActiveRoute(item.href);
    const Icon = item.icon;

    if (item.children && !isChild) {
      return (
        <DropdownMenu key={item.href}>
          <DropdownMenuTrigger asChild>
            <Button
              variant={isActive ? "secondary" : "ghost"}
              size="sm"
              className={cn(
                "justify-start gap-2 w-full",
                isActive && "bg-accent text-accent-foreground"
              )}
            >
              <Icon className="h-4 w-4" />
              {item.label}
              {item.badge && (
                <Badge variant="secondary" className="ml-auto">
                  {item.badge}
                </Badge>
              )}
              <ChevronDown className="h-3 w-3 ml-auto" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-48">
            {item.children
              .filter(child => currentUser?.role && child.roles.includes(currentUser.role))
              .map(child => (
                <DropdownMenuItem key={child.href} asChild>
                  <Link to={child.href} className="flex items-center gap-2">
                    <child.icon className="h-4 w-4" />
                    {child.label}
                    {child.badge && (
                      <Badge variant="secondary" className="ml-auto">
                        {child.badge}
                      </Badge>
                    )}
                  </Link>
                </DropdownMenuItem>
              ))}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    }

    return (
      <Button
        key={item.href}
        variant={isActive ? "secondary" : "ghost"}
        size="sm"
        asChild
        className={cn(
          "justify-start gap-2 w-full",
          isActive && "bg-accent text-accent-foreground"
        )}
      >
        <Link to={item.href}>
          <Icon className="h-4 w-4" />
          {item.label}
          {item.badge && (
            <Badge variant="secondary" className="ml-auto">
              {item.badge}
            </Badge>
          )}
        </Link>
      </Button>
    );
  };

  return (
    <nav className="border-b border-border bg-card/50 backdrop-blur supports-[backdrop-filter]:bg-card/60">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo and Brand */}
          <div className="flex items-center gap-8">
            <Link to="/" className="flex items-center gap-2">
              <div className="h-8 w-8 rounded bg-gradient-to-br from-primary to-primary/80"></div>
              <span className="text-lg font-semibold">ProjectHub</span>
            </Link>
            
            {/* Main Navigation */}
            <div className="hidden lg:flex items-center gap-1">
              {filteredNavItems.map(item => renderNavigationItem(item))}
            </div>
          </div>

          {/* Search and Actions */}
          <div className="flex items-center gap-4">
            {/* Search */}
            <form onSubmit={handleSearch} className="hidden md:block">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search projects, tasks..."
                  className="pl-10 w-64"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </form>

            {/* Quick Actions */}
            {currentUser?.role !== 'customer' && (
              <Button
                variant="default"
                size="sm"
                onClick={onCreateProject}
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                <span className="hidden sm:inline">New Project</span>
              </Button>
            )}

            {/* Notifications */}
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-4 w-4" />
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center text-xs"
              >
                3
              </Badge>
            </Button>

            {/* User Menu */}
            {currentUser && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={currentUser.avatar_url} alt={currentUser.name} />
                      <AvatarFallback>
                        {currentUser.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">{currentUser.name}</p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {currentUser.email}
                      </p>
                      <p className="text-xs leading-none text-muted-foreground capitalize">
                        {currentUser.role.replace('_', ' ')}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link to="/profile">Profile</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link to="/settings">Settings</Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    Log out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="lg:hidden border-t border-border py-2">
          <div className="flex flex-wrap gap-1">
            {filteredNavItems.slice(0, 4).map(item => {
              const Icon = item.icon;
              const isActive = isActiveRoute(item.href);
              
              return (
                <Button
                  key={item.href}
                  variant={isActive ? "secondary" : "ghost"}
                  size="sm"
                  asChild
                  className={cn(
                    "flex-1 min-w-0",
                    isActive && "bg-accent text-accent-foreground"
                  )}
                >
                  <Link to={item.href} className="flex flex-col items-center gap-1">
                    <Icon className="h-4 w-4" />
                    <span className="text-xs truncate">{item.label}</span>
                    {item.badge && (
                      <Badge variant="secondary" className="text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </Link>
                </Button>
              );
            })}
          </div>
        </div>
      </div>
    </nav>
  );
}
