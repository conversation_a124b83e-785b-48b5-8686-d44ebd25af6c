/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
)

// checks if the DocumentUpdate type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &DocumentUpdate{}

// DocumentUpdate struct for DocumentUpdate
type DocumentUpdate struct {
	Content *string `json:"content,omitempty"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
	FilterTags []string `json:"filter_tags,omitempty"`
	SourceId *string `json:"source_id,omitempty"`
}

// NewDocumentUpdate instantiates a new DocumentUpdate object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDocumentUpdate() *DocumentUpdate {
	this := DocumentUpdate{}
	return &this
}

// NewDocumentUpdateWithDefaults instantiates a new DocumentUpdate object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDocumentUpdateWithDefaults() *DocumentUpdate {
	this := DocumentUpdate{}
	return &this
}

// GetContent returns the Content field value if set, zero value otherwise.
func (o *DocumentUpdate) GetContent() string {
	if o == nil || IsNil(o.Content) {
		var ret string
		return ret
	}
	return *o.Content
}

// GetContentOk returns a tuple with the Content field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentUpdate) GetContentOk() (*string, bool) {
	if o == nil || IsNil(o.Content) {
		return nil, false
	}
	return o.Content, true
}

// HasContent returns a boolean if a field has been set.
func (o *DocumentUpdate) HasContent() bool {
	if o != nil && !IsNil(o.Content) {
		return true
	}

	return false
}

// SetContent gets a reference to the given string and assigns it to the Content field.
func (o *DocumentUpdate) SetContent(v string) {
	o.Content = &v
}

// GetMetadata returns the Metadata field value if set, zero value otherwise.
func (o *DocumentUpdate) GetMetadata() map[string]interface{} {
	if o == nil || IsNil(o.Metadata) {
		var ret map[string]interface{}
		return ret
	}
	return o.Metadata
}

// GetMetadataOk returns a tuple with the Metadata field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentUpdate) GetMetadataOk() (map[string]interface{}, bool) {
	if o == nil || IsNil(o.Metadata) {
		return map[string]interface{}{}, false
	}
	return o.Metadata, true
}

// HasMetadata returns a boolean if a field has been set.
func (o *DocumentUpdate) HasMetadata() bool {
	if o != nil && !IsNil(o.Metadata) {
		return true
	}

	return false
}

// SetMetadata gets a reference to the given map[string]interface{} and assigns it to the Metadata field.
func (o *DocumentUpdate) SetMetadata(v map[string]interface{}) {
	o.Metadata = v
}

// GetFilterTags returns the FilterTags field value if set, zero value otherwise.
func (o *DocumentUpdate) GetFilterTags() []string {
	if o == nil || IsNil(o.FilterTags) {
		var ret []string
		return ret
	}
	return o.FilterTags
}

// GetFilterTagsOk returns a tuple with the FilterTags field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentUpdate) GetFilterTagsOk() ([]string, bool) {
	if o == nil || IsNil(o.FilterTags) {
		return nil, false
	}
	return o.FilterTags, true
}

// HasFilterTags returns a boolean if a field has been set.
func (o *DocumentUpdate) HasFilterTags() bool {
	if o != nil && !IsNil(o.FilterTags) {
		return true
	}

	return false
}

// SetFilterTags gets a reference to the given []string and assigns it to the FilterTags field.
func (o *DocumentUpdate) SetFilterTags(v []string) {
	o.FilterTags = v
}

// GetSourceId returns the SourceId field value if set, zero value otherwise.
func (o *DocumentUpdate) GetSourceId() string {
	if o == nil || IsNil(o.SourceId) {
		var ret string
		return ret
	}
	return *o.SourceId
}

// GetSourceIdOk returns a tuple with the SourceId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DocumentUpdate) GetSourceIdOk() (*string, bool) {
	if o == nil || IsNil(o.SourceId) {
		return nil, false
	}
	return o.SourceId, true
}

// HasSourceId returns a boolean if a field has been set.
func (o *DocumentUpdate) HasSourceId() bool {
	if o != nil && !IsNil(o.SourceId) {
		return true
	}

	return false
}

// SetSourceId gets a reference to the given string and assigns it to the SourceId field.
func (o *DocumentUpdate) SetSourceId(v string) {
	o.SourceId = &v
}

func (o DocumentUpdate) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o DocumentUpdate) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Content) {
		toSerialize["content"] = o.Content
	}
	if !IsNil(o.Metadata) {
		toSerialize["metadata"] = o.Metadata
	}
	if !IsNil(o.FilterTags) {
		toSerialize["filter_tags"] = o.FilterTags
	}
	if !IsNil(o.SourceId) {
		toSerialize["source_id"] = o.SourceId
	}
	return toSerialize, nil
}

type NullableDocumentUpdate struct {
	value *DocumentUpdate
	isSet bool
}

func (v NullableDocumentUpdate) Get() *DocumentUpdate {
	return v.value
}

func (v *NullableDocumentUpdate) Set(val *DocumentUpdate) {
	v.value = val
	v.isSet = true
}

func (v NullableDocumentUpdate) IsSet() bool {
	return v.isSet
}

func (v *NullableDocumentUpdate) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDocumentUpdate(val *DocumentUpdate) *NullableDocumentUpdate {
	return &NullableDocumentUpdate{value: val, isSet: true}
}

func (v NullableDocumentUpdate) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDocumentUpdate) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


