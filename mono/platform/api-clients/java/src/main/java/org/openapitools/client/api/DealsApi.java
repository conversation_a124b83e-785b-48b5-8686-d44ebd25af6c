/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.api;

import org.openapitools.client.ApiCallback;
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.ApiResponse;
import org.openapitools.client.Configuration;
import org.openapitools.client.Pair;
import org.openapitools.client.ProgressRequestBody;
import org.openapitools.client.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import org.openapitools.client.model.Deal;
import org.openapitools.client.model.DealCreate;
import org.openapitools.client.model.DealDetails;
import org.openapitools.client.model.DealStage;
import org.openapitools.client.model.DealUpdate;
import org.openapitools.client.model.DealWithDetails;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DealsApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public DealsApi() {
        this(Configuration.getDefaultApiClient());
    }

    public DealsApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for dealStagesGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of deal stages </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call dealStagesGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/deal-stages";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call dealStagesGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return dealStagesGetCall(_callback);

    }

    /**
     * List deal stages
     * Retrieve all deal stages ordered by pipeline order
     * @return List&lt;DealStage&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of deal stages </td><td>  -  </td></tr>
     </table>
     */
    public List<DealStage> dealStagesGet() throws ApiException {
        ApiResponse<List<DealStage>> localVarResp = dealStagesGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * List deal stages
     * Retrieve all deal stages ordered by pipeline order
     * @return ApiResponse&lt;List&lt;DealStage&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of deal stages </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<DealStage>> dealStagesGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = dealStagesGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<List<DealStage>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * List deal stages (asynchronously)
     * Retrieve all deal stages ordered by pipeline order
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of deal stages </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call dealStagesGetAsync(final ApiCallback<List<DealStage>> _callback) throws ApiException {

        okhttp3.Call localVarCall = dealStagesGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<List<DealStage>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for dealsGet
     * @param stageId Filter by deal stage ID (optional)
     * @param companyId Filter by company ID (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of deals </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call dealsGetCall(UUID stageId, UUID companyId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/deals";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (stageId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("stage_id", stageId));
        }

        if (companyId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("company_id", companyId));
        }

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call dealsGetValidateBeforeCall(UUID stageId, UUID companyId, final ApiCallback _callback) throws ApiException {
        return dealsGetCall(stageId, companyId, _callback);

    }

    /**
     * List deals
     * Retrieve a list of all deals with company and stage information
     * @param stageId Filter by deal stage ID (optional)
     * @param companyId Filter by company ID (optional)
     * @return List&lt;DealWithDetails&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of deals </td><td>  -  </td></tr>
     </table>
     */
    public List<DealWithDetails> dealsGet(UUID stageId, UUID companyId) throws ApiException {
        ApiResponse<List<DealWithDetails>> localVarResp = dealsGetWithHttpInfo(stageId, companyId);
        return localVarResp.getData();
    }

    /**
     * List deals
     * Retrieve a list of all deals with company and stage information
     * @param stageId Filter by deal stage ID (optional)
     * @param companyId Filter by company ID (optional)
     * @return ApiResponse&lt;List&lt;DealWithDetails&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of deals </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<DealWithDetails>> dealsGetWithHttpInfo(UUID stageId, UUID companyId) throws ApiException {
        okhttp3.Call localVarCall = dealsGetValidateBeforeCall(stageId, companyId, null);
        Type localVarReturnType = new TypeToken<List<DealWithDetails>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * List deals (asynchronously)
     * Retrieve a list of all deals with company and stage information
     * @param stageId Filter by deal stage ID (optional)
     * @param companyId Filter by company ID (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> List of deals </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call dealsGetAsync(UUID stageId, UUID companyId, final ApiCallback<List<DealWithDetails>> _callback) throws ApiException {

        okhttp3.Call localVarCall = dealsGetValidateBeforeCall(stageId, companyId, _callback);
        Type localVarReturnType = new TypeToken<List<DealWithDetails>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for dealsIdDelete
     * @param id  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Deal deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Deal not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call dealsIdDeleteCall(UUID id, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/deals/{id}"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call dealsIdDeleteValidateBeforeCall(UUID id, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling dealsIdDelete(Async)");
        }

        return dealsIdDeleteCall(id, _callback);

    }

    /**
     * Delete deal
     * Delete a deal
     * @param id  (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Deal deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Deal not found </td><td>  -  </td></tr>
     </table>
     */
    public void dealsIdDelete(UUID id) throws ApiException {
        dealsIdDeleteWithHttpInfo(id);
    }

    /**
     * Delete deal
     * Delete a deal
     * @param id  (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Deal deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Deal not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> dealsIdDeleteWithHttpInfo(UUID id) throws ApiException {
        okhttp3.Call localVarCall = dealsIdDeleteValidateBeforeCall(id, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Delete deal (asynchronously)
     * Delete a deal
     * @param id  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 204 </td><td> Deal deleted successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Deal not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call dealsIdDeleteAsync(UUID id, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = dealsIdDeleteValidateBeforeCall(id, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for dealsIdGet
     * @param id  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Deal details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Deal not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call dealsIdGetCall(UUID id, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/deals/{id}"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call dealsIdGetValidateBeforeCall(UUID id, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling dealsIdGet(Async)");
        }

        return dealsIdGetCall(id, _callback);

    }

    /**
     * Get deal
     * Retrieve a specific deal by ID
     * @param id  (required)
     * @return DealDetails
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Deal details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Deal not found </td><td>  -  </td></tr>
     </table>
     */
    public DealDetails dealsIdGet(UUID id) throws ApiException {
        ApiResponse<DealDetails> localVarResp = dealsIdGetWithHttpInfo(id);
        return localVarResp.getData();
    }

    /**
     * Get deal
     * Retrieve a specific deal by ID
     * @param id  (required)
     * @return ApiResponse&lt;DealDetails&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Deal details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Deal not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<DealDetails> dealsIdGetWithHttpInfo(UUID id) throws ApiException {
        okhttp3.Call localVarCall = dealsIdGetValidateBeforeCall(id, null);
        Type localVarReturnType = new TypeToken<DealDetails>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get deal (asynchronously)
     * Retrieve a specific deal by ID
     * @param id  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Deal details </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Deal not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call dealsIdGetAsync(UUID id, final ApiCallback<DealDetails> _callback) throws ApiException {

        okhttp3.Call localVarCall = dealsIdGetValidateBeforeCall(id, _callback);
        Type localVarReturnType = new TypeToken<DealDetails>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for dealsIdPut
     * @param id  (required)
     * @param dealUpdate  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Deal updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Deal not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call dealsIdPutCall(UUID id, DealUpdate dealUpdate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = dealUpdate;

        // create path and map variables
        String localVarPath = "/deals/{id}"
            .replace("{" + "id" + "}", localVarApiClient.escapeString(id.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call dealsIdPutValidateBeforeCall(UUID id, DealUpdate dealUpdate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new ApiException("Missing the required parameter 'id' when calling dealsIdPut(Async)");
        }

        // verify the required parameter 'dealUpdate' is set
        if (dealUpdate == null) {
            throw new ApiException("Missing the required parameter 'dealUpdate' when calling dealsIdPut(Async)");
        }

        return dealsIdPutCall(id, dealUpdate, _callback);

    }

    /**
     * Update deal
     * Update an existing deal
     * @param id  (required)
     * @param dealUpdate  (required)
     * @return Deal
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Deal updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Deal not found </td><td>  -  </td></tr>
     </table>
     */
    public Deal dealsIdPut(UUID id, DealUpdate dealUpdate) throws ApiException {
        ApiResponse<Deal> localVarResp = dealsIdPutWithHttpInfo(id, dealUpdate);
        return localVarResp.getData();
    }

    /**
     * Update deal
     * Update an existing deal
     * @param id  (required)
     * @param dealUpdate  (required)
     * @return ApiResponse&lt;Deal&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Deal updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Deal not found </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Deal> dealsIdPutWithHttpInfo(UUID id, DealUpdate dealUpdate) throws ApiException {
        okhttp3.Call localVarCall = dealsIdPutValidateBeforeCall(id, dealUpdate, null);
        Type localVarReturnType = new TypeToken<Deal>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Update deal (asynchronously)
     * Update an existing deal
     * @param id  (required)
     * @param dealUpdate  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Deal updated successfully </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Deal not found </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call dealsIdPutAsync(UUID id, DealUpdate dealUpdate, final ApiCallback<Deal> _callback) throws ApiException {

        okhttp3.Call localVarCall = dealsIdPutValidateBeforeCall(id, dealUpdate, _callback);
        Type localVarReturnType = new TypeToken<Deal>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for dealsPost
     * @param dealCreate  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Deal created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call dealsPostCall(DealCreate dealCreate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = dealCreate;

        // create path and map variables
        String localVarPath = "/deals";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call dealsPostValidateBeforeCall(DealCreate dealCreate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'dealCreate' is set
        if (dealCreate == null) {
            throw new ApiException("Missing the required parameter 'dealCreate' when calling dealsPost(Async)");
        }

        return dealsPostCall(dealCreate, _callback);

    }

    /**
     * Create deal
     * Create a new deal
     * @param dealCreate  (required)
     * @return Deal
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Deal created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public Deal dealsPost(DealCreate dealCreate) throws ApiException {
        ApiResponse<Deal> localVarResp = dealsPostWithHttpInfo(dealCreate);
        return localVarResp.getData();
    }

    /**
     * Create deal
     * Create a new deal
     * @param dealCreate  (required)
     * @return ApiResponse&lt;Deal&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Deal created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Deal> dealsPostWithHttpInfo(DealCreate dealCreate) throws ApiException {
        okhttp3.Call localVarCall = dealsPostValidateBeforeCall(dealCreate, null);
        Type localVarReturnType = new TypeToken<Deal>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Create deal (asynchronously)
     * Create a new deal
     * @param dealCreate  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 201 </td><td> Deal created successfully </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Invalid input </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call dealsPostAsync(DealCreate dealCreate, final ApiCallback<Deal> _callback) throws ApiException {

        okhttp3.Call localVarCall = dealsPostValidateBeforeCall(dealCreate, _callback);
        Type localVarReturnType = new TypeToken<Deal>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
