/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import java.util.Objects;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.openapitools.client.model.InteractionWithDetails;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.openapitools.client.JSON;

/**
 * InteractionsGet200Response
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-07-14T08:13:23.820009+10:00[Australia/Brisbane]", comments = "Generator version: 7.9.0")
public class InteractionsGet200Response {
  public static final String SERIALIZED_NAME_INTERACTIONS = "interactions";
  @SerializedName(SERIALIZED_NAME_INTERACTIONS)
  private List<InteractionWithDetails> interactions = new ArrayList<>();

  public static final String SERIALIZED_NAME_TOTAL_COUNT = "total_count";
  @SerializedName(SERIALIZED_NAME_TOTAL_COUNT)
  private Integer totalCount;

  public static final String SERIALIZED_NAME_HAS_MORE = "has_more";
  @SerializedName(SERIALIZED_NAME_HAS_MORE)
  private Boolean hasMore;

  public InteractionsGet200Response() {
  }

  public InteractionsGet200Response interactions(List<InteractionWithDetails> interactions) {
    this.interactions = interactions;
    return this;
  }

  public InteractionsGet200Response addInteractionsItem(InteractionWithDetails interactionsItem) {
    if (this.interactions == null) {
      this.interactions = new ArrayList<>();
    }
    this.interactions.add(interactionsItem);
    return this;
  }

  /**
   * Get interactions
   * @return interactions
   */
  @javax.annotation.Nullable
  public List<InteractionWithDetails> getInteractions() {
    return interactions;
  }

  public void setInteractions(List<InteractionWithDetails> interactions) {
    this.interactions = interactions;
  }


  public InteractionsGet200Response totalCount(Integer totalCount) {
    this.totalCount = totalCount;
    return this;
  }

  /**
   * Get totalCount
   * @return totalCount
   */
  @javax.annotation.Nullable
  public Integer getTotalCount() {
    return totalCount;
  }

  public void setTotalCount(Integer totalCount) {
    this.totalCount = totalCount;
  }


  public InteractionsGet200Response hasMore(Boolean hasMore) {
    this.hasMore = hasMore;
    return this;
  }

  /**
   * Get hasMore
   * @return hasMore
   */
  @javax.annotation.Nullable
  public Boolean getHasMore() {
    return hasMore;
  }

  public void setHasMore(Boolean hasMore) {
    this.hasMore = hasMore;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InteractionsGet200Response interactionsGet200Response = (InteractionsGet200Response) o;
    return Objects.equals(this.interactions, interactionsGet200Response.interactions) &&
        Objects.equals(this.totalCount, interactionsGet200Response.totalCount) &&
        Objects.equals(this.hasMore, interactionsGet200Response.hasMore);
  }

  @Override
  public int hashCode() {
    return Objects.hash(interactions, totalCount, hasMore);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InteractionsGet200Response {\n");
    sb.append("    interactions: ").append(toIndentedString(interactions)).append("\n");
    sb.append("    totalCount: ").append(toIndentedString(totalCount)).append("\n");
    sb.append("    hasMore: ").append(toIndentedString(hasMore)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("interactions");
    openapiFields.add("total_count");
    openapiFields.add("has_more");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

  /**
   * Validates the JSON Element and throws an exception if issues found
   *
   * @param jsonElement JSON Element
   * @throws IOException if the JSON Element is invalid with respect to InteractionsGet200Response
   */
  public static void validateJsonElement(JsonElement jsonElement) throws IOException {
      if (jsonElement == null) {
        if (!InteractionsGet200Response.openapiRequiredFields.isEmpty()) { // has required fields but JSON element is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in InteractionsGet200Response is not found in the empty JSON string", InteractionsGet200Response.openapiRequiredFields.toString()));
        }
      }

      Set<Map.Entry<String, JsonElement>> entries = jsonElement.getAsJsonObject().entrySet();
      // check to see if the JSON string contains additional fields
      for (Map.Entry<String, JsonElement> entry : entries) {
        if (!InteractionsGet200Response.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `InteractionsGet200Response` properties. JSON: %s", entry.getKey(), jsonElement.toString()));
        }
      }
        JsonObject jsonObj = jsonElement.getAsJsonObject();
      if (jsonObj.get("interactions") != null && !jsonObj.get("interactions").isJsonNull()) {
        JsonArray jsonArrayinteractions = jsonObj.getAsJsonArray("interactions");
        if (jsonArrayinteractions != null) {
          // ensure the json data is an array
          if (!jsonObj.get("interactions").isJsonArray()) {
            throw new IllegalArgumentException(String.format("Expected the field `interactions` to be an array in the JSON string but got `%s`", jsonObj.get("interactions").toString()));
          }

          // validate the optional field `interactions` (array)
          for (int i = 0; i < jsonArrayinteractions.size(); i++) {
            InteractionWithDetails.validateJsonElement(jsonArrayinteractions.get(i));
          };
        }
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!InteractionsGet200Response.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'InteractionsGet200Response' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<InteractionsGet200Response> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(InteractionsGet200Response.class));

       return (TypeAdapter<T>) new TypeAdapter<InteractionsGet200Response>() {
           @Override
           public void write(JsonWriter out, InteractionsGet200Response value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public InteractionsGet200Response read(JsonReader in) throws IOException {
             JsonElement jsonElement = elementAdapter.read(in);
             validateJsonElement(jsonElement);
             return thisAdapter.fromJsonTree(jsonElement);
           }

       }.nullSafe();
    }
  }

  /**
   * Create an instance of InteractionsGet200Response given an JSON string
   *
   * @param jsonString JSON string
   * @return An instance of InteractionsGet200Response
   * @throws IOException if the JSON string is invalid with respect to InteractionsGet200Response
   */
  public static InteractionsGet200Response fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, InteractionsGet200Response.class);
  }

  /**
   * Convert an instance of InteractionsGet200Response to an JSON string
   *
   * @return JSON string
   */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

