import { Router, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireRole, canAccessProject } from '../middleware/auth';

const router = Router();
const prisma = new PrismaClient();

// Validation schemas
const createProjectSchema = z.object({
  name: z.string().min(1, 'Project name is required'),
  description: z.string().optional(),
  type: z.enum(['internal', 'external', 'hybrid']),
  company_id: z.string().uuid().optional(),
  company_name: z.string().min(1, 'Company name is required'),
  customer_contact_id: z.string().uuid().optional(),
  priority_level: z.enum(['P0', 'P1', 'P2', 'P3', 'P4']).default('P3'),
  effort_estimate: z.enum(['XS', 'S', 'M', 'L', 'XL', 'XXL']).default('M'),
  impact_type: z.enum(['Revenue', 'Platform', 'Bug_Fix', 'R_D', 'Customer_Success']).default('Platform'),
  start_date: z.string().datetime().optional(),
  end_date: z.string().datetime().optional(),
  is_customer_visible: z.boolean().default(false),
  tags: z.array(z.string()).default([]),
});

/**
 * @route GET /api/projects
 * @desc Get all projects (filtered by user role and permissions)
 * @access Private
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const {
    page = '1',
    limit = '10',
    status,
    type,
    search,
    priority_level,
    lead_id,
    company_id,
    show_archived = 'false',
    sort_by = 'created_at',
    sort_order = 'desc',
    group_by = 'none'
  } = req.query;

  const skip = (parseInt(page as string) - 1) * parseInt(limit as string);

  let whereClause: any = {};

  // Filter based on user role
  if (req.user?.role === 'customer') {
    // Customers can only see projects where they are the customer contact
    whereClause.customer_contact = { email: req.user.email };
    whereClause.is_customer_visible = true;
  } else if (req.user?.role === 'team_member') {
    // Team members can see projects they're assigned to or leading
    whereClause.OR = [
      { project_lead_id: req.user.id },
      { team_members: { some: { user_id: req.user.id } } },
    ];
  }
  // Admin and project managers can see all projects

  // Core Project Pulse Advanced Filters
  if (status) {
    whereClause.status = status;
  }
  if (type) {
    whereClause.type = type;
  }
  if (priority_level) {
    whereClause.priority_level = priority_level;
  }
  if (lead_id) {
    whereClause.project_lead_id = lead_id;
  }
  if (company_id) {
    whereClause.company_id = company_id;
  }

  // Archive filter - show archived projects only if explicitly requested
  if (show_archived === 'true') {
    whereClause.status = 'archived';
  } else {
    whereClause.status = { not: 'archived' };
  }

  // Enhanced search across multiple fields
  if (search) {
    const searchConditions = [
      { name: { contains: search as string, mode: 'insensitive' } },
      { description: { contains: search as string, mode: 'insensitive' } },
      { company_name: { contains: search as string, mode: 'insensitive' } },
      { tags: { has: search as string } },
    ];

    // If there are existing OR conditions (from role filtering), combine them
    if (whereClause.OR) {
      whereClause.AND = [
        { OR: whereClause.OR },
        { OR: searchConditions }
      ];
      delete whereClause.OR;
    } else {
      whereClause.OR = searchConditions;
    }
  }

  // Sorting configuration
  const validSortFields = ['name', 'created_at', 'updated_at', 'start_date', 'end_date', 'priority_level', 'status'];
  const sortField = validSortFields.includes(sort_by as string) ? sort_by as string : 'created_at';
  const sortDirection = sort_order === 'asc' ? 'asc' : 'desc';

  let orderBy: any = { [sortField]: sortDirection };

  // Special sorting for priority levels (P0 should come first)
  if (sortField === 'priority_level') {
    orderBy = [
      { priority_level: sortDirection },
      { created_at: 'desc' } // Secondary sort
    ];
  }

  const [projects, total] = await Promise.all([
    prisma.project.findMany({
      where: whereClause,
      include: {
        company: true,
        customer_contact: true,
        project_lead: {
          select: { id: true, name: true, email: true, avatar_url: true },
        },
        team_members: {
          include: {
            user: {
              select: { id: true, name: true, email: true, avatar_url: true },
            },
          },
        },
        tasks: {
          select: {
            id: true,
            name: true,
            status: true,
            priority_level: true,
            assignee_id: true,
            due_date: true,
            parent_task_id: true,
          },
        },
        requirements: {
          select: {
            id: true,
            title: true,
            status: true,
            priority_level: true,
          },
        },
        _count: {
          select: {
            tasks: true,
            requirements: true,
          },
        },
      },
      skip,
      take: parseInt(limit as string),
      orderBy,
    }),
    prisma.project.count({ where: whereClause }),
  ]);

  // Calculate progress and enhance project data
  const enhancedProjects = projects.map(project => {
    const totalTasks = project.tasks.length;
    const completedTasks = project.tasks.filter(task => task.status === 'done').length;
    const calculatedProgress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

    // Calculate days remaining/overdue
    const today = new Date();
    let daysRemaining = null;
    let isOverdue = false;

    if (project.end_date) {
      const endDate = new Date(project.end_date);
      const diffTime = endDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      daysRemaining = diffDays;
      isOverdue = diffDays < 0;
    }

    return {
      ...project,
      calculated_progress: calculatedProgress,
      days_remaining: daysRemaining,
      is_overdue: isOverdue,
      task_summary: {
        total: totalTasks,
        completed: completedTasks,
        in_progress: project.tasks.filter(task => task.status === 'in_progress').length,
        to_do: project.tasks.filter(task => task.status === 'to_do').length,
      },
    };
  });

  // Group projects if requested
  let groupedProjects = null;
  if (group_by !== 'none') {
    groupedProjects = enhancedProjects.reduce((groups: any, project: any) => {
      let groupKey: string = '';

      switch (group_by) {
        case 'status':
          groupKey = project.status || 'No Status';
          break;
        case 'priority_level':
          groupKey = project.priority_level || 'No Priority';
          break;
        case 'lead':
          groupKey = (project.project_lead as any)?.name || 'Unassigned';
          break;
        case 'start_date':
          if (project.start_date) {
            const isoString = new Date(project.start_date as any).toISOString();
            const dateParts = isoString.split('T');
            groupKey = (dateParts[0] as string) || 'No Start Date';
          } else {
            groupKey = 'No Start Date';
          }
          break;
        case 'end_date':
          if (project.end_date) {
            const isoString = new Date(project.end_date as any).toISOString();
            const dateParts = isoString.split('T');
            groupKey = (dateParts[0] as string) || 'No End Date';
          } else {
            groupKey = 'No End Date';
          }
          break;
        default:
          groupKey = 'Other';
      }

      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(project);
      return groups;
    }, {});
  }

  res.json({
    status: 'success',
    data: {
      projects: enhancedProjects,
      grouped_projects: groupedProjects,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        pages: Math.ceil(total / parseInt(limit as string)),
      },
      filters: {
        status,
        type,
        priority_level,
        lead_id,
        company_id,
        show_archived,
        search,
      },
      sorting: {
        sort_by: sortField,
        sort_order: sortDirection,
      },
      grouping: {
        group_by,
      },
    },
  });
}));

/**
 * @route GET /api/projects/:id
 * @desc Get project by ID
 * @access Private
 */
router.get('/:id', canAccessProject, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const project = await prisma.project.findUnique({
    where: { id: id as string },
    include: {
      company: true,
      customer_contact: true,
      project_lead: {
        select: { id: true, name: true, email: true, avatar_url: true, title: true },
      },
      team_members: {
        include: {
          user: {
            select: { id: true, name: true, email: true, avatar_url: true, title: true },
          },
        },
      },
      tasks: {
        include: {
          assignee: {
            select: { id: true, name: true, email: true, avatar_url: true },
          },
          sub_tasks: {
            include: {
              assignee: {
                select: { id: true, name: true, email: true, avatar_url: true },
              },
            },
          },
        },
        orderBy: { created_at: 'asc' },
      },
      requirements: {
        include: {
          created_by: {
            select: { id: true, name: true, email: true },
          },
          assigned_to: {
            select: { id: true, name: true, email: true },
          },
        },
        orderBy: { created_at: 'desc' },
      },
      interactions: {
        include: {
          created_by: {
            select: { id: true, name: true, email: true },
          },
          contact: true,
        },
        orderBy: { interaction_date: 'desc' },
        take: 10, // Latest 10 interactions
      },
    },
  });

  if (!project) {
    return res.status(404).json({
      status: 'error',
      message: 'Project not found',
    });
  }

  // Filter sensitive data for customers
  if (req.user?.role === 'customer') {
    const customerVisibleFields = project.customer_visible_fields;
    const filteredProject = Object.keys(project).reduce((acc: any, key) => {
      if (customerVisibleFields.includes(key) || ['id', 'name', 'description', 'status', 'progress_percentage'].includes(key)) {
        acc[key] = (project as any)[key];
      }
      return acc;
    }, {});

    // Filter tasks to only customer-visible ones
    filteredProject.tasks = (project as any).tasks?.filter((task: any) => task.is_customer_visible);
    
    return res.json({
      status: 'success',
      data: { project: filteredProject },
    });
  }

  res.json({
    status: 'success',
    data: { project },
  });
}));

/**
 * @route POST /api/projects
 * @desc Create new project
 * @access Private (Admin, Project Manager)
 */
router.post('/', requireRole('admin', 'project_manager'), asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const validatedData = createProjectSchema.parse(req.body);

  const project = await prisma.project.create({
    data: {
      name: validatedData.name,
      description: validatedData.description ?? null,
      type: validatedData.type,
      company_id: validatedData.company_id || null,
      company_name: validatedData.company_name || '',
      customer_contact_id: validatedData.customer_contact_id || null,
      priority_level: validatedData.priority_level,
      effort_estimate: validatedData.effort_estimate,
      impact_type: validatedData.impact_type,
      start_date: validatedData.start_date ? new Date(validatedData.start_date) : null,
      end_date: validatedData.end_date ? new Date(validatedData.end_date) : null,
      is_customer_visible: validatedData.is_customer_visible ?? false,
      tags: validatedData.tags ?? [],
      project_lead_id: req.user!.id,
    },
    include: {
      company: true,
      customer_contact: true,
      project_lead: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
    },
  });

  res.status(201).json({
    status: 'success',
    message: 'Project created successfully',
    data: { project },
  });
}));

/**
 * @route PUT /api/projects/:id
 * @desc Update project
 * @access Private (Admin, Project Manager, Project Lead)
 */
router.put('/:id', canAccessProject, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const updateData = createProjectSchema.partial().parse(req.body);

  const updateFields: any = {};

  if (updateData.name !== undefined) updateFields.name = updateData.name;
  if (updateData.description !== undefined) updateFields.description = updateData.description ?? null;
  if (updateData.type !== undefined) updateFields.type = updateData.type;
  if (updateData.company_id !== undefined) updateFields.company_id = updateData.company_id ?? null;
  if (updateData.company_name !== undefined) updateFields.company_name = updateData.company_name;
  if (updateData.customer_contact_id !== undefined) updateFields.customer_contact_id = updateData.customer_contact_id ?? null;
  if (updateData.priority_level !== undefined) updateFields.priority_level = updateData.priority_level;
  if (updateData.effort_estimate !== undefined) updateFields.effort_estimate = updateData.effort_estimate;
  if (updateData.impact_type !== undefined) updateFields.impact_type = updateData.impact_type;
  if (updateData.start_date !== undefined) updateFields.start_date = updateData.start_date ? new Date(updateData.start_date) : null;
  if (updateData.end_date !== undefined) updateFields.end_date = updateData.end_date ? new Date(updateData.end_date) : null;
  if (updateData.is_customer_visible !== undefined) updateFields.is_customer_visible = updateData.is_customer_visible;
  if (updateData.tags !== undefined) updateFields.tags = updateData.tags;

  const project = await prisma.project.update({
    where: { id: id as string },
    data: updateFields,
    include: {
      company: true,
      customer_contact: true,
      project_lead: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
    },
  });

  res.json({
    status: 'success',
    message: 'Project updated successfully',
    data: { project },
  });
}));

/**
 * @route DELETE /api/projects/:id
 * @desc Delete project
 * @access Private (Admin only)
 */
router.delete('/:id', requireRole('admin'), asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  await prisma.project.delete({
    where: { id: id as string },
  });

  res.json({
    status: 'success',
    message: 'Project deleted successfully',
  });
}));

/**
 * @route PATCH /api/projects/:id/status
 * @desc Update project status with status change tracking
 * @access Private (Admin, Project Manager, Project Lead)
 */
router.patch('/:id/status', canAccessProject, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { status } = req.body;

  if (!status) {
    return res.status(400).json({
      status: 'error',
      message: 'Status is required',
    });
  }

  const validStatuses = ['draft', 'requirements', 'backlog', 'not_started', 'in_progress', 'completed', 'archived', 'on_hold'];
  if (!validStatuses.includes(status)) {
    return res.status(400).json({
      status: 'error',
      message: 'Invalid status value',
    });
  }

  const project = await prisma.project.update({
    where: { id: id as string },
    data: {
      status,
      updated_at: new Date(),
    },
    include: {
      project_lead: {
        select: { id: true, name: true, email: true },
      },
    },
  });

  res.json({
    status: 'success',
    data: { project },
    message: `Project status updated to ${status}`,
  });
}));

/**
 * @route PATCH /api/projects/:id/archive
 * @desc Archive/unarchive project
 * @access Private (Admin, Project Manager)
 */
router.patch('/:id/archive', requireRole('admin', 'project_manager'), asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { archive = true } = req.body;

  const project = await prisma.project.update({
    where: { id: id as string },
    data: {
      status: archive ? 'archived' : 'not_started',
      updated_at: new Date(),
    },
  });

  res.json({
    status: 'success',
    data: { project },
    message: `Project ${archive ? 'archived' : 'unarchived'} successfully`,
  });
}));

/**
 * @route PATCH /api/projects/:id/priority
 * @desc Update project priority with history tracking
 * @access Private (Admin, Project Manager, Project Lead)
 */
router.patch('/:id/priority', canAccessProject, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { priority_level } = req.body;

  if (!priority_level) {
    return res.status(400).json({
      status: 'error',
      message: 'Priority level is required',
    });
  }

  const validPriorities = ['P0', 'P1', 'P2', 'P3', 'P4'];
  if (!validPriorities.includes(priority_level)) {
    return res.status(400).json({
      status: 'error',
      message: 'Invalid priority level',
    });
  }

  const project = await prisma.project.update({
    where: { id: id as string },
    data: {
      priority_level,
      updated_at: new Date(),
    },
  });

  res.json({
    status: 'success',
    data: { project },
    message: `Project priority updated to ${priority_level}`,
  });
}));

/**
 * @route GET /api/projects/stats/overview
 * @desc Get project statistics overview
 * @access Private
 */
router.get('/stats/overview', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  let whereClause: any = {};

  // Filter based on user role
  if (req.user?.role === 'customer') {
    whereClause.customer_contact = { email: req.user.email };
    whereClause.is_customer_visible = true;
  } else if (req.user?.role === 'team_member') {
    whereClause.OR = [
      { project_lead_id: req.user.id },
      { team_members: { some: { user_id: req.user.id } } },
    ];
  }

  const [
    totalProjects,
    activeProjects,
    completedProjects,
    archivedProjects,
    overdueProjects,
    priorityStats,
    statusStats
  ] = await Promise.all([
    prisma.project.count({ where: whereClause }),
    prisma.project.count({ where: { ...whereClause, status: { in: ['in_progress', 'not_started'] } } }),
    prisma.project.count({ where: { ...whereClause, status: 'completed' } }),
    prisma.project.count({ where: { ...whereClause, status: 'archived' } }),
    prisma.project.count({
      where: {
        ...whereClause,
        end_date: { lt: new Date() },
        status: { not: 'completed' }
      }
    }),
    prisma.project.groupBy({
      by: ['priority_level'],
      where: whereClause,
      _count: true,
    }),
    prisma.project.groupBy({
      by: ['status'],
      where: whereClause,
      _count: true,
    }),
  ]);

  res.json({
    status: 'success',
    data: {
      overview: {
        total_projects: totalProjects,
        active_projects: activeProjects,
        completed_projects: completedProjects,
        archived_projects: archivedProjects,
        overdue_projects: overdueProjects,
      },
      priority_distribution: priorityStats.reduce((acc: any, stat: any) => {
        acc[stat.priority_level] = stat._count;
        return acc;
      }, {}),
      status_distribution: statusStats.reduce((acc: any, stat: any) => {
        acc[stat.status] = stat._count;
        return acc;
      }, {}),
    },
  });
}));

export default router;
