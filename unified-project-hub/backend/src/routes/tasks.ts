import { Router, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { AuthenticatedRequest, authMiddleware } from '../middleware/auth';

const router = Router();
const prisma = new PrismaClient();

// GET /api/tasks - Get all tasks
router.get('/', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  try {
    const tasks = await prisma.task.findMany({
      include: {
        project: {
          select: {
            id: true,
            name: true,
            company_name: true
          }
        },
        assignee: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        parent_task: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        created_at: 'desc'
      }
    });

    res.json({
      success: true,
      tasks: tasks,
      total: tasks.length
    });
  } catch (error) {
    console.error('Error fetching tasks:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch tasks'
    });
  }
}));

// GET /api/tasks/:id - Get single task
router.get('/:id', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    
    const task = await prisma.task.findUnique({
      where: { id },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            company_name: true
          }
        },
        assignee: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        reviewer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        parent_task: {
          select: {
            id: true,
            name: true
          }
        },
        sub_tasks: {
          select: {
            id: true,
            name: true,
            status: true,
            priority_level: true
          }
        }
      }
    });

    if (!task) {
      return res.status(404).json({
        success: false,
        error: 'Task not found'
      });
    }

    res.json({
      success: true,
      task: task
    });
  } catch (error) {
    console.error('Error fetching task:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch task'
    });
  }
}));

// POST /api/tasks - Create new task
router.post('/', authMiddleware, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  try {
    const {
      name,
      description,
      project_id,
      assignee_id,
      reviewer_id,
      parent_task_id,
      priority_level,
      effort_estimate,
      due_date,
      status,
      tags,
      is_customer_visible,
      estimated_hours
    } = req.body;

    const task = await prisma.task.create({
      data: {
        name,
        description,
        project_id,
        assignee_id: assignee_id || null,
        reviewer_id: reviewer_id || null,
        parent_task_id: parent_task_id || null,
        priority_level: priority_level || 'P3',
        effort_estimate: effort_estimate || 'M',
        due_date: due_date ? new Date(due_date) : null,
        status: status || 'todo',
        tags: tags || [],
        is_customer_visible: is_customer_visible || false,
        estimated_hours: estimated_hours || null,
        external_links: []
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            company_name: true
          }
        },
        assignee: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      task: task
    });
  } catch (error) {
    console.error('Error creating task:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create task'
    });
  }
}));

export default router;
