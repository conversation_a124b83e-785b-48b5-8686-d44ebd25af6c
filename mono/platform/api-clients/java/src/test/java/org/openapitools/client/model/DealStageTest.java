/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

/**
 * Model tests for DealStage
 */
public class DealStageTest {
    private final DealStage model = new DealStage();

    /**
     * Model tests for DealStage
     */
    @Test
    public void testDealStage() {
        // TODO: test DealStage
    }

    /**
     * Test the property 'id'
     */
    @Test
    public void idTest() {
        // TODO: test id
    }

    /**
     * Test the property 'name'
     */
    @Test
    public void nameTest() {
        // TODO: test name
    }

    /**
     * Test the property 'pipelineOrder'
     */
    @Test
    public void pipelineOrderTest() {
        // TODO: test pipelineOrder
    }

    /**
     * Test the property 'isClosedWon'
     */
    @Test
    public void isClosedWonTest() {
        // TODO: test isClosedWon
    }

    /**
     * Test the property 'isClosedLost'
     */
    @Test
    public void isClosedLostTest() {
        // TODO: test isClosedLost
    }

    /**
     * Test the property 'createdAt'
     */
    @Test
    public void createdAtTest() {
        // TODO: test createdAt
    }

}
