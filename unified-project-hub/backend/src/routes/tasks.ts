import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireRole, canAccessProject } from '../middleware/auth';

const router = Router();
const prisma = new PrismaClient();

// Validation schemas
const createTaskSchema = z.object({
  project_id: z.string().uuid('Invalid project ID'),
  parent_task_id: z.string().uuid().optional(),
  name: z.string().min(1, 'Task name is required'),
  description: z.string().optional(),
  priority_level: z.enum(['P0', 'P1', 'P2', 'P3', 'P4']).default('P3'),
  effort_estimate: z.enum(['XS', 'S', 'M', 'L', 'XL', 'XXL']).default('M'),
  assignee_id: z.string().uuid('Invalid assignee ID'),
  reviewer_id: z.string().uuid().optional(),
  due_date: z.string().datetime().optional(),
  is_customer_visible: z.boolean().default(false),
  tags: z.array(z.string()).default([]),
  estimated_hours: z.number().positive().optional(),
});

/**
 * @route GET /api/tasks
 * @desc Get tasks (filtered by user role and permissions)
 * @access Private
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const {
    page = '1',
    limit = '10',
    status,
    project_id,
    assignee_id,
    reviewer_id,
    priority_level,
    effort_estimate,
    search,
    due_date_from,
    due_date_to,
    is_overdue = 'false',
    show_completed = 'true',
    parent_task_id,
    sort_by = 'updated_at',
    sort_order = 'desc',
    group_by = 'none'
  } = req.query;

  const skip = (parseInt(page as string) - 1) * parseInt(limit as string);

  let whereClause: any = {};

  // Filter based on user role
  if (req.user?.role === 'customer') {
    whereClause.is_customer_visible = true;
    whereClause.project = {
      customer_contact: { email: req.user.email },
    };
  } else if (req.user?.role === 'team_member') {
    whereClause.OR = [
      { assignee_id: req.user.id },
      { reviewer_id: req.user.id },
      { project: { project_lead_id: req.user.id } },
      { project: { team_members: { some: { user_id: req.user.id } } } },
    ];
  }

  // Core Project Pulse Advanced Filters
  if (status) whereClause.status = status;
  if (project_id) whereClause.project_id = project_id;
  if (assignee_id) whereClause.assignee_id = assignee_id;
  if (reviewer_id) whereClause.reviewer_id = reviewer_id;
  if (priority_level) whereClause.priority_level = priority_level;
  if (effort_estimate) whereClause.effort_estimate = effort_estimate;
  if (parent_task_id) whereClause.parent_task_id = parent_task_id;

  // Show/hide completed tasks
  if (show_completed === 'false') {
    whereClause.status = { not: 'done' };
  }

  // Date range filtering
  if (due_date_from || due_date_to) {
    whereClause.due_date = {};
    if (due_date_from) whereClause.due_date.gte = new Date(due_date_from as string);
    if (due_date_to) whereClause.due_date.lte = new Date(due_date_to as string);
  }

  // Overdue tasks filter
  if (is_overdue === 'true') {
    whereClause.due_date = { lt: new Date() };
    whereClause.status = { not: 'done' };
  }

  // Enhanced search across multiple fields
  if (search) {
    const searchConditions = [
      { name: { contains: search as string, mode: 'insensitive' } },
      { description: { contains: search as string, mode: 'insensitive' } },
      { tags: { has: search as string } },
      { project: { name: { contains: search as string, mode: 'insensitive' } } },
    ];

    // If there are existing OR conditions (from role filtering), combine them
    if (whereClause.OR) {
      whereClause.AND = [
        { OR: whereClause.OR },
        { OR: searchConditions }
      ];
      delete whereClause.OR;
    } else {
      whereClause.OR = searchConditions;
    }
  }

  // Sorting configuration
  const validSortFields = ['name', 'created_at', 'updated_at', 'due_date', 'priority_level', 'status', 'effort_estimate'];
  const sortField = validSortFields.includes(sort_by as string) ? sort_by as string : 'updated_at';
  const sortDirection = sort_order === 'asc' ? 'asc' : 'desc';

  let orderBy: any = { [sortField]: sortDirection };

  // Special sorting for priority levels (P0 should come first)
  if (sortField === 'priority_level') {
    orderBy = [
      { priority_level: sortDirection },
      { created_at: 'desc' } // Secondary sort
    ];
  }

  const [tasks, total] = await Promise.all([
    prisma.task.findMany({
      where: whereClause,
      include: {
        project: {
          select: {
            id: true,
            name: true,
            company_name: true,
            status: true,
            priority_level: true
          },
        },
        assignee: {
          select: { id: true, name: true, email: true, avatar_url: true },
        },
        reviewer: {
          select: { id: true, name: true, email: true, avatar_url: true },
        },
        parent_task: {
          select: {
            id: true,
            name: true,
            status: true,
            assignee: {
              select: { id: true, name: true, email: true, avatar_url: true }
            }
          },
        },
        sub_tasks: {
          include: {
            assignee: {
              select: { id: true, name: true, email: true, avatar_url: true },
            },
          },
          orderBy: { created_at: 'asc' },
        },
        requirements: {
          select: {
            requirement: {
              select: { id: true, title: true, status: true }
            }
          }
        },
        _count: {
          select: {
            sub_tasks: true,
          },
        },
      },
      skip,
      take: parseInt(limit as string),
      orderBy,
    }),
    prisma.task.count({ where: whereClause }),
  ]);

  // Calculate enhanced task data with Core Project Pulse features
  const enhancedTasks = tasks.map(task => {
    const today = new Date();
    let daysRemaining = null;
    let isOverdue = false;

    if (task.due_date) {
      const dueDate = new Date(task.due_date);
      const diffTime = dueDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      daysRemaining = diffDays;
      isOverdue = diffDays < 0 && task.status !== 'done';
    }

    // Calculate sub-task progress
    const totalSubTasks = task.sub_tasks.length;
    const completedSubTasks = task.sub_tasks.filter(subTask => subTask.status === 'done').length;
    const subTaskProgress = totalSubTasks > 0 ? Math.round((completedSubTasks / totalSubTasks) * 100) : 0;

    return {
      ...task,
      days_remaining: daysRemaining,
      is_overdue: isOverdue,
      sub_task_summary: {
        total: totalSubTasks,
        completed: completedSubTasks,
        in_progress: task.sub_tasks.filter(subTask => subTask.status === 'in_progress').length,
        to_do: task.sub_tasks.filter(subTask => subTask.status === 'to_do').length,
        progress_percentage: subTaskProgress,
      },
    };
  });

  // Group tasks if requested
  let groupedTasks = null;
  if (group_by !== 'none') {
    groupedTasks = enhancedTasks.reduce((groups: any, task: any) => {
      let groupKey = '';

      switch (group_by) {
        case 'status':
          groupKey = task.status || 'No Status';
          break;
        case 'priority_level':
          groupKey = task.priority_level || 'No Priority';
          break;
        case 'assignee':
          groupKey = task.assignee?.name || 'Unassigned';
          break;
        case 'project':
          groupKey = task.project?.name || 'No Project';
          break;
        case 'due_date':
          if (task.due_date) {
            const dueDate = new Date(task.due_date);
            groupKey = dueDate.toISOString().split('T')[0];
          } else {
            groupKey = 'No Due Date';
          }
          break;
        case 'effort_estimate':
          groupKey = task.effort_estimate || 'No Estimate';
          break;
        default:
          groupKey = 'Other';
      }

      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(task);
      return groups;
    }, {});
  }

  res.json({
    status: 'success',
    data: {
      tasks: enhancedTasks,
      grouped_tasks: groupedTasks,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        pages: Math.ceil(total / parseInt(limit as string)),
      },
      filters: {
        status,
        project_id,
        assignee_id,
        reviewer_id,
        priority_level,
        effort_estimate,
        search,
        due_date_from,
        due_date_to,
        is_overdue,
        show_completed,
        parent_task_id,
      },
      sorting: {
        sort_by: sortField,
        sort_order: sortDirection,
      },
      grouping: {
        group_by,
      },
    },
  });
}));

/**
 * @route POST /api/tasks
 * @desc Create new task
 * @access Private
 */
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createTaskSchema.parse(req.body);

  // Check if user can access the project
  const project = await prisma.project.findFirst({
    where: {
      id: validatedData.project_id,
      OR: [
        { project_lead_id: req.user!.id },
        { team_members: { some: { user_id: req.user!.id } } },
        ...(req.user!.role === 'admin' || req.user!.role === 'project_manager' ? [{}] : []),
      ],
    },
  });

  if (!project) {
    return res.status(404).json({
      status: 'error',
      message: 'Project not found or access denied',
    });
  }

  const task = await prisma.task.create({
    data: {
      ...validatedData,
      due_date: validatedData.due_date ? new Date(validatedData.due_date) : undefined,
    },
    include: {
      project: {
        select: { id: true, name: true, company_name: true },
      },
      assignee: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
      reviewer: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
    },
  });

  res.status(201).json({
    status: 'success',
    message: 'Task created successfully',
    data: { task },
  });
}));

/**
 * @route PUT /api/tasks/:id
 * @desc Update task
 * @access Private
 */
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const updateData = createTaskSchema.partial().parse(req.body);

  // Check if user can update this task
  const existingTask = await prisma.task.findFirst({
    where: {
      id,
      OR: [
        { assignee_id: req.user!.id },
        { reviewer_id: req.user!.id },
        { project: { project_lead_id: req.user!.id } },
        { project: { team_members: { some: { user_id: req.user!.id } } } },
        ...(req.user!.role === 'admin' || req.user!.role === 'project_manager' ? [{}] : []),
      ],
    },
  });

  if (!existingTask) {
    return res.status(404).json({
      status: 'error',
      message: 'Task not found or access denied',
    });
  }

  const task = await prisma.task.update({
    where: { id },
    data: {
      ...updateData,
      due_date: updateData.due_date ? new Date(updateData.due_date) : undefined,
      completed_at: updateData.status === 'done' ? new Date() : undefined,
    },
    include: {
      project: {
        select: { id: true, name: true, company_name: true },
      },
      assignee: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
      reviewer: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
    },
  });

  res.json({
    status: 'success',
    message: 'Task updated successfully',
    data: { task },
  });
}));

/**
 * @route DELETE /api/tasks/:id
 * @desc Delete task
 * @access Private
 */
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if user can delete this task
  const existingTask = await prisma.task.findFirst({
    where: {
      id,
      OR: [
        { project: { project_lead_id: req.user!.id } },
        ...(req.user!.role === 'admin' || req.user!.role === 'project_manager' ? [{}] : []),
      ],
    },
  });

  if (!existingTask) {
    return res.status(404).json({
      status: 'error',
      message: 'Task not found or access denied',
    });
  }

  await prisma.task.delete({
    where: { id },
  });

  res.json({
    status: 'success',
    message: 'Task deleted successfully',
  });
}));

/**
 * @route PATCH /api/tasks/:id/status
 * @desc Update task status with status change tracking
 * @access Private
 */
router.patch('/:id/status', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const { status, reason } = req.body;

  if (!status) {
    return res.status(400).json({
      status: 'error',
      message: 'Status is required',
    });
  }

  const validStatuses = ['to_do', 'in_progress', 'in_review', 'done', 'blocked'];
  if (!validStatuses.includes(status)) {
    return res.status(400).json({
      status: 'error',
      message: 'Invalid status value',
    });
  }

  // Check if user can access the task
  const existingTask = await prisma.task.findFirst({
    where: {
      id,
      OR: [
        { assignee_id: req.user!.id },
        { reviewer_id: req.user!.id },
        { project: { project_lead_id: req.user!.id } },
        { project: { team_members: { some: { user_id: req.user!.id } } } },
        ...(req.user!.role === 'admin' || req.user!.role === 'project_manager' ? [{}] : []),
      ],
    },
  });

  if (!existingTask) {
    return res.status(404).json({
      status: 'error',
      message: 'Task not found or access denied',
    });
  }

  const task = await prisma.task.update({
    where: { id },
    data: {
      status,
      completed_at: status === 'done' ? new Date() : null,
      updated_at: new Date(),
    },
    include: {
      assignee: {
        select: { id: true, name: true, email: true },
      },
      project: {
        select: { id: true, name: true },
      },
    },
  });

  res.json({
    status: 'success',
    data: { task },
    message: `Task status updated to ${status}`,
  });
}));

/**
 * @route PATCH /api/tasks/:id/priority
 * @desc Update task priority
 * @access Private
 */
router.patch('/:id/priority', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const { priority_level, reason } = req.body;

  if (!priority_level) {
    return res.status(400).json({
      status: 'error',
      message: 'Priority level is required',
    });
  }

  const validPriorities = ['P0', 'P1', 'P2', 'P3', 'P4'];
  if (!validPriorities.includes(priority_level)) {
    return res.status(400).json({
      status: 'error',
      message: 'Invalid priority level',
    });
  }

  // Check if user can access the task
  const existingTask = await prisma.task.findFirst({
    where: {
      id,
      OR: [
        { assignee_id: req.user!.id },
        { reviewer_id: req.user!.id },
        { project: { project_lead_id: req.user!.id } },
        { project: { team_members: { some: { user_id: req.user!.id } } } },
        ...(req.user!.role === 'admin' || req.user!.role === 'project_manager' ? [{}] : []),
      ],
    },
  });

  if (!existingTask) {
    return res.status(404).json({
      status: 'error',
      message: 'Task not found or access denied',
    });
  }

  const task = await prisma.task.update({
    where: { id },
    data: {
      priority_level,
      updated_at: new Date(),
    },
  });

  res.json({
    status: 'success',
    data: { task },
    message: `Task priority updated to ${priority_level}`,
  });
}));

/**
 * @route PATCH /api/tasks/:id/assign
 * @desc Assign task to user
 * @access Private
 */
router.patch('/:id/assign', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const { assignee_id, reviewer_id } = req.body;

  // Check if user can access the task
  const existingTask = await prisma.task.findFirst({
    where: {
      id,
      OR: [
        { assignee_id: req.user!.id },
        { reviewer_id: req.user!.id },
        { project: { project_lead_id: req.user!.id } },
        { project: { team_members: { some: { user_id: req.user!.id } } } },
        ...(req.user!.role === 'admin' || req.user!.role === 'project_manager' ? [{}] : []),
      ],
    },
  });

  if (!existingTask) {
    return res.status(404).json({
      status: 'error',
      message: 'Task not found or access denied',
    });
  }

  const updateData: any = { updated_at: new Date() };
  if (assignee_id !== undefined) updateData.assignee_id = assignee_id;
  if (reviewer_id !== undefined) updateData.reviewer_id = reviewer_id;

  const task = await prisma.task.update({
    where: { id },
    data: updateData,
    include: {
      assignee: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
      reviewer: {
        select: { id: true, name: true, email: true, avatar_url: true },
      },
    },
  });

  res.json({
    status: 'success',
    data: { task },
    message: 'Task assignment updated successfully',
  });
}));

export default router;
