import { useAuth } from '@/contexts/AuthContext'
import { formatDate } from '@/lib/utils'

export default function ProfilePage() {
  const { user } = useAuth()

  if (!user) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-gray-500">Loading profile...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Profile</h1>
        <p className="text-gray-600 mt-1">
          View and manage your profile information
        </p>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        {/* Profile Header */}
        <div className="bg-gradient-to-r from-primary to-primary/80 px-6 py-8">
          <div className="flex items-center space-x-6">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center text-2xl font-bold text-white">
              {user.name.split(' ').map(n => n[0]).join('')}
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">{user.name}</h2>
              <p className="text-primary-100">{user.title || 'Team Member'}</p>
              <p className="text-primary-100 capitalize">{user.role.replace('_', ' ')}</p>
            </div>
          </div>
        </div>

        {/* Profile Content */}
        <div className="px-6 py-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Basic Information */}
            <div className="lg:col-span-2 space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                <dl className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Full Name</dt>
                    <dd className="mt-1 text-sm text-gray-900">{user.name}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Email Address</dt>
                    <dd className="mt-1 text-sm text-gray-900">{user.email}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Job Title</dt>
                    <dd className="mt-1 text-sm text-gray-900">{user.title || 'Not specified'}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Department</dt>
                    <dd className="mt-1 text-sm text-gray-900">{user.department || 'Not specified'}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Role</dt>
                    <dd className="mt-1 text-sm text-gray-900 capitalize">{user.role.replace('_', ' ')}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Member Since</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatDate(user.created_at)}</dd>
                  </div>
                </dl>
              </div>

              {/* Skills */}
              {user.skills && user.skills.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Skills</h3>
                  <div className="flex flex-wrap gap-2">
                    {user.skills.map((skill, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Work Information */}
              {(user.weekly_capacity_hours || user.current_utilization_percentage) && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Work Information</h3>
                  <dl className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {user.weekly_capacity_hours && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Weekly Capacity</dt>
                        <dd className="mt-1 text-sm text-gray-900">{user.weekly_capacity_hours} hours</dd>
                      </div>
                    )}
                    {user.current_utilization_percentage && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Current Utilization</dt>
                        <dd className="mt-1 text-sm text-gray-900">{user.current_utilization_percentage}%</dd>
                      </div>
                    )}
                  </dl>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  <button className="w-full bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/90 transition-colors text-sm">
                    Edit Profile
                  </button>
                  <button className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors text-sm">
                    Change Password
                  </button>
                  <button className="w-full bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors text-sm">
                    Download Data
                  </button>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Account Status</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Status</span>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {user.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Last Updated</span>
                    <span className="text-sm text-gray-900">{formatDate(user.updated_at)}</span>
                  </div>
                </div>
              </div>

              {/* Role-specific information */}
              {user.role === 'customer' && (
                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-900 mb-2">Customer Access</h4>
                  <p className="text-sm text-blue-700">
                    You have access to view your project progress and communicate with your project team.
                  </p>
                </div>
              )}

              {(user.role === 'admin' || user.role === 'project_manager') && (
                <div className="bg-green-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-green-900 mb-2">Management Access</h4>
                  <p className="text-sm text-green-700">
                    You have full access to manage projects, teams, and system settings.
                  </p>
                </div>
              )}

              {user.role === 'requirements_analyst' && (
                <div className="bg-purple-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-purple-900 mb-2">Analyst Access</h4>
                  <p className="text-sm text-purple-700">
                    You can create and manage requirements, PRDs, and project documentation.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
