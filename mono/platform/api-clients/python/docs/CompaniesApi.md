# crm_api_client.CompaniesApi

All URIs are relative to *https://api.crm.example.com/v1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**companies_get**](CompaniesApi.md#companies_get) | **GET** /companies | List companies
[**companies_id_delete**](CompaniesApi.md#companies_id_delete) | **DELETE** /companies/{id} | Delete company
[**companies_id_get**](CompaniesApi.md#companies_id_get) | **GET** /companies/{id} | Get company
[**companies_id_put**](CompaniesApi.md#companies_id_put) | **PUT** /companies/{id} | Update company
[**companies_id_status_put**](CompaniesApi.md#companies_id_status_put) | **PUT** /companies/{id}/status | Update company status
[**companies_post**](CompaniesApi.md#companies_post) | **POST** /companies | Create company
[**company_statuses_get**](CompaniesApi.md#company_statuses_get) | **GET** /company-statuses | List company statuses


# **companies_get**
> List[Company] companies_get(status_id=status_id, include_deleted=include_deleted)

List companies

Retrieve a list of all active companies

### Example

* Bearer (JWT) Authentication (bearerAuth):

```python
import crm_api_client
from crm_api_client.models.company import Company
from crm_api_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to https://api.crm.example.com/v1
# See configuration.py for a list of all supported configuration parameters.
configuration = crm_api_client.Configuration(
    host = "https://api.crm.example.com/v1"
)

# The client must configure the authentication and authorization parameters
# in accordance with the API server security policy.
# Examples for each auth method are provided below, use the example that
# satisfies your auth use case.

# Configure Bearer authorization (JWT): bearerAuth
configuration = crm_api_client.Configuration(
    access_token = os.environ["BEARER_TOKEN"]
)

# Enter a context with an instance of the API client
with crm_api_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = crm_api_client.CompaniesApi(api_client)
    status_id = 'status_id_example' # str | Filter by company status (optional)
    include_deleted = False # bool | Include soft-deleted companies (optional) (default to False)

    try:
        # List companies
        api_response = api_instance.companies_get(status_id=status_id, include_deleted=include_deleted)
        print("The response of CompaniesApi->companies_get:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling CompaniesApi->companies_get: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **status_id** | **str**| Filter by company status | [optional] 
 **include_deleted** | **bool**| Include soft-deleted companies | [optional] [default to False]

### Return type

[**List[Company]**](Company.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | List of companies |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **companies_id_delete**
> companies_id_delete(id)

Delete company

Soft delete a company (sets is_deleted to true)

### Example

* Bearer (JWT) Authentication (bearerAuth):

```python
import crm_api_client
from crm_api_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to https://api.crm.example.com/v1
# See configuration.py for a list of all supported configuration parameters.
configuration = crm_api_client.Configuration(
    host = "https://api.crm.example.com/v1"
)

# The client must configure the authentication and authorization parameters
# in accordance with the API server security policy.
# Examples for each auth method are provided below, use the example that
# satisfies your auth use case.

# Configure Bearer authorization (JWT): bearerAuth
configuration = crm_api_client.Configuration(
    access_token = os.environ["BEARER_TOKEN"]
)

# Enter a context with an instance of the API client
with crm_api_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = crm_api_client.CompaniesApi(api_client)
    id = 'id_example' # str | 

    try:
        # Delete company
        api_instance.companies_id_delete(id)
    except Exception as e:
        print("Exception when calling CompaniesApi->companies_id_delete: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **str**|  | 

### Return type

void (empty response body)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**204** | Company deleted successfully |  -  |
**404** | Company not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **companies_id_get**
> CompanyDetails companies_id_get(id)

Get company

Retrieve a specific company by ID

### Example

* Bearer (JWT) Authentication (bearerAuth):

```python
import crm_api_client
from crm_api_client.models.company_details import CompanyDetails
from crm_api_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to https://api.crm.example.com/v1
# See configuration.py for a list of all supported configuration parameters.
configuration = crm_api_client.Configuration(
    host = "https://api.crm.example.com/v1"
)

# The client must configure the authentication and authorization parameters
# in accordance with the API server security policy.
# Examples for each auth method are provided below, use the example that
# satisfies your auth use case.

# Configure Bearer authorization (JWT): bearerAuth
configuration = crm_api_client.Configuration(
    access_token = os.environ["BEARER_TOKEN"]
)

# Enter a context with an instance of the API client
with crm_api_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = crm_api_client.CompaniesApi(api_client)
    id = 'id_example' # str | 

    try:
        # Get company
        api_response = api_instance.companies_id_get(id)
        print("The response of CompaniesApi->companies_id_get:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling CompaniesApi->companies_id_get: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **str**|  | 

### Return type

[**CompanyDetails**](CompanyDetails.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Company details |  -  |
**404** | Company not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **companies_id_put**
> Company companies_id_put(id, company_update)

Update company

Update an existing company

### Example

* Bearer (JWT) Authentication (bearerAuth):

```python
import crm_api_client
from crm_api_client.models.company import Company
from crm_api_client.models.company_update import CompanyUpdate
from crm_api_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to https://api.crm.example.com/v1
# See configuration.py for a list of all supported configuration parameters.
configuration = crm_api_client.Configuration(
    host = "https://api.crm.example.com/v1"
)

# The client must configure the authentication and authorization parameters
# in accordance with the API server security policy.
# Examples for each auth method are provided below, use the example that
# satisfies your auth use case.

# Configure Bearer authorization (JWT): bearerAuth
configuration = crm_api_client.Configuration(
    access_token = os.environ["BEARER_TOKEN"]
)

# Enter a context with an instance of the API client
with crm_api_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = crm_api_client.CompaniesApi(api_client)
    id = 'id_example' # str | 
    company_update = crm_api_client.CompanyUpdate() # CompanyUpdate | 

    try:
        # Update company
        api_response = api_instance.companies_id_put(id, company_update)
        print("The response of CompaniesApi->companies_id_put:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling CompaniesApi->companies_id_put: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **str**|  | 
 **company_update** | [**CompanyUpdate**](CompanyUpdate.md)|  | 

### Return type

[**Company**](Company.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Company updated successfully |  -  |
**404** | Company not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **companies_id_status_put**
> companies_id_status_put(id, company_status_update_request)

Update company status

Update the status of a company

### Example

* Bearer (JWT) Authentication (bearerAuth):

```python
import crm_api_client
from crm_api_client.models.company_status_update_request import CompanyStatusUpdateRequest
from crm_api_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to https://api.crm.example.com/v1
# See configuration.py for a list of all supported configuration parameters.
configuration = crm_api_client.Configuration(
    host = "https://api.crm.example.com/v1"
)

# The client must configure the authentication and authorization parameters
# in accordance with the API server security policy.
# Examples for each auth method are provided below, use the example that
# satisfies your auth use case.

# Configure Bearer authorization (JWT): bearerAuth
configuration = crm_api_client.Configuration(
    access_token = os.environ["BEARER_TOKEN"]
)

# Enter a context with an instance of the API client
with crm_api_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = crm_api_client.CompaniesApi(api_client)
    id = 'id_example' # str | 
    company_status_update_request = crm_api_client.CompanyStatusUpdateRequest() # CompanyStatusUpdateRequest | 

    try:
        # Update company status
        api_instance.companies_id_status_put(id, company_status_update_request)
    except Exception as e:
        print("Exception when calling CompaniesApi->companies_id_status_put: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **str**|  | 
 **company_status_update_request** | [**CompanyStatusUpdateRequest**](CompanyStatusUpdateRequest.md)|  | 

### Return type

void (empty response body)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Company status updated successfully |  -  |
**404** | Company not found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **companies_post**
> Company companies_post(company_create)

Create company

Create a new company

### Example

* Bearer (JWT) Authentication (bearerAuth):

```python
import crm_api_client
from crm_api_client.models.company import Company
from crm_api_client.models.company_create import CompanyCreate
from crm_api_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to https://api.crm.example.com/v1
# See configuration.py for a list of all supported configuration parameters.
configuration = crm_api_client.Configuration(
    host = "https://api.crm.example.com/v1"
)

# The client must configure the authentication and authorization parameters
# in accordance with the API server security policy.
# Examples for each auth method are provided below, use the example that
# satisfies your auth use case.

# Configure Bearer authorization (JWT): bearerAuth
configuration = crm_api_client.Configuration(
    access_token = os.environ["BEARER_TOKEN"]
)

# Enter a context with an instance of the API client
with crm_api_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = crm_api_client.CompaniesApi(api_client)
    company_create = crm_api_client.CompanyCreate() # CompanyCreate | 

    try:
        # Create company
        api_response = api_instance.companies_post(company_create)
        print("The response of CompaniesApi->companies_post:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling CompaniesApi->companies_post: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **company_create** | [**CompanyCreate**](CompanyCreate.md)|  | 

### Return type

[**Company**](Company.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**201** | Company created successfully |  -  |
**400** | Invalid input |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **company_statuses_get**
> List[CompanyStatus] company_statuses_get()

List company statuses

Retrieve all company statuses ordered by pipeline order

### Example

* Bearer (JWT) Authentication (bearerAuth):

```python
import crm_api_client
from crm_api_client.models.company_status import CompanyStatus
from crm_api_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to https://api.crm.example.com/v1
# See configuration.py for a list of all supported configuration parameters.
configuration = crm_api_client.Configuration(
    host = "https://api.crm.example.com/v1"
)

# The client must configure the authentication and authorization parameters
# in accordance with the API server security policy.
# Examples for each auth method are provided below, use the example that
# satisfies your auth use case.

# Configure Bearer authorization (JWT): bearerAuth
configuration = crm_api_client.Configuration(
    access_token = os.environ["BEARER_TOKEN"]
)

# Enter a context with an instance of the API client
with crm_api_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = crm_api_client.CompaniesApi(api_client)

    try:
        # List company statuses
        api_response = api_instance.company_statuses_get()
        print("The response of CompaniesApi->company_statuses_get:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling CompaniesApi->company_statuses_get: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**List[CompanyStatus]**](CompanyStatus.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | List of company statuses |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

