/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"time"
)

// checks if the Deal type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &Deal{}

// Deal struct for Deal
type Deal struct {
	Id *string `json:"id,omitempty"`
	Title *string `json:"title,omitempty"`
	Description *string `json:"description,omitempty"`
	EstimatedValue *float64 `json:"estimated_value,omitempty"`
	CompanyId *string `json:"company_id,omitempty"`
	DealStageId *string `json:"deal_stage_id,omitempty"`
	ExpectedCloseDate *string `json:"expected_close_date,omitempty"`
	CreatedBy *string `json:"created_by,omitempty"`
	CreatedAt *time.Time `json:"created_at,omitempty"`
	UpdatedAt *time.Time `json:"updated_at,omitempty"`
}

// NewDeal instantiates a new Deal object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDeal() *Deal {
	this := Deal{}
	return &this
}

// NewDealWithDefaults instantiates a new Deal object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDealWithDefaults() *Deal {
	this := Deal{}
	return &this
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *Deal) GetId() string {
	if o == nil || IsNil(o.Id) {
		var ret string
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Deal) GetIdOk() (*string, bool) {
	if o == nil || IsNil(o.Id) {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *Deal) HasId() bool {
	if o != nil && !IsNil(o.Id) {
		return true
	}

	return false
}

// SetId gets a reference to the given string and assigns it to the Id field.
func (o *Deal) SetId(v string) {
	o.Id = &v
}

// GetTitle returns the Title field value if set, zero value otherwise.
func (o *Deal) GetTitle() string {
	if o == nil || IsNil(o.Title) {
		var ret string
		return ret
	}
	return *o.Title
}

// GetTitleOk returns a tuple with the Title field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Deal) GetTitleOk() (*string, bool) {
	if o == nil || IsNil(o.Title) {
		return nil, false
	}
	return o.Title, true
}

// HasTitle returns a boolean if a field has been set.
func (o *Deal) HasTitle() bool {
	if o != nil && !IsNil(o.Title) {
		return true
	}

	return false
}

// SetTitle gets a reference to the given string and assigns it to the Title field.
func (o *Deal) SetTitle(v string) {
	o.Title = &v
}

// GetDescription returns the Description field value if set, zero value otherwise.
func (o *Deal) GetDescription() string {
	if o == nil || IsNil(o.Description) {
		var ret string
		return ret
	}
	return *o.Description
}

// GetDescriptionOk returns a tuple with the Description field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Deal) GetDescriptionOk() (*string, bool) {
	if o == nil || IsNil(o.Description) {
		return nil, false
	}
	return o.Description, true
}

// HasDescription returns a boolean if a field has been set.
func (o *Deal) HasDescription() bool {
	if o != nil && !IsNil(o.Description) {
		return true
	}

	return false
}

// SetDescription gets a reference to the given string and assigns it to the Description field.
func (o *Deal) SetDescription(v string) {
	o.Description = &v
}

// GetEstimatedValue returns the EstimatedValue field value if set, zero value otherwise.
func (o *Deal) GetEstimatedValue() float64 {
	if o == nil || IsNil(o.EstimatedValue) {
		var ret float64
		return ret
	}
	return *o.EstimatedValue
}

// GetEstimatedValueOk returns a tuple with the EstimatedValue field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Deal) GetEstimatedValueOk() (*float64, bool) {
	if o == nil || IsNil(o.EstimatedValue) {
		return nil, false
	}
	return o.EstimatedValue, true
}

// HasEstimatedValue returns a boolean if a field has been set.
func (o *Deal) HasEstimatedValue() bool {
	if o != nil && !IsNil(o.EstimatedValue) {
		return true
	}

	return false
}

// SetEstimatedValue gets a reference to the given float64 and assigns it to the EstimatedValue field.
func (o *Deal) SetEstimatedValue(v float64) {
	o.EstimatedValue = &v
}

// GetCompanyId returns the CompanyId field value if set, zero value otherwise.
func (o *Deal) GetCompanyId() string {
	if o == nil || IsNil(o.CompanyId) {
		var ret string
		return ret
	}
	return *o.CompanyId
}

// GetCompanyIdOk returns a tuple with the CompanyId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Deal) GetCompanyIdOk() (*string, bool) {
	if o == nil || IsNil(o.CompanyId) {
		return nil, false
	}
	return o.CompanyId, true
}

// HasCompanyId returns a boolean if a field has been set.
func (o *Deal) HasCompanyId() bool {
	if o != nil && !IsNil(o.CompanyId) {
		return true
	}

	return false
}

// SetCompanyId gets a reference to the given string and assigns it to the CompanyId field.
func (o *Deal) SetCompanyId(v string) {
	o.CompanyId = &v
}

// GetDealStageId returns the DealStageId field value if set, zero value otherwise.
func (o *Deal) GetDealStageId() string {
	if o == nil || IsNil(o.DealStageId) {
		var ret string
		return ret
	}
	return *o.DealStageId
}

// GetDealStageIdOk returns a tuple with the DealStageId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Deal) GetDealStageIdOk() (*string, bool) {
	if o == nil || IsNil(o.DealStageId) {
		return nil, false
	}
	return o.DealStageId, true
}

// HasDealStageId returns a boolean if a field has been set.
func (o *Deal) HasDealStageId() bool {
	if o != nil && !IsNil(o.DealStageId) {
		return true
	}

	return false
}

// SetDealStageId gets a reference to the given string and assigns it to the DealStageId field.
func (o *Deal) SetDealStageId(v string) {
	o.DealStageId = &v
}

// GetExpectedCloseDate returns the ExpectedCloseDate field value if set, zero value otherwise.
func (o *Deal) GetExpectedCloseDate() string {
	if o == nil || IsNil(o.ExpectedCloseDate) {
		var ret string
		return ret
	}
	return *o.ExpectedCloseDate
}

// GetExpectedCloseDateOk returns a tuple with the ExpectedCloseDate field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Deal) GetExpectedCloseDateOk() (*string, bool) {
	if o == nil || IsNil(o.ExpectedCloseDate) {
		return nil, false
	}
	return o.ExpectedCloseDate, true
}

// HasExpectedCloseDate returns a boolean if a field has been set.
func (o *Deal) HasExpectedCloseDate() bool {
	if o != nil && !IsNil(o.ExpectedCloseDate) {
		return true
	}

	return false
}

// SetExpectedCloseDate gets a reference to the given string and assigns it to the ExpectedCloseDate field.
func (o *Deal) SetExpectedCloseDate(v string) {
	o.ExpectedCloseDate = &v
}

// GetCreatedBy returns the CreatedBy field value if set, zero value otherwise.
func (o *Deal) GetCreatedBy() string {
	if o == nil || IsNil(o.CreatedBy) {
		var ret string
		return ret
	}
	return *o.CreatedBy
}

// GetCreatedByOk returns a tuple with the CreatedBy field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Deal) GetCreatedByOk() (*string, bool) {
	if o == nil || IsNil(o.CreatedBy) {
		return nil, false
	}
	return o.CreatedBy, true
}

// HasCreatedBy returns a boolean if a field has been set.
func (o *Deal) HasCreatedBy() bool {
	if o != nil && !IsNil(o.CreatedBy) {
		return true
	}

	return false
}

// SetCreatedBy gets a reference to the given string and assigns it to the CreatedBy field.
func (o *Deal) SetCreatedBy(v string) {
	o.CreatedBy = &v
}

// GetCreatedAt returns the CreatedAt field value if set, zero value otherwise.
func (o *Deal) GetCreatedAt() time.Time {
	if o == nil || IsNil(o.CreatedAt) {
		var ret time.Time
		return ret
	}
	return *o.CreatedAt
}

// GetCreatedAtOk returns a tuple with the CreatedAt field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Deal) GetCreatedAtOk() (*time.Time, bool) {
	if o == nil || IsNil(o.CreatedAt) {
		return nil, false
	}
	return o.CreatedAt, true
}

// HasCreatedAt returns a boolean if a field has been set.
func (o *Deal) HasCreatedAt() bool {
	if o != nil && !IsNil(o.CreatedAt) {
		return true
	}

	return false
}

// SetCreatedAt gets a reference to the given time.Time and assigns it to the CreatedAt field.
func (o *Deal) SetCreatedAt(v time.Time) {
	o.CreatedAt = &v
}

// GetUpdatedAt returns the UpdatedAt field value if set, zero value otherwise.
func (o *Deal) GetUpdatedAt() time.Time {
	if o == nil || IsNil(o.UpdatedAt) {
		var ret time.Time
		return ret
	}
	return *o.UpdatedAt
}

// GetUpdatedAtOk returns a tuple with the UpdatedAt field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Deal) GetUpdatedAtOk() (*time.Time, bool) {
	if o == nil || IsNil(o.UpdatedAt) {
		return nil, false
	}
	return o.UpdatedAt, true
}

// HasUpdatedAt returns a boolean if a field has been set.
func (o *Deal) HasUpdatedAt() bool {
	if o != nil && !IsNil(o.UpdatedAt) {
		return true
	}

	return false
}

// SetUpdatedAt gets a reference to the given time.Time and assigns it to the UpdatedAt field.
func (o *Deal) SetUpdatedAt(v time.Time) {
	o.UpdatedAt = &v
}

func (o Deal) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o Deal) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Id) {
		toSerialize["id"] = o.Id
	}
	if !IsNil(o.Title) {
		toSerialize["title"] = o.Title
	}
	if !IsNil(o.Description) {
		toSerialize["description"] = o.Description
	}
	if !IsNil(o.EstimatedValue) {
		toSerialize["estimated_value"] = o.EstimatedValue
	}
	if !IsNil(o.CompanyId) {
		toSerialize["company_id"] = o.CompanyId
	}
	if !IsNil(o.DealStageId) {
		toSerialize["deal_stage_id"] = o.DealStageId
	}
	if !IsNil(o.ExpectedCloseDate) {
		toSerialize["expected_close_date"] = o.ExpectedCloseDate
	}
	if !IsNil(o.CreatedBy) {
		toSerialize["created_by"] = o.CreatedBy
	}
	if !IsNil(o.CreatedAt) {
		toSerialize["created_at"] = o.CreatedAt
	}
	if !IsNil(o.UpdatedAt) {
		toSerialize["updated_at"] = o.UpdatedAt
	}
	return toSerialize, nil
}

type NullableDeal struct {
	value *Deal
	isSet bool
}

func (v NullableDeal) Get() *Deal {
	return v.value
}

func (v *NullableDeal) Set(val *Deal) {
	v.value = val
	v.isSet = true
}

func (v NullableDeal) IsSet() bool {
	return v.isSet
}

func (v *NullableDeal) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDeal(val *Deal) *NullableDeal {
	return &NullableDeal{value: val, isSet: true}
}

func (v NullableDeal) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDeal) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


