/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
)

// checks if the ArliDocumentsVectorizePost200Response type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ArliDocumentsVectorizePost200Response{}

// ArliDocumentsVectorizePost200Response struct for ArliDocumentsVectorizePost200Response
type ArliDocumentsVectorizePost200Response struct {
	Success *bool `json:"success,omitempty"`
	Message *string `json:"message,omitempty"`
	VectorId *string `json:"vector_id,omitempty"`
}

// NewArliDocumentsVectorizePost200Response instantiates a new ArliDocumentsVectorizePost200Response object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewArliDocumentsVectorizePost200Response() *ArliDocumentsVectorizePost200Response {
	this := ArliDocumentsVectorizePost200Response{}
	return &this
}

// NewArliDocumentsVectorizePost200ResponseWithDefaults instantiates a new ArliDocumentsVectorizePost200Response object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewArliDocumentsVectorizePost200ResponseWithDefaults() *ArliDocumentsVectorizePost200Response {
	this := ArliDocumentsVectorizePost200Response{}
	return &this
}

// GetSuccess returns the Success field value if set, zero value otherwise.
func (o *ArliDocumentsVectorizePost200Response) GetSuccess() bool {
	if o == nil || IsNil(o.Success) {
		var ret bool
		return ret
	}
	return *o.Success
}

// GetSuccessOk returns a tuple with the Success field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ArliDocumentsVectorizePost200Response) GetSuccessOk() (*bool, bool) {
	if o == nil || IsNil(o.Success) {
		return nil, false
	}
	return o.Success, true
}

// HasSuccess returns a boolean if a field has been set.
func (o *ArliDocumentsVectorizePost200Response) HasSuccess() bool {
	if o != nil && !IsNil(o.Success) {
		return true
	}

	return false
}

// SetSuccess gets a reference to the given bool and assigns it to the Success field.
func (o *ArliDocumentsVectorizePost200Response) SetSuccess(v bool) {
	o.Success = &v
}

// GetMessage returns the Message field value if set, zero value otherwise.
func (o *ArliDocumentsVectorizePost200Response) GetMessage() string {
	if o == nil || IsNil(o.Message) {
		var ret string
		return ret
	}
	return *o.Message
}

// GetMessageOk returns a tuple with the Message field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ArliDocumentsVectorizePost200Response) GetMessageOk() (*string, bool) {
	if o == nil || IsNil(o.Message) {
		return nil, false
	}
	return o.Message, true
}

// HasMessage returns a boolean if a field has been set.
func (o *ArliDocumentsVectorizePost200Response) HasMessage() bool {
	if o != nil && !IsNil(o.Message) {
		return true
	}

	return false
}

// SetMessage gets a reference to the given string and assigns it to the Message field.
func (o *ArliDocumentsVectorizePost200Response) SetMessage(v string) {
	o.Message = &v
}

// GetVectorId returns the VectorId field value if set, zero value otherwise.
func (o *ArliDocumentsVectorizePost200Response) GetVectorId() string {
	if o == nil || IsNil(o.VectorId) {
		var ret string
		return ret
	}
	return *o.VectorId
}

// GetVectorIdOk returns a tuple with the VectorId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ArliDocumentsVectorizePost200Response) GetVectorIdOk() (*string, bool) {
	if o == nil || IsNil(o.VectorId) {
		return nil, false
	}
	return o.VectorId, true
}

// HasVectorId returns a boolean if a field has been set.
func (o *ArliDocumentsVectorizePost200Response) HasVectorId() bool {
	if o != nil && !IsNil(o.VectorId) {
		return true
	}

	return false
}

// SetVectorId gets a reference to the given string and assigns it to the VectorId field.
func (o *ArliDocumentsVectorizePost200Response) SetVectorId(v string) {
	o.VectorId = &v
}

func (o ArliDocumentsVectorizePost200Response) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ArliDocumentsVectorizePost200Response) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Success) {
		toSerialize["success"] = o.Success
	}
	if !IsNil(o.Message) {
		toSerialize["message"] = o.Message
	}
	if !IsNil(o.VectorId) {
		toSerialize["vector_id"] = o.VectorId
	}
	return toSerialize, nil
}

type NullableArliDocumentsVectorizePost200Response struct {
	value *ArliDocumentsVectorizePost200Response
	isSet bool
}

func (v NullableArliDocumentsVectorizePost200Response) Get() *ArliDocumentsVectorizePost200Response {
	return v.value
}

func (v *NullableArliDocumentsVectorizePost200Response) Set(val *ArliDocumentsVectorizePost200Response) {
	v.value = val
	v.isSet = true
}

func (v NullableArliDocumentsVectorizePost200Response) IsSet() bool {
	return v.isSet
}

func (v *NullableArliDocumentsVectorizePost200Response) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableArliDocumentsVectorizePost200Response(val *ArliDocumentsVectorizePost200Response) *NullableArliDocumentsVectorizePost200Response {
	return &NullableArliDocumentsVectorizePost200Response{value: val, isSet: true}
}

func (v NullableArliDocumentsVectorizePost200Response) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableArliDocumentsVectorizePost200Response) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


