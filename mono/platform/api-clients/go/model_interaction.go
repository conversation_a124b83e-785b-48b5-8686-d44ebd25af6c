/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"time"
)

// checks if the Interaction type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &Interaction{}

// Interaction struct for Interaction
type Interaction struct {
	Id *string `json:"id,omitempty"`
	CompanyId NullableString `json:"company_id,omitempty"`
	ContactId NullableString `json:"contact_id,omitempty"`
	InteractionType *string `json:"interaction_type,omitempty"`
	Notes *string `json:"notes,omitempty"`
	InteractionDatetime *time.Time `json:"interaction_datetime,omitempty"`
	CreatedBy *string `json:"created_by,omitempty"`
	CreatedAt *time.Time `json:"created_at,omitempty"`
	UpdatedAt *time.Time `json:"updated_at,omitempty"`
}

// NewInteraction instantiates a new Interaction object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewInteraction() *Interaction {
	this := Interaction{}
	return &this
}

// NewInteractionWithDefaults instantiates a new Interaction object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewInteractionWithDefaults() *Interaction {
	this := Interaction{}
	return &this
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *Interaction) GetId() string {
	if o == nil || IsNil(o.Id) {
		var ret string
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Interaction) GetIdOk() (*string, bool) {
	if o == nil || IsNil(o.Id) {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *Interaction) HasId() bool {
	if o != nil && !IsNil(o.Id) {
		return true
	}

	return false
}

// SetId gets a reference to the given string and assigns it to the Id field.
func (o *Interaction) SetId(v string) {
	o.Id = &v
}

// GetCompanyId returns the CompanyId field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *Interaction) GetCompanyId() string {
	if o == nil || IsNil(o.CompanyId.Get()) {
		var ret string
		return ret
	}
	return *o.CompanyId.Get()
}

// GetCompanyIdOk returns a tuple with the CompanyId field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *Interaction) GetCompanyIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.CompanyId.Get(), o.CompanyId.IsSet()
}

// HasCompanyId returns a boolean if a field has been set.
func (o *Interaction) HasCompanyId() bool {
	if o != nil && o.CompanyId.IsSet() {
		return true
	}

	return false
}

// SetCompanyId gets a reference to the given NullableString and assigns it to the CompanyId field.
func (o *Interaction) SetCompanyId(v string) {
	o.CompanyId.Set(&v)
}
// SetCompanyIdNil sets the value for CompanyId to be an explicit nil
func (o *Interaction) SetCompanyIdNil() {
	o.CompanyId.Set(nil)
}

// UnsetCompanyId ensures that no value is present for CompanyId, not even an explicit nil
func (o *Interaction) UnsetCompanyId() {
	o.CompanyId.Unset()
}

// GetContactId returns the ContactId field value if set, zero value otherwise (both if not set or set to explicit null).
func (o *Interaction) GetContactId() string {
	if o == nil || IsNil(o.ContactId.Get()) {
		var ret string
		return ret
	}
	return *o.ContactId.Get()
}

// GetContactIdOk returns a tuple with the ContactId field value if set, nil otherwise
// and a boolean to check if the value has been set.
// NOTE: If the value is an explicit nil, `nil, true` will be returned
func (o *Interaction) GetContactIdOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return o.ContactId.Get(), o.ContactId.IsSet()
}

// HasContactId returns a boolean if a field has been set.
func (o *Interaction) HasContactId() bool {
	if o != nil && o.ContactId.IsSet() {
		return true
	}

	return false
}

// SetContactId gets a reference to the given NullableString and assigns it to the ContactId field.
func (o *Interaction) SetContactId(v string) {
	o.ContactId.Set(&v)
}
// SetContactIdNil sets the value for ContactId to be an explicit nil
func (o *Interaction) SetContactIdNil() {
	o.ContactId.Set(nil)
}

// UnsetContactId ensures that no value is present for ContactId, not even an explicit nil
func (o *Interaction) UnsetContactId() {
	o.ContactId.Unset()
}

// GetInteractionType returns the InteractionType field value if set, zero value otherwise.
func (o *Interaction) GetInteractionType() string {
	if o == nil || IsNil(o.InteractionType) {
		var ret string
		return ret
	}
	return *o.InteractionType
}

// GetInteractionTypeOk returns a tuple with the InteractionType field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Interaction) GetInteractionTypeOk() (*string, bool) {
	if o == nil || IsNil(o.InteractionType) {
		return nil, false
	}
	return o.InteractionType, true
}

// HasInteractionType returns a boolean if a field has been set.
func (o *Interaction) HasInteractionType() bool {
	if o != nil && !IsNil(o.InteractionType) {
		return true
	}

	return false
}

// SetInteractionType gets a reference to the given string and assigns it to the InteractionType field.
func (o *Interaction) SetInteractionType(v string) {
	o.InteractionType = &v
}

// GetNotes returns the Notes field value if set, zero value otherwise.
func (o *Interaction) GetNotes() string {
	if o == nil || IsNil(o.Notes) {
		var ret string
		return ret
	}
	return *o.Notes
}

// GetNotesOk returns a tuple with the Notes field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Interaction) GetNotesOk() (*string, bool) {
	if o == nil || IsNil(o.Notes) {
		return nil, false
	}
	return o.Notes, true
}

// HasNotes returns a boolean if a field has been set.
func (o *Interaction) HasNotes() bool {
	if o != nil && !IsNil(o.Notes) {
		return true
	}

	return false
}

// SetNotes gets a reference to the given string and assigns it to the Notes field.
func (o *Interaction) SetNotes(v string) {
	o.Notes = &v
}

// GetInteractionDatetime returns the InteractionDatetime field value if set, zero value otherwise.
func (o *Interaction) GetInteractionDatetime() time.Time {
	if o == nil || IsNil(o.InteractionDatetime) {
		var ret time.Time
		return ret
	}
	return *o.InteractionDatetime
}

// GetInteractionDatetimeOk returns a tuple with the InteractionDatetime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Interaction) GetInteractionDatetimeOk() (*time.Time, bool) {
	if o == nil || IsNil(o.InteractionDatetime) {
		return nil, false
	}
	return o.InteractionDatetime, true
}

// HasInteractionDatetime returns a boolean if a field has been set.
func (o *Interaction) HasInteractionDatetime() bool {
	if o != nil && !IsNil(o.InteractionDatetime) {
		return true
	}

	return false
}

// SetInteractionDatetime gets a reference to the given time.Time and assigns it to the InteractionDatetime field.
func (o *Interaction) SetInteractionDatetime(v time.Time) {
	o.InteractionDatetime = &v
}

// GetCreatedBy returns the CreatedBy field value if set, zero value otherwise.
func (o *Interaction) GetCreatedBy() string {
	if o == nil || IsNil(o.CreatedBy) {
		var ret string
		return ret
	}
	return *o.CreatedBy
}

// GetCreatedByOk returns a tuple with the CreatedBy field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Interaction) GetCreatedByOk() (*string, bool) {
	if o == nil || IsNil(o.CreatedBy) {
		return nil, false
	}
	return o.CreatedBy, true
}

// HasCreatedBy returns a boolean if a field has been set.
func (o *Interaction) HasCreatedBy() bool {
	if o != nil && !IsNil(o.CreatedBy) {
		return true
	}

	return false
}

// SetCreatedBy gets a reference to the given string and assigns it to the CreatedBy field.
func (o *Interaction) SetCreatedBy(v string) {
	o.CreatedBy = &v
}

// GetCreatedAt returns the CreatedAt field value if set, zero value otherwise.
func (o *Interaction) GetCreatedAt() time.Time {
	if o == nil || IsNil(o.CreatedAt) {
		var ret time.Time
		return ret
	}
	return *o.CreatedAt
}

// GetCreatedAtOk returns a tuple with the CreatedAt field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Interaction) GetCreatedAtOk() (*time.Time, bool) {
	if o == nil || IsNil(o.CreatedAt) {
		return nil, false
	}
	return o.CreatedAt, true
}

// HasCreatedAt returns a boolean if a field has been set.
func (o *Interaction) HasCreatedAt() bool {
	if o != nil && !IsNil(o.CreatedAt) {
		return true
	}

	return false
}

// SetCreatedAt gets a reference to the given time.Time and assigns it to the CreatedAt field.
func (o *Interaction) SetCreatedAt(v time.Time) {
	o.CreatedAt = &v
}

// GetUpdatedAt returns the UpdatedAt field value if set, zero value otherwise.
func (o *Interaction) GetUpdatedAt() time.Time {
	if o == nil || IsNil(o.UpdatedAt) {
		var ret time.Time
		return ret
	}
	return *o.UpdatedAt
}

// GetUpdatedAtOk returns a tuple with the UpdatedAt field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *Interaction) GetUpdatedAtOk() (*time.Time, bool) {
	if o == nil || IsNil(o.UpdatedAt) {
		return nil, false
	}
	return o.UpdatedAt, true
}

// HasUpdatedAt returns a boolean if a field has been set.
func (o *Interaction) HasUpdatedAt() bool {
	if o != nil && !IsNil(o.UpdatedAt) {
		return true
	}

	return false
}

// SetUpdatedAt gets a reference to the given time.Time and assigns it to the UpdatedAt field.
func (o *Interaction) SetUpdatedAt(v time.Time) {
	o.UpdatedAt = &v
}

func (o Interaction) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o Interaction) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Id) {
		toSerialize["id"] = o.Id
	}
	if o.CompanyId.IsSet() {
		toSerialize["company_id"] = o.CompanyId.Get()
	}
	if o.ContactId.IsSet() {
		toSerialize["contact_id"] = o.ContactId.Get()
	}
	if !IsNil(o.InteractionType) {
		toSerialize["interaction_type"] = o.InteractionType
	}
	if !IsNil(o.Notes) {
		toSerialize["notes"] = o.Notes
	}
	if !IsNil(o.InteractionDatetime) {
		toSerialize["interaction_datetime"] = o.InteractionDatetime
	}
	if !IsNil(o.CreatedBy) {
		toSerialize["created_by"] = o.CreatedBy
	}
	if !IsNil(o.CreatedAt) {
		toSerialize["created_at"] = o.CreatedAt
	}
	if !IsNil(o.UpdatedAt) {
		toSerialize["updated_at"] = o.UpdatedAt
	}
	return toSerialize, nil
}

type NullableInteraction struct {
	value *Interaction
	isSet bool
}

func (v NullableInteraction) Get() *Interaction {
	return v.value
}

func (v *NullableInteraction) Set(val *Interaction) {
	v.value = val
	v.isSet = true
}

func (v NullableInteraction) IsSet() bool {
	return v.isSet
}

func (v *NullableInteraction) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableInteraction(val *Interaction) *NullableInteraction {
	return &NullableInteraction{value: val, isSet: true}
}

func (v NullableInteraction) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableInteraction) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


