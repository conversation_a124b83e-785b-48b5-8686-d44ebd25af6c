version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: unified-project-hub-db
    environment:
      POSTGRES_DB: unified_project_hub
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/migrations/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: unified-project-hub-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API Server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: unified-project-hub-backend
    environment:
      NODE_ENV: development
      PORT: 8000
      DATABASE_URL: ********************************************/unified_project_hub
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-jwt-secret-key
      JWT_EXPIRES_IN: 7d
      CORS_ORIGIN: http://localhost:3000
      FILE_UPLOAD_PATH: /app/uploads
      MAX_FILE_SIZE: 10485760
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - backend_uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - app-network
    command: npm run dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend React Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: unified-project-hub-frontend
    environment:
      NODE_ENV: development
      VITE_API_BASE_URL: http://localhost:8000/api
      VITE_WS_URL: ws://localhost:8000
      VITE_APP_NAME: Unified Project Hub
      VITE_APP_VERSION: 1.0.0
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - app-network
    command: npm run dev

  # MinIO for file storage (S3-compatible)
  minio:
    image: minio/minio:latest
    container_name: unified-project-hub-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - app-network
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy (for production)
  nginx:
    image: nginx:alpine
    container_name: unified-project-hub-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    networks:
      - app-network
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  backend_uploads:
    driver: local

networks:
  app-network:
    driver: bridge
