import { Router, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { async<PERSON><PERSON><PERSON> } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';

const router = Router();
const prisma = new PrismaClient();

// GET /api/projects - Get all projects
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  try {
    const projects = await prisma.project.findMany({
      include: {
        company: true,
        project_lead: true,
        customer_contact: true,
        tasks: {
          include: {
            assignee: true
          }
        }
      },
      orderBy: {
        created_at: 'desc'
      }
    });

    res.json({
      status: 'success',
      data: {
        projects,
        pagination: {
          page: 1,
          limit: 20,
          total: projects.length,
          pages: 1
        },
        filters: {},
        sorting: {},
        grouping: {}
      }
    });
  } catch (error) {
    console.error('Error fetching projects:', error);
    res.status(500).json({ error: 'Failed to fetch projects' });
  }
}));

// GET /api/projects/:id - Get single project
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    
    const project = await prisma.project.findUnique({
      where: { id },
      include: {
        company: true,
        project_lead: true,
        customer_contact: true,
        tasks: {
          include: {
            assignee: true
          }
        }
      }
    });

    if (!project) {
      return res.status(404).json({ error: 'Project not found' });
    }

    res.json({
      status: 'success',
      data: project
    });
  } catch (error) {
    console.error('Error fetching project:', error);
    res.status(500).json({ error: 'Failed to fetch project' });
  }
}));

// POST /api/projects - Create new project
router.post('/', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  try {
    const {
      name,
      description,
      type,
      start_date,
      end_date,
      project_lead_id,
      company_id,
      customer_contact_id,
      tags
    } = req.body;

    const project = await prisma.project.create({
      data: {
        name,
        description,
        type,
        start_date: start_date ? new Date(start_date) : null,
        end_date: end_date ? new Date(end_date) : null,
        project_lead_id,
        company_id: company_id || null,
        customer_contact_id: customer_contact_id || null,
        tags: tags || [],
        status: 'draft',
        company_name: ''
      },
      include: {
        company: true,
        project_lead: true,
        customer_contact: true,
        tasks: {
          include: {
            assignee: true
          }
        }
      }
    });

    res.status(201).json({
      status: 'success',
      data: project
    });
  } catch (error) {
    console.error('Error creating project:', error);
    res.status(500).json({ error: 'Failed to create project' });
  }
}));

// PUT /api/projects/:id - Update project
router.put('/:id', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      type,
      start_date,
      end_date,
      project_lead_id,
      company_id,
      customer_contact_id,
      tags,
      status
    } = req.body;

    const project = await prisma.project.update({
      where: { id },
      data: {
        name,
        description,
        type,
        start_date: start_date ? new Date(start_date) : null,
        end_date: end_date ? new Date(end_date) : null,
        project_lead_id,
        company_id: company_id || null,
        customer_contact_id: customer_contact_id || null,
        tags: tags || [],
        status
      },
      include: {
        company: true,
        project_lead: true,
        customer_contact: true,
        tasks: {
          include: {
            assignee: true
          }
        }
      }
    });

    res.json({
      status: 'success',
      data: project
    });
  } catch (error) {
    console.error('Error updating project:', error);
    res.status(500).json({ error: 'Failed to update project' });
  }
}));

// DELETE /api/projects/:id - Delete project
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;

    await prisma.project.delete({
      where: { id }
    });

    res.json({
      status: 'success',
      message: 'Project deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting project:', error);
    res.status(500).json({ error: 'Failed to delete project' });
  }
}));

export default router;
