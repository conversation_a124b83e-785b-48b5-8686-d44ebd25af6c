# \ArliAPI

All URIs are relative to *https://api.crm.example.com/v1*

Method | HTTP request | Description
------------- | ------------- | -------------
[**ArliDocumentsGet**](ArliAPI.md#ArliDocumentsGet) | **Get** /arli/documents | List documents
[**ArliDocumentsIdDelete**](ArliAPI.md#ArliDocumentsIdDelete) | **Delete** /arli/documents/{id} | Delete document
[**ArliDocumentsIdGet**](ArliAPI.md#ArliDocumentsIdGet) | **Get** /arli/documents/{id} | Get document
[**ArliDocumentsIdPut**](ArliAPI.md#ArliDocumentsIdPut) | **Put** /arli/documents/{id} | Update document
[**ArliDocumentsPost**](ArliAPI.md#ArliDocumentsPost) | **Post** /arli/documents | Create document
[**ArliDocumentsVectorizePost**](ArliAPI.md#ArliDocumentsVectorizePost) | **Post** /arli/documents/vectorize | Vectorize document
[**ArliFilterTagsGet**](ArliAPI.md#ArliFilterTagsGet) | **Get** /arli/filter-tags | List filter tags
[**ArliFilterTagsPost**](ArliAPI.md#ArliFilterTagsPost) | **Post** /arli/filter-tags | Create filter tag



## ArliDocumentsGet

> ArliDocumentsGet200Response ArliDocumentsGet(ctx).FilterTags(filterTags).MetadataFilter(metadataFilter).Limit(limit).Offset(offset).Execute()

List documents



### Example

```go
package main

import (
	"context"
	"fmt"
	"os"
	openapiclient "github.com/GIT_USER_ID/GIT_REPO_ID"
)

func main() {
	filterTags := []string{"Inner_example"} // []string | Filter by tags (optional)
	metadataFilter := map[string]interface{}{ ... } // map[string]interface{} | Filter by metadata properties (optional)
	limit := int32(56) // int32 |  (optional) (default to 20)
	offset := int32(56) // int32 |  (optional) (default to 0)

	configuration := openapiclient.NewConfiguration()
	apiClient := openapiclient.NewAPIClient(configuration)
	resp, r, err := apiClient.ArliAPI.ArliDocumentsGet(context.Background()).FilterTags(filterTags).MetadataFilter(metadataFilter).Limit(limit).Offset(offset).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `ArliAPI.ArliDocumentsGet``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ArliDocumentsGet`: ArliDocumentsGet200Response
	fmt.Fprintf(os.Stdout, "Response from `ArliAPI.ArliDocumentsGet`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiArliDocumentsGetRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **filterTags** | **[]string** | Filter by tags | 
 **metadataFilter** | [**map[string]interface{}**](map[string]interface{}.md) | Filter by metadata properties | 
 **limit** | **int32** |  | [default to 20]
 **offset** | **int32** |  | [default to 0]

### Return type

[**ArliDocumentsGet200Response**](ArliDocumentsGet200Response.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## ArliDocumentsIdDelete

> ArliDocumentsIdDelete(ctx, id).Execute()

Delete document



### Example

```go
package main

import (
	"context"
	"fmt"
	"os"
	openapiclient "github.com/GIT_USER_ID/GIT_REPO_ID"
)

func main() {
	id := "38400000-8cf0-11bd-b23e-10b96e4ef00d" // string | 

	configuration := openapiclient.NewConfiguration()
	apiClient := openapiclient.NewAPIClient(configuration)
	r, err := apiClient.ArliAPI.ArliDocumentsIdDelete(context.Background(), id).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `ArliAPI.ArliDocumentsIdDelete``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
}
```

### Path Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
**ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
**id** | **string** |  | 

### Other Parameters

Other parameters are passed through a pointer to a apiArliDocumentsIdDeleteRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


### Return type

 (empty response body)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## ArliDocumentsIdGet

> DocumentDetails ArliDocumentsIdGet(ctx, id).Execute()

Get document



### Example

```go
package main

import (
	"context"
	"fmt"
	"os"
	openapiclient "github.com/GIT_USER_ID/GIT_REPO_ID"
)

func main() {
	id := "38400000-8cf0-11bd-b23e-10b96e4ef00d" // string | 

	configuration := openapiclient.NewConfiguration()
	apiClient := openapiclient.NewAPIClient(configuration)
	resp, r, err := apiClient.ArliAPI.ArliDocumentsIdGet(context.Background(), id).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `ArliAPI.ArliDocumentsIdGet``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ArliDocumentsIdGet`: DocumentDetails
	fmt.Fprintf(os.Stdout, "Response from `ArliAPI.ArliDocumentsIdGet`: %v\n", resp)
}
```

### Path Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
**ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
**id** | **string** |  | 

### Other Parameters

Other parameters are passed through a pointer to a apiArliDocumentsIdGetRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------


### Return type

[**DocumentDetails**](DocumentDetails.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## ArliDocumentsIdPut

> Document ArliDocumentsIdPut(ctx, id).DocumentUpdate(documentUpdate).Execute()

Update document



### Example

```go
package main

import (
	"context"
	"fmt"
	"os"
	openapiclient "github.com/GIT_USER_ID/GIT_REPO_ID"
)

func main() {
	id := "38400000-8cf0-11bd-b23e-10b96e4ef00d" // string | 
	documentUpdate := *openapiclient.NewDocumentUpdate() // DocumentUpdate | 

	configuration := openapiclient.NewConfiguration()
	apiClient := openapiclient.NewAPIClient(configuration)
	resp, r, err := apiClient.ArliAPI.ArliDocumentsIdPut(context.Background(), id).DocumentUpdate(documentUpdate).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `ArliAPI.ArliDocumentsIdPut``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ArliDocumentsIdPut`: Document
	fmt.Fprintf(os.Stdout, "Response from `ArliAPI.ArliDocumentsIdPut`: %v\n", resp)
}
```

### Path Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
**ctx** | **context.Context** | context for authentication, logging, cancellation, deadlines, tracing, etc.
**id** | **string** |  | 

### Other Parameters

Other parameters are passed through a pointer to a apiArliDocumentsIdPutRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------

 **documentUpdate** | [**DocumentUpdate**](DocumentUpdate.md) |  | 

### Return type

[**Document**](Document.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## ArliDocumentsPost

> Document ArliDocumentsPost(ctx).DocumentCreate(documentCreate).Execute()

Create document



### Example

```go
package main

import (
	"context"
	"fmt"
	"os"
	openapiclient "github.com/GIT_USER_ID/GIT_REPO_ID"
)

func main() {
	documentCreate := *openapiclient.NewDocumentCreate("Content_example") // DocumentCreate | 

	configuration := openapiclient.NewConfiguration()
	apiClient := openapiclient.NewAPIClient(configuration)
	resp, r, err := apiClient.ArliAPI.ArliDocumentsPost(context.Background()).DocumentCreate(documentCreate).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `ArliAPI.ArliDocumentsPost``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ArliDocumentsPost`: Document
	fmt.Fprintf(os.Stdout, "Response from `ArliAPI.ArliDocumentsPost`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiArliDocumentsPostRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **documentCreate** | [**DocumentCreate**](DocumentCreate.md) |  | 

### Return type

[**Document**](Document.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## ArliDocumentsVectorizePost

> ArliDocumentsVectorizePost200Response ArliDocumentsVectorizePost(ctx).ArliDocumentsVectorizePostRequest(arliDocumentsVectorizePostRequest).Execute()

Vectorize document



### Example

```go
package main

import (
	"context"
	"fmt"
	"os"
	openapiclient "github.com/GIT_USER_ID/GIT_REPO_ID"
)

func main() {
	arliDocumentsVectorizePostRequest := *openapiclient.NewArliDocumentsVectorizePostRequest("DocumentId_example", "Content_example") // ArliDocumentsVectorizePostRequest | 

	configuration := openapiclient.NewConfiguration()
	apiClient := openapiclient.NewAPIClient(configuration)
	resp, r, err := apiClient.ArliAPI.ArliDocumentsVectorizePost(context.Background()).ArliDocumentsVectorizePostRequest(arliDocumentsVectorizePostRequest).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `ArliAPI.ArliDocumentsVectorizePost``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ArliDocumentsVectorizePost`: ArliDocumentsVectorizePost200Response
	fmt.Fprintf(os.Stdout, "Response from `ArliAPI.ArliDocumentsVectorizePost`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiArliDocumentsVectorizePostRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **arliDocumentsVectorizePostRequest** | [**ArliDocumentsVectorizePostRequest**](ArliDocumentsVectorizePostRequest.md) |  | 

### Return type

[**ArliDocumentsVectorizePost200Response**](ArliDocumentsVectorizePost200Response.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## ArliFilterTagsGet

> []FilterTag ArliFilterTagsGet(ctx).Execute()

List filter tags



### Example

```go
package main

import (
	"context"
	"fmt"
	"os"
	openapiclient "github.com/GIT_USER_ID/GIT_REPO_ID"
)

func main() {

	configuration := openapiclient.NewConfiguration()
	apiClient := openapiclient.NewAPIClient(configuration)
	resp, r, err := apiClient.ArliAPI.ArliFilterTagsGet(context.Background()).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `ArliAPI.ArliFilterTagsGet``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ArliFilterTagsGet`: []FilterTag
	fmt.Fprintf(os.Stdout, "Response from `ArliAPI.ArliFilterTagsGet`: %v\n", resp)
}
```

### Path Parameters

This endpoint does not need any parameter.

### Other Parameters

Other parameters are passed through a pointer to a apiArliFilterTagsGetRequest struct via the builder pattern


### Return type

[**[]FilterTag**](FilterTag.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)


## ArliFilterTagsPost

> FilterTag ArliFilterTagsPost(ctx).FilterTagCreate(filterTagCreate).Execute()

Create filter tag



### Example

```go
package main

import (
	"context"
	"fmt"
	"os"
	openapiclient "github.com/GIT_USER_ID/GIT_REPO_ID"
)

func main() {
	filterTagCreate := *openapiclient.NewFilterTagCreate("Name_example") // FilterTagCreate | 

	configuration := openapiclient.NewConfiguration()
	apiClient := openapiclient.NewAPIClient(configuration)
	resp, r, err := apiClient.ArliAPI.ArliFilterTagsPost(context.Background()).FilterTagCreate(filterTagCreate).Execute()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error when calling `ArliAPI.ArliFilterTagsPost``: %v\n", err)
		fmt.Fprintf(os.Stderr, "Full HTTP response: %v\n", r)
	}
	// response from `ArliFilterTagsPost`: FilterTag
	fmt.Fprintf(os.Stdout, "Response from `ArliAPI.ArliFilterTagsPost`: %v\n", resp)
}
```

### Path Parameters



### Other Parameters

Other parameters are passed through a pointer to a apiArliFilterTagsPostRequest struct via the builder pattern


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **filterTagCreate** | [**FilterTagCreate**](FilterTagCreate.md) |  | 

### Return type

[**FilterTag**](FilterTag.md)

### Authorization

[bearerAuth](../README.md#bearerAuth)

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints)
[[Back to Model list]](../README.md#documentation-for-models)
[[Back to README]](../README.md)

