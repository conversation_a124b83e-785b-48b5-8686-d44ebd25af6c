// Unified Data Model for Merged Project Management System
// Combines features from core-project-pulse, project-brief-buddy, and mono CRM

// ============================================================================
// ENUMS AND TYPES
// ============================================================================

export type ProjectType = 'internal' | 'external' | 'hybrid';
export type ProjectStatus = 'draft' | 'requirements' | 'backlog' | 'not-started' | 'in-progress' | 'completed' | 'archived' | 'on-hold';
export type TaskStatus = 'to-do' | 'in-progress' | 'done' | 'blocked';
export type PriorityLevel = 'P0' | 'P1' | 'P2' | 'P3' | 'P4';
export type EffortEstimate = 'XS' | 'S' | 'M' | 'L' | 'XL' | 'XXL';
export type ImpactType = 'Revenue' | 'Platform' | 'Bug Fix' | 'R&D' | 'Customer Success';
export type UserRole = 'admin' | 'project_manager' | 'team_member' | 'customer' | 'requirements_analyst';
export type CompanyStatus = 'prospect' | 'active' | 'inactive' | 'archived';
export type ContactRole = 'primary' | 'technical' | 'business' | 'stakeholder';
export type InteractionType = 'email' | 'call' | 'meeting' | 'note' | 'task_update' | 'milestone';
export type RequirementStatus = 'draft' | 'review' | 'approved' | 'implemented' | 'tested';

// ============================================================================
// CORE ENTITIES
// ============================================================================

export interface UnifiedProject {
  id: string;
  name: string;
  description?: string;
  type: ProjectType;
  status: ProjectStatus;
  
  // Company/Customer Information
  company_id?: string;
  company_name: string;
  customer_contact_id?: string;
  
  // Team Assignment
  project_lead_id: string;
  team_member_ids: string[];
  
  // Priority and Planning
  priority_level: PriorityLevel;
  effort_estimate: EffortEstimate;
  impact_type: ImpactType;
  
  // Dates
  start_date?: string;
  end_date?: string;
  original_end_date?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  archived_at?: string;
  
  // Requirements and Documentation
  prd_document_link?: string;
  requirements_status: RequirementStatus;
  requirements_completion_percentage: number;
  
  // External Links
  poc_url?: string;
  repository_url?: string;
  design_url?: string;
  
  // Metrics
  progress_percentage: number;
  budget?: number;
  actual_cost?: number;
  
  // Visibility and Access
  is_customer_visible: boolean;
  customer_visible_fields: string[];
  
  // Metadata
  tags: string[];
  custom_fields?: Record<string, any>;
}

export interface Company {
  id: string;
  name: string;
  status: CompanyStatus;
  industry?: string;
  size?: string;
  website?: string;
  address?: string;
  phone?: string;
  email?: string;
  description?: string;
  research_notes?: string;
  created_at: string;
  updated_at: string;
  
  // CRM specific
  deal_value?: number;
  deal_stage?: string;
  last_interaction_date?: string;
  
  // Project relationship
  active_projects_count: number;
  total_projects_count: number;
}

export interface Contact {
  id: string;
  company_id: string;
  name: string;
  email: string;
  phone?: string;
  role: ContactRole;
  title?: string;
  department?: string;
  is_primary: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  
  // Project associations
  project_ids: string[];
  
  // Communication preferences
  preferred_contact_method?: 'email' | 'phone' | 'slack';
  timezone?: string;
}

export interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  title?: string;
  department?: string;
  avatar_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  
  // Capacity and availability
  weekly_capacity_hours?: number;
  current_utilization_percentage?: number;
  skills: string[];
  
  // Project associations
  assigned_project_ids: string[];
  leading_project_ids: string[];
}

export interface UnifiedTask {
  id: string;
  project_id: string;
  parent_task_id?: string; // For subtasks
  
  name: string;
  description?: string;
  status: TaskStatus;
  priority_level: PriorityLevel;
  effort_estimate: EffortEstimate;
  
  // Assignment
  assignee_id: string;
  reviewer_id?: string;
  
  // Dates
  due_date?: string;
  start_date?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  
  // Requirements linkage
  requirement_ids: string[];
  
  // Customer visibility
  is_customer_visible: boolean;
  customer_description?: string; // Simplified description for customers
  
  // Dependencies
  depends_on_task_ids: string[];
  blocks_task_ids: string[];
  
  // Metadata
  tags: string[];
  estimated_hours?: number;
  actual_hours?: number;
  
  // Attachments and links
  attachment_urls: string[];
  external_links: string[];
}

export interface Requirement {
  id: string;
  project_id: string;
  
  title: string;
  description: string;
  type: 'functional' | 'non-functional' | 'business' | 'technical';
  status: RequirementStatus;
  priority_level: PriorityLevel;
  
  // Documentation
  detailed_specification?: string; // Markdown content
  acceptance_criteria: string[];
  user_stories: string[];
  
  // Approval workflow
  created_by_id: string;
  assigned_to_id?: string;
  approved_by_id?: string;
  
  // Dates
  created_at: string;
  updated_at: string;
  approved_at?: string;
  
  // Implementation tracking
  related_task_ids: string[];
  implementation_notes?: string;
  
  // Attachments
  mockup_urls: string[];
  diagram_urls: string[];
  reference_urls: string[];
}

export interface Interaction {
  id: string;
  project_id?: string;
  company_id?: string;
  contact_id?: string;
  
  type: InteractionType;
  subject: string;
  content: string;
  
  // Participants
  created_by_id: string;
  participant_ids: string[];
  
  // Dates
  interaction_date: string;
  created_at: string;
  updated_at: string;
  
  // Metadata
  is_customer_visible: boolean;
  tags: string[];
  
  // Attachments
  attachment_urls: string[];
  recording_url?: string;
  transcript_url?: string;
}

// ============================================================================
// COMPOSITE TYPES FOR UI
// ============================================================================

export interface ProjectWithDetails extends UnifiedProject {
  company?: Company;
  customer_contact?: Contact;
  project_lead?: TeamMember;
  team_members?: TeamMember[];
  tasks?: TaskWithSubTasks[];
  requirements?: Requirement[];
  recent_interactions?: Interaction[];
  
  // Computed metrics
  tasks_completed: number;
  tasks_total: number;
  requirements_approved: number;
  requirements_total: number;
  days_remaining?: number;
  is_overdue: boolean;
}

export interface TaskWithSubTasks extends UnifiedTask {
  assignee?: TeamMember;
  reviewer?: TeamMember;
  sub_tasks?: UnifiedTask[];
  requirements?: Requirement[];
  parent_task?: UnifiedTask;
}

export interface CompanyWithProjects extends Company {
  contacts?: Contact[];
  projects?: UnifiedProject[];
  recent_interactions?: Interaction[];
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  has_more: boolean;
}

// ============================================================================
// FORM AND INPUT TYPES
// ============================================================================

export interface ProjectCreateInput {
  name: string;
  description?: string;
  type: ProjectType;
  company_id?: string;
  company_name: string;
  project_lead_id: string;
  priority_level: PriorityLevel;
  effort_estimate: EffortEstimate;
  impact_type: ImpactType;
  start_date?: string;
  end_date?: string;
  is_customer_visible: boolean;
  tags?: string[];
}

export interface ProjectUpdateInput extends Partial<ProjectCreateInput> {
  id: string;
  status?: ProjectStatus;
}

export interface TaskCreateInput {
  project_id: string;
  parent_task_id?: string;
  name: string;
  description?: string;
  assignee_id: string;
  priority_level: PriorityLevel;
  effort_estimate: EffortEstimate;
  due_date?: string;
  is_customer_visible: boolean;
  requirement_ids?: string[];
}

export interface TaskUpdateInput extends Partial<TaskCreateInput> {
  id: string;
  status?: TaskStatus;
}
