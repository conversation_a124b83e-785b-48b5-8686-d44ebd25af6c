# DealWithDetails

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Id** | Pointer to **string** |  | [optional] 
**Title** | Pointer to **string** |  | [optional] 
**Description** | Pointer to **string** |  | [optional] 
**EstimatedValue** | Pointer to **float64** |  | [optional] 
**CompanyId** | Pointer to **string** |  | [optional] 
**DealStageId** | Pointer to **string** |  | [optional] 
**ExpectedCloseDate** | Pointer to **string** |  | [optional] 
**CreatedBy** | Pointer to **string** |  | [optional] 
**CreatedAt** | Pointer to **time.Time** |  | [optional] 
**UpdatedAt** | Pointer to **time.Time** |  | [optional] 
**Company** | Pointer to [**DealCompanyInfo**](DealCompanyInfo.md) |  | [optional] 
**DealStage** | Pointer to [**DealStageInfo**](DealStageInfo.md) |  | [optional] 

## Methods

### NewDealWithDetails

`func NewDealWithDetails() *DealWithDetails`

NewDealWithDetails instantiates a new DealWithDetails object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDealWithDetailsWithDefaults

`func NewDealWithDetailsWithDefaults() *DealWithDetails`

NewDealWithDetailsWithDefaults instantiates a new DealWithDetails object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetId

`func (o *DealWithDetails) GetId() string`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *DealWithDetails) GetIdOk() (*string, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *DealWithDetails) SetId(v string)`

SetId sets Id field to given value.

### HasId

`func (o *DealWithDetails) HasId() bool`

HasId returns a boolean if a field has been set.

### GetTitle

`func (o *DealWithDetails) GetTitle() string`

GetTitle returns the Title field if non-nil, zero value otherwise.

### GetTitleOk

`func (o *DealWithDetails) GetTitleOk() (*string, bool)`

GetTitleOk returns a tuple with the Title field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetTitle

`func (o *DealWithDetails) SetTitle(v string)`

SetTitle sets Title field to given value.

### HasTitle

`func (o *DealWithDetails) HasTitle() bool`

HasTitle returns a boolean if a field has been set.

### GetDescription

`func (o *DealWithDetails) GetDescription() string`

GetDescription returns the Description field if non-nil, zero value otherwise.

### GetDescriptionOk

`func (o *DealWithDetails) GetDescriptionOk() (*string, bool)`

GetDescriptionOk returns a tuple with the Description field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDescription

`func (o *DealWithDetails) SetDescription(v string)`

SetDescription sets Description field to given value.

### HasDescription

`func (o *DealWithDetails) HasDescription() bool`

HasDescription returns a boolean if a field has been set.

### GetEstimatedValue

`func (o *DealWithDetails) GetEstimatedValue() float64`

GetEstimatedValue returns the EstimatedValue field if non-nil, zero value otherwise.

### GetEstimatedValueOk

`func (o *DealWithDetails) GetEstimatedValueOk() (*float64, bool)`

GetEstimatedValueOk returns a tuple with the EstimatedValue field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetEstimatedValue

`func (o *DealWithDetails) SetEstimatedValue(v float64)`

SetEstimatedValue sets EstimatedValue field to given value.

### HasEstimatedValue

`func (o *DealWithDetails) HasEstimatedValue() bool`

HasEstimatedValue returns a boolean if a field has been set.

### GetCompanyId

`func (o *DealWithDetails) GetCompanyId() string`

GetCompanyId returns the CompanyId field if non-nil, zero value otherwise.

### GetCompanyIdOk

`func (o *DealWithDetails) GetCompanyIdOk() (*string, bool)`

GetCompanyIdOk returns a tuple with the CompanyId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCompanyId

`func (o *DealWithDetails) SetCompanyId(v string)`

SetCompanyId sets CompanyId field to given value.

### HasCompanyId

`func (o *DealWithDetails) HasCompanyId() bool`

HasCompanyId returns a boolean if a field has been set.

### GetDealStageId

`func (o *DealWithDetails) GetDealStageId() string`

GetDealStageId returns the DealStageId field if non-nil, zero value otherwise.

### GetDealStageIdOk

`func (o *DealWithDetails) GetDealStageIdOk() (*string, bool)`

GetDealStageIdOk returns a tuple with the DealStageId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDealStageId

`func (o *DealWithDetails) SetDealStageId(v string)`

SetDealStageId sets DealStageId field to given value.

### HasDealStageId

`func (o *DealWithDetails) HasDealStageId() bool`

HasDealStageId returns a boolean if a field has been set.

### GetExpectedCloseDate

`func (o *DealWithDetails) GetExpectedCloseDate() string`

GetExpectedCloseDate returns the ExpectedCloseDate field if non-nil, zero value otherwise.

### GetExpectedCloseDateOk

`func (o *DealWithDetails) GetExpectedCloseDateOk() (*string, bool)`

GetExpectedCloseDateOk returns a tuple with the ExpectedCloseDate field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetExpectedCloseDate

`func (o *DealWithDetails) SetExpectedCloseDate(v string)`

SetExpectedCloseDate sets ExpectedCloseDate field to given value.

### HasExpectedCloseDate

`func (o *DealWithDetails) HasExpectedCloseDate() bool`

HasExpectedCloseDate returns a boolean if a field has been set.

### GetCreatedBy

`func (o *DealWithDetails) GetCreatedBy() string`

GetCreatedBy returns the CreatedBy field if non-nil, zero value otherwise.

### GetCreatedByOk

`func (o *DealWithDetails) GetCreatedByOk() (*string, bool)`

GetCreatedByOk returns a tuple with the CreatedBy field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreatedBy

`func (o *DealWithDetails) SetCreatedBy(v string)`

SetCreatedBy sets CreatedBy field to given value.

### HasCreatedBy

`func (o *DealWithDetails) HasCreatedBy() bool`

HasCreatedBy returns a boolean if a field has been set.

### GetCreatedAt

`func (o *DealWithDetails) GetCreatedAt() time.Time`

GetCreatedAt returns the CreatedAt field if non-nil, zero value otherwise.

### GetCreatedAtOk

`func (o *DealWithDetails) GetCreatedAtOk() (*time.Time, bool)`

GetCreatedAtOk returns a tuple with the CreatedAt field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCreatedAt

`func (o *DealWithDetails) SetCreatedAt(v time.Time)`

SetCreatedAt sets CreatedAt field to given value.

### HasCreatedAt

`func (o *DealWithDetails) HasCreatedAt() bool`

HasCreatedAt returns a boolean if a field has been set.

### GetUpdatedAt

`func (o *DealWithDetails) GetUpdatedAt() time.Time`

GetUpdatedAt returns the UpdatedAt field if non-nil, zero value otherwise.

### GetUpdatedAtOk

`func (o *DealWithDetails) GetUpdatedAtOk() (*time.Time, bool)`

GetUpdatedAtOk returns a tuple with the UpdatedAt field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetUpdatedAt

`func (o *DealWithDetails) SetUpdatedAt(v time.Time)`

SetUpdatedAt sets UpdatedAt field to given value.

### HasUpdatedAt

`func (o *DealWithDetails) HasUpdatedAt() bool`

HasUpdatedAt returns a boolean if a field has been set.

### GetCompany

`func (o *DealWithDetails) GetCompany() DealCompanyInfo`

GetCompany returns the Company field if non-nil, zero value otherwise.

### GetCompanyOk

`func (o *DealWithDetails) GetCompanyOk() (*DealCompanyInfo, bool)`

GetCompanyOk returns a tuple with the Company field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetCompany

`func (o *DealWithDetails) SetCompany(v DealCompanyInfo)`

SetCompany sets Company field to given value.

### HasCompany

`func (o *DealWithDetails) HasCompany() bool`

HasCompany returns a boolean if a field has been set.

### GetDealStage

`func (o *DealWithDetails) GetDealStage() DealStageInfo`

GetDealStage returns the DealStage field if non-nil, zero value otherwise.

### GetDealStageOk

`func (o *DealWithDetails) GetDealStageOk() (*DealStageInfo, bool)`

GetDealStageOk returns a tuple with the DealStage field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDealStage

`func (o *DealWithDetails) SetDealStage(v DealStageInfo)`

SetDealStage sets DealStage field to given value.

### HasDealStage

`func (o *DealWithDetails) HasDealStage() bool`

HasDealStage returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


