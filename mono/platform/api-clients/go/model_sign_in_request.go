/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"bytes"
	"fmt"
)

// checks if the SignInRequest type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &SignInRequest{}

// SignInRequest struct for SignInRequest
type SignInRequest struct {
	Email string `json:"email"`
	Password string `json:"password"`
}

type _SignInRequest SignInRequest

// NewSignInRequest instantiates a new SignInRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewSignInRequest(email string, password string) *SignInRequest {
	this := SignInRequest{}
	this.Email = email
	this.Password = password
	return &this
}

// NewSignInRequestWithDefaults instantiates a new SignInRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewSignInRequestWithDefaults() *SignInRequest {
	this := SignInRequest{}
	return &this
}

// GetEmail returns the Email field value
func (o *SignInRequest) GetEmail() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Email
}

// GetEmailOk returns a tuple with the Email field value
// and a boolean to check if the value has been set.
func (o *SignInRequest) GetEmailOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Email, true
}

// SetEmail sets field value
func (o *SignInRequest) SetEmail(v string) {
	o.Email = v
}

// GetPassword returns the Password field value
func (o *SignInRequest) GetPassword() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Password
}

// GetPasswordOk returns a tuple with the Password field value
// and a boolean to check if the value has been set.
func (o *SignInRequest) GetPasswordOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Password, true
}

// SetPassword sets field value
func (o *SignInRequest) SetPassword(v string) {
	o.Password = v
}

func (o SignInRequest) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o SignInRequest) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["email"] = o.Email
	toSerialize["password"] = o.Password
	return toSerialize, nil
}

func (o *SignInRequest) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"email",
		"password",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varSignInRequest := _SignInRequest{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varSignInRequest)

	if err != nil {
		return err
	}

	*o = SignInRequest(varSignInRequest)

	return err
}

type NullableSignInRequest struct {
	value *SignInRequest
	isSet bool
}

func (v NullableSignInRequest) Get() *SignInRequest {
	return v.value
}

func (v *NullableSignInRequest) Set(val *SignInRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableSignInRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableSignInRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableSignInRequest(val *SignInRequest) *NullableSignInRequest {
	return &NullableSignInRequest{value: val, isSet: true}
}

func (v NullableSignInRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableSignInRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


