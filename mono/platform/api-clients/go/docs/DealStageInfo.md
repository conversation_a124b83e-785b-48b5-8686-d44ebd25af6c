# DealStageInfo

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**Id** | Pointer to **string** |  | [optional] 
**Name** | Pointer to **string** |  | [optional] 
**PipelineOrder** | Pointer to **int32** |  | [optional] 

## Methods

### NewDealStageInfo

`func NewDealStageInfo() *DealStageInfo`

NewDealStageInfo instantiates a new DealStageInfo object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewDealStageInfoWithDefaults

`func NewDealStageInfoWithDefaults() *DealStageInfo`

NewDealStageInfoWithDefaults instantiates a new DealStageInfo object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetId

`func (o *DealStageInfo) GetId() string`

GetId returns the Id field if non-nil, zero value otherwise.

### GetIdOk

`func (o *DealStageInfo) GetIdOk() (*string, bool)`

GetIdOk returns a tuple with the Id field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetId

`func (o *DealStageInfo) SetId(v string)`

SetId sets Id field to given value.

### HasId

`func (o *DealStageInfo) HasId() bool`

HasId returns a boolean if a field has been set.

### GetName

`func (o *DealStageInfo) GetName() string`

GetName returns the Name field if non-nil, zero value otherwise.

### GetNameOk

`func (o *DealStageInfo) GetNameOk() (*string, bool)`

GetNameOk returns a tuple with the Name field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetName

`func (o *DealStageInfo) SetName(v string)`

SetName sets Name field to given value.

### HasName

`func (o *DealStageInfo) HasName() bool`

HasName returns a boolean if a field has been set.

### GetPipelineOrder

`func (o *DealStageInfo) GetPipelineOrder() int32`

GetPipelineOrder returns the PipelineOrder field if non-nil, zero value otherwise.

### GetPipelineOrderOk

`func (o *DealStageInfo) GetPipelineOrderOk() (*int32, bool)`

GetPipelineOrderOk returns a tuple with the PipelineOrder field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetPipelineOrder

`func (o *DealStageInfo) SetPipelineOrder(v int32)`

SetPipelineOrder sets PipelineOrder field to given value.

### HasPipelineOrder

`func (o *DealStageInfo) HasPipelineOrder() bool`

HasPipelineOrder returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


