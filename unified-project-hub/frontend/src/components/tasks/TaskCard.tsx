import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Calendar, 
  User, 
  Al<PERSON><PERSON>riangle, 
  CheckCircle, 
  Clock, 
  Building, 
  MoreHorizontal,
  Edit,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Flag,
  Layers
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { formatDate } from '@/lib/utils';

interface TaskCardProps {
  task: {
    id: string;
    name: string;
    description?: string;
    status: string;
    priority_level: string;
    effort_estimate: string;
    due_date?: string;
    days_remaining?: number;
    is_overdue?: boolean;
    estimated_hours?: number;
    actual_hours?: number;
    assignee?: {
      id: string;
      name: string;
      avatar_url?: string;
    };
    reviewer?: {
      id: string;
      name: string;
      avatar_url?: string;
    };
    project?: {
      id: string;
      name: string;
      company_name: string;
    };
    parent_task?: {
      id: string;
      name: string;
      status: string;
    };
    sub_task_summary?: {
      total: number;
      completed: number;
      in_progress: number;
      to_do: number;
      progress_percentage: number;
    };
    tags?: string[];
    is_customer_visible?: boolean;
  };
  onEdit?: (task: any) => void;
  onDelete?: (task: any) => void;
  onAssign?: (task: any) => void;
  onUpdateStatus?: (task: any, status: string) => void;
  onUpdatePriority?: (task: any, priority: string) => void;
  showActions?: boolean;
  showProject?: boolean;
}

export function TaskCard({ 
  task, 
  onEdit, 
  onDelete, 
  onAssign,
  onUpdateStatus,
  onUpdatePriority,
  showActions = true,
  showProject = true
}: TaskCardProps) {
  const isOverdue = task.is_overdue && task.status !== 'done';
  
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'done':
        return 'default';
      case 'in_progress':
        return 'secondary';
      case 'in_review':
        return 'outline';
      case 'blocked':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case 'P0':
        return 'destructive';
      case 'P1':
        return 'destructive';
      case 'P2':
        return 'default';
      case 'P3':
        return 'secondary';
      case 'P4':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const getEffortColor = (effort: string) => {
    switch (effort) {
      case 'XS':
        return 'text-green-600';
      case 'S':
        return 'text-blue-600';
      case 'M':
        return 'text-yellow-600';
      case 'L':
        return 'text-orange-600';
      case 'XL':
        return 'text-red-600';
      case 'XXL':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatStatusLabel = (status: string) => {
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <Card className={`hover:shadow-md transition-shadow ${isOverdue ? 'border-red-200 bg-red-50/30' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg font-semibold truncate">
              <Link 
                to={`/tasks/${task.id}`}
                className="hover:text-primary transition-colors"
              >
                {task.name}
              </Link>
            </CardTitle>
            {task.description && (
              <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                {task.description}
              </p>
            )}
          </div>
          {showActions && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onEdit?.(task)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onAssign?.(task)}>
                  <UserCheck className="mr-2 h-4 w-4" />
                  Assign
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => onDelete?.(task)} className="text-destructive">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>

        {/* Status, Priority, and Effort Badges */}
        <div className="flex items-center gap-2 mt-2 flex-wrap">
          <Badge variant={getStatusBadgeVariant(task.status)}>
            {formatStatusLabel(task.status)}
          </Badge>
          <Badge variant={getPriorityBadgeVariant(task.priority_level)}>
            <Flag className="h-3 w-3 mr-1" />
            {task.priority_level}
          </Badge>
          <Badge variant="outline" className={getEffortColor(task.effort_estimate)}>
            {task.effort_estimate}
          </Badge>
          {isOverdue && (
            <Badge variant="destructive" className="flex items-center gap-1">
              <AlertTriangle className="h-3 w-3" />
              Overdue
            </Badge>
          )}
          {task.is_customer_visible && (
            <Badge variant="outline" className="text-xs">
              Customer Visible
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Parent Task */}
        {task.parent_task && (
          <div className="flex items-center gap-2 text-sm">
            <Layers className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">Subtask of:</span>
            <Link 
              to={`/tasks/${task.parent_task.id}`}
              className="text-primary hover:underline"
            >
              {task.parent_task.name}
            </Link>
          </div>
        )}

        {/* Sub-tasks Progress */}
        {task.sub_task_summary && task.sub_task_summary.total > 0 && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Sub-tasks Progress</span>
              <span className="font-medium">{task.sub_task_summary.progress_percentage}%</span>
            </div>
            <Progress value={task.sub_task_summary.progress_percentage} className="h-2" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{task.sub_task_summary.completed} of {task.sub_task_summary.total} completed</span>
              {task.sub_task_summary.in_progress > 0 && (
                <span>{task.sub_task_summary.in_progress} in progress</span>
              )}
            </div>
          </div>
        )}

        {/* Project and Due Date */}
        <div className="space-y-2 text-sm">
          {showProject && task.project && (
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-muted-foreground" />
              <Link 
                to={`/projects/${task.project.id}`}
                className="text-primary hover:underline"
              >
                {task.project.name}
              </Link>
              <span className="text-muted-foreground">({task.project.company_name})</span>
            </div>
          )}
          
          {task.due_date && (
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className={isOverdue ? 'text-red-600' : ''}>
                Due {formatDate(task.due_date)}
              </span>
            </div>
          )}

          {task.days_remaining !== null && task.days_remaining !== undefined && (
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className={isOverdue ? 'text-red-600' : ''}>
                {isOverdue 
                  ? `${Math.abs(task.days_remaining)} days overdue`
                  : `${task.days_remaining} days remaining`
                }
              </span>
            </div>
          )}
        </div>

        {/* Assignee and Reviewer */}
        <div className="space-y-2">
          {task.assignee && (
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <Avatar className="h-6 w-6">
                <AvatarImage src={task.assignee.avatar_url} />
                <AvatarFallback className="text-xs">
                  {task.assignee.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <span className="text-sm font-medium">{task.assignee.name}</span>
            </div>
          )}
          
          {task.reviewer && (
            <div className="flex items-center gap-2">
              <UserCheck className="h-4 w-4 text-muted-foreground" />
              <Avatar className="h-6 w-6">
                <AvatarImage src={task.reviewer.avatar_url} />
                <AvatarFallback className="text-xs">
                  {task.reviewer.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <span className="text-sm">Reviewer: {task.reviewer.name}</span>
            </div>
          )}
        </div>

        {/* Time Tracking */}
        {(task.estimated_hours || task.actual_hours) && (
          <div className="flex justify-between text-sm text-muted-foreground">
            {task.estimated_hours && (
              <span>Est: {task.estimated_hours}h</span>
            )}
            {task.actual_hours && (
              <span>Actual: {task.actual_hours}h</span>
            )}
          </div>
        )}

        {/* Tags */}
        {task.tags && task.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {task.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {task.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{task.tags.length - 3} more
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
