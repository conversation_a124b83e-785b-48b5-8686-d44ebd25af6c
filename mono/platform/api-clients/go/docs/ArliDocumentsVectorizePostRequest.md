# ArliDocumentsVectorizePostRequest

## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**DocumentId** | **string** |  | 
**Content** | **string** |  | 
**Metadata** | Pointer to **map[string]interface{}** |  | [optional] 

## Methods

### NewArliDocumentsVectorizePostRequest

`func NewArliDocumentsVectorizePostRequest(documentId string, content string, ) *ArliDocumentsVectorizePostRequest`

NewArliDocumentsVectorizePostRequest instantiates a new ArliDocumentsVectorizePostRequest object
This constructor will assign default values to properties that have it defined,
and makes sure properties required by API are set, but the set of arguments
will change when the set of required properties is changed

### NewArliDocumentsVectorizePostRequestWithDefaults

`func NewArliDocumentsVectorizePostRequestWithDefaults() *ArliDocumentsVectorizePostRequest`

NewArliDocumentsVectorizePostRequestWithDefaults instantiates a new ArliDocumentsVectorizePostRequest object
This constructor will only assign default values to properties that have it defined,
but it doesn't guarantee that properties required by API are set

### GetDocumentId

`func (o *ArliDocumentsVectorizePostRequest) GetDocumentId() string`

GetDocumentId returns the DocumentId field if non-nil, zero value otherwise.

### GetDocumentIdOk

`func (o *ArliDocumentsVectorizePostRequest) GetDocumentIdOk() (*string, bool)`

GetDocumentIdOk returns a tuple with the DocumentId field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetDocumentId

`func (o *ArliDocumentsVectorizePostRequest) SetDocumentId(v string)`

SetDocumentId sets DocumentId field to given value.


### GetContent

`func (o *ArliDocumentsVectorizePostRequest) GetContent() string`

GetContent returns the Content field if non-nil, zero value otherwise.

### GetContentOk

`func (o *ArliDocumentsVectorizePostRequest) GetContentOk() (*string, bool)`

GetContentOk returns a tuple with the Content field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetContent

`func (o *ArliDocumentsVectorizePostRequest) SetContent(v string)`

SetContent sets Content field to given value.


### GetMetadata

`func (o *ArliDocumentsVectorizePostRequest) GetMetadata() map[string]interface{}`

GetMetadata returns the Metadata field if non-nil, zero value otherwise.

### GetMetadataOk

`func (o *ArliDocumentsVectorizePostRequest) GetMetadataOk() (*map[string]interface{}, bool)`

GetMetadataOk returns a tuple with the Metadata field if it's non-nil, zero value otherwise
and a boolean to check if the value has been set.

### SetMetadata

`func (o *ArliDocumentsVectorizePostRequest) SetMetadata(v map[string]interface{})`

SetMetadata sets Metadata field to given value.

### HasMetadata

`func (o *ArliDocumentsVectorizePostRequest) HasMetadata() bool`

HasMetadata returns a boolean if a field has been set.


[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


