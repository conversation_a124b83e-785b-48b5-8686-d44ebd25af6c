import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { asyncHandler } from '../middleware/errorHandler';
import { AuthenticatedRequest, requireRole, canAccessCompany } from '../middleware/auth';

const router = Router();
const prisma = new PrismaClient();

// Apply company access middleware to all routes
router.use(canAccessCompany);

// Validation schemas
const createCompanySchema = z.object({
  name: z.string().min(1, 'Company name is required'),
  status: z.enum(['prospect', 'active', 'inactive', 'archived']).default('prospect'),
  industry: z.string().optional(),
  size: z.string().optional(),
  website: z.string().url().optional().or(z.literal('')),
  address: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email().optional().or(z.literal('')),
  description: z.string().optional(),
  research_notes: z.string().optional(),
  deal_value: z.number().positive().optional(),
  deal_stage: z.string().optional(),
});

/**
 * @route GET /api/companies
 * @desc Get all companies
 * @access Private (No customers)
 */
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { page = '1', limit = '10', status, search } = req.query;
  const skip = (parseInt(page as string) - 1) * parseInt(limit as string);

  let whereClause: any = {};

  // Apply filters
  if (status) {
    whereClause.status = status;
  }
  if (search) {
    whereClause.OR = [
      { name: { contains: search as string, mode: 'insensitive' } },
      { industry: { contains: search as string, mode: 'insensitive' } },
      { description: { contains: search as string, mode: 'insensitive' } },
    ];
  }

  const [companies, total] = await Promise.all([
    prisma.company.findMany({
      where: whereClause,
      include: {
        contacts: {
          where: { is_active: true },
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            title: true,
            is_primary: true,
          },
        },
        projects: {
          select: {
            id: true,
            name: true,
            status: true,
            progress_percentage: true,
          },
          orderBy: { updated_at: 'desc' },
          take: 5, // Latest 5 projects
        },
        _count: {
          select: {
            contacts: true,
            projects: true,
            interactions: true,
          },
        },
      },
      skip,
      take: parseInt(limit as string),
      orderBy: { updated_at: 'desc' },
    }),
    prisma.company.count({ where: whereClause }),
  ]);

  res.json({
    status: 'success',
    data: {
      companies,
      pagination: {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        total,
        pages: Math.ceil(total / parseInt(limit as string)),
      },
    },
  });
}));

/**
 * @route GET /api/companies/:id
 * @desc Get company by ID
 * @access Private (No customers)
 */
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  const company = await prisma.company.findUnique({
    where: { id },
    include: {
      contacts: {
        where: { is_active: true },
        orderBy: { is_primary: 'desc' },
      },
      projects: {
        include: {
          project_lead: {
            select: { id: true, name: true, email: true, avatar_url: true },
          },
          _count: {
            select: { tasks: true },
          },
        },
        orderBy: { updated_at: 'desc' },
      },
      interactions: {
        include: {
          created_by: {
            select: { id: true, name: true, email: true },
          },
          contact: {
            select: { id: true, name: true, email: true },
          },
        },
        orderBy: { interaction_date: 'desc' },
        take: 10, // Latest 10 interactions
      },
    },
  });

  if (!company) {
    return res.status(404).json({
      status: 'error',
      message: 'Company not found',
    });
  }

  res.json({
    status: 'success',
    data: { company },
  });
}));

/**
 * @route POST /api/companies
 * @desc Create new company
 * @access Private (Admin, Project Manager)
 */
router.post('/', requireRole('admin', 'project_manager'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const validatedData = createCompanySchema.parse(req.body);

  const company = await prisma.company.create({
    data: validatedData,
    include: {
      contacts: true,
      _count: {
        select: {
          contacts: true,
          projects: true,
          interactions: true,
        },
      },
    },
  });

  res.status(201).json({
    status: 'success',
    message: 'Company created successfully',
    data: { company },
  });
}));

/**
 * @route PUT /api/companies/:id
 * @desc Update company
 * @access Private (Admin, Project Manager)
 */
router.put('/:id', requireRole('admin', 'project_manager'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;
  const updateData = createCompanySchema.partial().parse(req.body);

  const company = await prisma.company.update({
    where: { id },
    data: updateData,
    include: {
      contacts: true,
      _count: {
        select: {
          contacts: true,
          projects: true,
          interactions: true,
        },
      },
    },
  });

  res.json({
    status: 'success',
    message: 'Company updated successfully',
    data: { company },
  });
}));

/**
 * @route DELETE /api/companies/:id
 * @desc Delete company
 * @access Private (Admin only)
 */
router.delete('/:id', requireRole('admin'), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { id } = req.params;

  // Check if company has active projects
  const activeProjects = await prisma.project.count({
    where: {
      company_id: id,
      status: { in: ['in_progress', 'not_started'] },
    },
  });

  if (activeProjects > 0) {
    return res.status(400).json({
      status: 'error',
      message: 'Cannot delete company with active projects',
    });
  }

  await prisma.company.delete({
    where: { id },
  });

  res.json({
    status: 'success',
    message: 'Company deleted successfully',
  });
}));

export default router;
