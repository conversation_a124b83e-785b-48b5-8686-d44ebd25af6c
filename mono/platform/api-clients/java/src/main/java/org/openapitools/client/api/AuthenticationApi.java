/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.api;

import org.openapitools.client.ApiCallback;
import org.openapitools.client.ApiClient;
import org.openapitools.client.ApiException;
import org.openapitools.client.ApiResponse;
import org.openapitools.client.Configuration;
import org.openapitools.client.Pair;
import org.openapitools.client.ProgressRequestBody;
import org.openapitools.client.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import org.openapitools.client.model.AuthResponse;
import org.openapitools.client.model.OAuthRedirectResponse;
import org.openapitools.client.model.OAuthSignInRequest;
import org.openapitools.client.model.Session;
import org.openapitools.client.model.SignInRequest;
import org.openapitools.client.model.User;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AuthenticationApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public AuthenticationApi() {
        this(Configuration.getDefaultApiClient());
    }

    public AuthenticationApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for authSessionGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Session information </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authenticated </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call authSessionGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/auth/session";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] {  };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call authSessionGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return authSessionGetCall(_callback);

    }

    /**
     * Get current session
     * Retrieve the current user session
     * @return Session
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Session information </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authenticated </td><td>  -  </td></tr>
     </table>
     */
    public Session authSessionGet() throws ApiException {
        ApiResponse<Session> localVarResp = authSessionGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Get current session
     * Retrieve the current user session
     * @return ApiResponse&lt;Session&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Session information </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authenticated </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Session> authSessionGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = authSessionGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<Session>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get current session (asynchronously)
     * Retrieve the current user session
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Session information </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authenticated </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call authSessionGetAsync(final ApiCallback<Session> _callback) throws ApiException {

        okhttp3.Call localVarCall = authSessionGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<Session>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for authSigninOauthPost
     * @param oauthSignInRequest  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OAuth redirect URL </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call authSigninOauthPostCall(OAuthSignInRequest oauthSignInRequest, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = oauthSignInRequest;

        // create path and map variables
        String localVarPath = "/auth/signin/oauth";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] {  };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call authSigninOauthPostValidateBeforeCall(OAuthSignInRequest oauthSignInRequest, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'oauthSignInRequest' is set
        if (oauthSignInRequest == null) {
            throw new ApiException("Missing the required parameter 'oauthSignInRequest' when calling authSigninOauthPost(Async)");
        }

        return authSigninOauthPostCall(oauthSignInRequest, _callback);

    }

    /**
     * Sign in with OAuth
     * Authenticate user with OAuth provider (Google)
     * @param oauthSignInRequest  (required)
     * @return OAuthRedirectResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OAuth redirect URL </td><td>  -  </td></tr>
     </table>
     */
    public OAuthRedirectResponse authSigninOauthPost(OAuthSignInRequest oauthSignInRequest) throws ApiException {
        ApiResponse<OAuthRedirectResponse> localVarResp = authSigninOauthPostWithHttpInfo(oauthSignInRequest);
        return localVarResp.getData();
    }

    /**
     * Sign in with OAuth
     * Authenticate user with OAuth provider (Google)
     * @param oauthSignInRequest  (required)
     * @return ApiResponse&lt;OAuthRedirectResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OAuth redirect URL </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<OAuthRedirectResponse> authSigninOauthPostWithHttpInfo(OAuthSignInRequest oauthSignInRequest) throws ApiException {
        okhttp3.Call localVarCall = authSigninOauthPostValidateBeforeCall(oauthSignInRequest, null);
        Type localVarReturnType = new TypeToken<OAuthRedirectResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Sign in with OAuth (asynchronously)
     * Authenticate user with OAuth provider (Google)
     * @param oauthSignInRequest  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OAuth redirect URL </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call authSigninOauthPostAsync(OAuthSignInRequest oauthSignInRequest, final ApiCallback<OAuthRedirectResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = authSigninOauthPostValidateBeforeCall(oauthSignInRequest, _callback);
        Type localVarReturnType = new TypeToken<OAuthRedirectResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for authSigninPost
     * @param signInRequest  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Authentication successful </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Invalid credentials </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call authSigninPostCall(SignInRequest signInRequest, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = signInRequest;

        // create path and map variables
        String localVarPath = "/auth/signin";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] {  };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call authSigninPostValidateBeforeCall(SignInRequest signInRequest, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'signInRequest' is set
        if (signInRequest == null) {
            throw new ApiException("Missing the required parameter 'signInRequest' when calling authSigninPost(Async)");
        }

        return authSigninPostCall(signInRequest, _callback);

    }

    /**
     * Sign in with email/password
     * Authenticate user with email and password
     * @param signInRequest  (required)
     * @return AuthResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Authentication successful </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Invalid credentials </td><td>  -  </td></tr>
     </table>
     */
    public AuthResponse authSigninPost(SignInRequest signInRequest) throws ApiException {
        ApiResponse<AuthResponse> localVarResp = authSigninPostWithHttpInfo(signInRequest);
        return localVarResp.getData();
    }

    /**
     * Sign in with email/password
     * Authenticate user with email and password
     * @param signInRequest  (required)
     * @return ApiResponse&lt;AuthResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Authentication successful </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Invalid credentials </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<AuthResponse> authSigninPostWithHttpInfo(SignInRequest signInRequest) throws ApiException {
        okhttp3.Call localVarCall = authSigninPostValidateBeforeCall(signInRequest, null);
        Type localVarReturnType = new TypeToken<AuthResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Sign in with email/password (asynchronously)
     * Authenticate user with email and password
     * @param signInRequest  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Authentication successful </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Invalid credentials </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call authSigninPostAsync(SignInRequest signInRequest, final ApiCallback<AuthResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = authSigninPostValidateBeforeCall(signInRequest, _callback);
        Type localVarReturnType = new TypeToken<AuthResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for authSignoutPost
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Successfully signed out </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authenticated </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call authSignoutPostCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/auth/signout";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call authSignoutPostValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return authSignoutPostCall(_callback);

    }

    /**
     * Sign out
     * End the current user session
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Successfully signed out </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authenticated </td><td>  -  </td></tr>
     </table>
     */
    public void authSignoutPost() throws ApiException {
        authSignoutPostWithHttpInfo();
    }

    /**
     * Sign out
     * End the current user session
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Successfully signed out </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authenticated </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> authSignoutPostWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = authSignoutPostValidateBeforeCall(null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Sign out (asynchronously)
     * End the current user session
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Successfully signed out </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authenticated </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call authSignoutPostAsync(final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = authSignoutPostValidateBeforeCall(_callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for authUserGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> User information </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authenticated </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call authUserGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/auth/user";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "bearerAuth" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call authUserGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return authUserGetCall(_callback);

    }

    /**
     * Get current user
     * Retrieve current authenticated user information
     * @return User
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> User information </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authenticated </td><td>  -  </td></tr>
     </table>
     */
    public User authUserGet() throws ApiException {
        ApiResponse<User> localVarResp = authUserGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Get current user
     * Retrieve current authenticated user information
     * @return ApiResponse&lt;User&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> User information </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authenticated </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<User> authUserGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = authUserGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<User>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get current user (asynchronously)
     * Retrieve current authenticated user information
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> User information </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authenticated </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call authUserGetAsync(final ApiCallback<User> _callback) throws ApiException {

        okhttp3.Call localVarCall = authUserGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<User>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
