/*
 * CRM API
 * Complete API specification for the CRM system
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.api;

import org.openapitools.client.ApiException;
import org.openapitools.client.model.Company;
import org.openapitools.client.model.CompanyCreate;
import org.openapitools.client.model.CompanyDetails;
import org.openapitools.client.model.CompanyStatus;
import org.openapitools.client.model.CompanyStatusUpdateRequest;
import org.openapitools.client.model.CompanyUpdate;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for CompaniesApi
 */
@Disabled
public class CompaniesApiTest {

    private final CompaniesApi api = new CompaniesApi();

    /**
     * List companies
     *
     * Retrieve a list of all active companies
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void companiesGetTest() throws ApiException {
        UUID statusId = null;
        Boolean includeDeleted = null;
        List<Company> response = api.companiesGet(statusId, includeDeleted);
        // TODO: test validations
    }

    /**
     * Delete company
     *
     * Soft delete a company (sets is_deleted to true)
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void companiesIdDeleteTest() throws ApiException {
        UUID id = null;
        api.companiesIdDelete(id);
        // TODO: test validations
    }

    /**
     * Get company
     *
     * Retrieve a specific company by ID
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void companiesIdGetTest() throws ApiException {
        UUID id = null;
        CompanyDetails response = api.companiesIdGet(id);
        // TODO: test validations
    }

    /**
     * Update company
     *
     * Update an existing company
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void companiesIdPutTest() throws ApiException {
        UUID id = null;
        CompanyUpdate companyUpdate = null;
        Company response = api.companiesIdPut(id, companyUpdate);
        // TODO: test validations
    }

    /**
     * Update company status
     *
     * Update the status of a company
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void companiesIdStatusPutTest() throws ApiException {
        UUID id = null;
        CompanyStatusUpdateRequest companyStatusUpdateRequest = null;
        api.companiesIdStatusPut(id, companyStatusUpdateRequest);
        // TODO: test validations
    }

    /**
     * Create company
     *
     * Create a new company
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void companiesPostTest() throws ApiException {
        CompanyCreate companyCreate = null;
        Company response = api.companiesPost(companyCreate);
        // TODO: test validations
    }

    /**
     * List company statuses
     *
     * Retrieve all company statuses ordered by pipeline order
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void companyStatusesGetTest() throws ApiException {
        List<CompanyStatus> response = api.companyStatusesGet();
        // TODO: test validations
    }

}
