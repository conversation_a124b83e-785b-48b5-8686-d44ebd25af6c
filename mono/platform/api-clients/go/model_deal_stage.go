/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"time"
)

// checks if the DealStage type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &DealStage{}

// DealStage struct for DealStage
type DealStage struct {
	Id *string `json:"id,omitempty"`
	Name *string `json:"name,omitempty"`
	PipelineOrder *int32 `json:"pipeline_order,omitempty"`
	IsClosedWon *bool `json:"is_closed_won,omitempty"`
	IsClosedLost *bool `json:"is_closed_lost,omitempty"`
	CreatedAt *time.Time `json:"created_at,omitempty"`
}

// NewDealStage instantiates a new DealStage object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDealStage() *DealStage {
	this := DealStage{}
	var isClosedWon bool = false
	this.IsClosedWon = &isClosedWon
	var isClosedLost bool = false
	this.IsClosedLost = &isClosedLost
	return &this
}

// NewDealStageWithDefaults instantiates a new DealStage object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDealStageWithDefaults() *DealStage {
	this := DealStage{}
	var isClosedWon bool = false
	this.IsClosedWon = &isClosedWon
	var isClosedLost bool = false
	this.IsClosedLost = &isClosedLost
	return &this
}

// GetId returns the Id field value if set, zero value otherwise.
func (o *DealStage) GetId() string {
	if o == nil || IsNil(o.Id) {
		var ret string
		return ret
	}
	return *o.Id
}

// GetIdOk returns a tuple with the Id field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealStage) GetIdOk() (*string, bool) {
	if o == nil || IsNil(o.Id) {
		return nil, false
	}
	return o.Id, true
}

// HasId returns a boolean if a field has been set.
func (o *DealStage) HasId() bool {
	if o != nil && !IsNil(o.Id) {
		return true
	}

	return false
}

// SetId gets a reference to the given string and assigns it to the Id field.
func (o *DealStage) SetId(v string) {
	o.Id = &v
}

// GetName returns the Name field value if set, zero value otherwise.
func (o *DealStage) GetName() string {
	if o == nil || IsNil(o.Name) {
		var ret string
		return ret
	}
	return *o.Name
}

// GetNameOk returns a tuple with the Name field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealStage) GetNameOk() (*string, bool) {
	if o == nil || IsNil(o.Name) {
		return nil, false
	}
	return o.Name, true
}

// HasName returns a boolean if a field has been set.
func (o *DealStage) HasName() bool {
	if o != nil && !IsNil(o.Name) {
		return true
	}

	return false
}

// SetName gets a reference to the given string and assigns it to the Name field.
func (o *DealStage) SetName(v string) {
	o.Name = &v
}

// GetPipelineOrder returns the PipelineOrder field value if set, zero value otherwise.
func (o *DealStage) GetPipelineOrder() int32 {
	if o == nil || IsNil(o.PipelineOrder) {
		var ret int32
		return ret
	}
	return *o.PipelineOrder
}

// GetPipelineOrderOk returns a tuple with the PipelineOrder field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealStage) GetPipelineOrderOk() (*int32, bool) {
	if o == nil || IsNil(o.PipelineOrder) {
		return nil, false
	}
	return o.PipelineOrder, true
}

// HasPipelineOrder returns a boolean if a field has been set.
func (o *DealStage) HasPipelineOrder() bool {
	if o != nil && !IsNil(o.PipelineOrder) {
		return true
	}

	return false
}

// SetPipelineOrder gets a reference to the given int32 and assigns it to the PipelineOrder field.
func (o *DealStage) SetPipelineOrder(v int32) {
	o.PipelineOrder = &v
}

// GetIsClosedWon returns the IsClosedWon field value if set, zero value otherwise.
func (o *DealStage) GetIsClosedWon() bool {
	if o == nil || IsNil(o.IsClosedWon) {
		var ret bool
		return ret
	}
	return *o.IsClosedWon
}

// GetIsClosedWonOk returns a tuple with the IsClosedWon field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealStage) GetIsClosedWonOk() (*bool, bool) {
	if o == nil || IsNil(o.IsClosedWon) {
		return nil, false
	}
	return o.IsClosedWon, true
}

// HasIsClosedWon returns a boolean if a field has been set.
func (o *DealStage) HasIsClosedWon() bool {
	if o != nil && !IsNil(o.IsClosedWon) {
		return true
	}

	return false
}

// SetIsClosedWon gets a reference to the given bool and assigns it to the IsClosedWon field.
func (o *DealStage) SetIsClosedWon(v bool) {
	o.IsClosedWon = &v
}

// GetIsClosedLost returns the IsClosedLost field value if set, zero value otherwise.
func (o *DealStage) GetIsClosedLost() bool {
	if o == nil || IsNil(o.IsClosedLost) {
		var ret bool
		return ret
	}
	return *o.IsClosedLost
}

// GetIsClosedLostOk returns a tuple with the IsClosedLost field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealStage) GetIsClosedLostOk() (*bool, bool) {
	if o == nil || IsNil(o.IsClosedLost) {
		return nil, false
	}
	return o.IsClosedLost, true
}

// HasIsClosedLost returns a boolean if a field has been set.
func (o *DealStage) HasIsClosedLost() bool {
	if o != nil && !IsNil(o.IsClosedLost) {
		return true
	}

	return false
}

// SetIsClosedLost gets a reference to the given bool and assigns it to the IsClosedLost field.
func (o *DealStage) SetIsClosedLost(v bool) {
	o.IsClosedLost = &v
}

// GetCreatedAt returns the CreatedAt field value if set, zero value otherwise.
func (o *DealStage) GetCreatedAt() time.Time {
	if o == nil || IsNil(o.CreatedAt) {
		var ret time.Time
		return ret
	}
	return *o.CreatedAt
}

// GetCreatedAtOk returns a tuple with the CreatedAt field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DealStage) GetCreatedAtOk() (*time.Time, bool) {
	if o == nil || IsNil(o.CreatedAt) {
		return nil, false
	}
	return o.CreatedAt, true
}

// HasCreatedAt returns a boolean if a field has been set.
func (o *DealStage) HasCreatedAt() bool {
	if o != nil && !IsNil(o.CreatedAt) {
		return true
	}

	return false
}

// SetCreatedAt gets a reference to the given time.Time and assigns it to the CreatedAt field.
func (o *DealStage) SetCreatedAt(v time.Time) {
	o.CreatedAt = &v
}

func (o DealStage) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o DealStage) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Id) {
		toSerialize["id"] = o.Id
	}
	if !IsNil(o.Name) {
		toSerialize["name"] = o.Name
	}
	if !IsNil(o.PipelineOrder) {
		toSerialize["pipeline_order"] = o.PipelineOrder
	}
	if !IsNil(o.IsClosedWon) {
		toSerialize["is_closed_won"] = o.IsClosedWon
	}
	if !IsNil(o.IsClosedLost) {
		toSerialize["is_closed_lost"] = o.IsClosedLost
	}
	if !IsNil(o.CreatedAt) {
		toSerialize["created_at"] = o.CreatedAt
	}
	return toSerialize, nil
}

type NullableDealStage struct {
	value *DealStage
	isSet bool
}

func (v NullableDealStage) Get() *DealStage {
	return v.value
}

func (v *NullableDealStage) Set(val *DealStage) {
	v.value = val
	v.isSet = true
}

func (v NullableDealStage) IsSet() bool {
	return v.isSet
}

func (v *NullableDealStage) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDealStage(val *DealStage) *NullableDealStage {
	return &NullableDealStage{value: val, isSet: true}
}

func (v NullableDealStage) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDealStage) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


