/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"bytes"
	"fmt"
)

// checks if the OAuthSignInRequest type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &OAuthSignInRequest{}

// OAuthSignInRequest struct for OAuthSignInRequest
type OAuthSignInRequest struct {
	Provider string `json:"provider"`
	RedirectTo *string `json:"redirectTo,omitempty"`
}

type _OAuthSignInRequest OAuthSignInRequest

// NewOAuthSignInRequest instantiates a new OAuthSignInRequest object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewOAuthSignInRequest(provider string) *OAuthSignInRequest {
	this := OAuthSignInRequest{}
	this.Provider = provider
	return &this
}

// NewOAuthSignInRequestWithDefaults instantiates a new OAuthSignInRequest object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewOAuthSignInRequestWithDefaults() *OAuthSignInRequest {
	this := OAuthSignInRequest{}
	return &this
}

// GetProvider returns the Provider field value
func (o *OAuthSignInRequest) GetProvider() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Provider
}

// GetProviderOk returns a tuple with the Provider field value
// and a boolean to check if the value has been set.
func (o *OAuthSignInRequest) GetProviderOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Provider, true
}

// SetProvider sets field value
func (o *OAuthSignInRequest) SetProvider(v string) {
	o.Provider = v
}

// GetRedirectTo returns the RedirectTo field value if set, zero value otherwise.
func (o *OAuthSignInRequest) GetRedirectTo() string {
	if o == nil || IsNil(o.RedirectTo) {
		var ret string
		return ret
	}
	return *o.RedirectTo
}

// GetRedirectToOk returns a tuple with the RedirectTo field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *OAuthSignInRequest) GetRedirectToOk() (*string, bool) {
	if o == nil || IsNil(o.RedirectTo) {
		return nil, false
	}
	return o.RedirectTo, true
}

// HasRedirectTo returns a boolean if a field has been set.
func (o *OAuthSignInRequest) HasRedirectTo() bool {
	if o != nil && !IsNil(o.RedirectTo) {
		return true
	}

	return false
}

// SetRedirectTo gets a reference to the given string and assigns it to the RedirectTo field.
func (o *OAuthSignInRequest) SetRedirectTo(v string) {
	o.RedirectTo = &v
}

func (o OAuthSignInRequest) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o OAuthSignInRequest) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["provider"] = o.Provider
	if !IsNil(o.RedirectTo) {
		toSerialize["redirectTo"] = o.RedirectTo
	}
	return toSerialize, nil
}

func (o *OAuthSignInRequest) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"provider",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varOAuthSignInRequest := _OAuthSignInRequest{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varOAuthSignInRequest)

	if err != nil {
		return err
	}

	*o = OAuthSignInRequest(varOAuthSignInRequest)

	return err
}

type NullableOAuthSignInRequest struct {
	value *OAuthSignInRequest
	isSet bool
}

func (v NullableOAuthSignInRequest) Get() *OAuthSignInRequest {
	return v.value
}

func (v *NullableOAuthSignInRequest) Set(val *OAuthSignInRequest) {
	v.value = val
	v.isSet = true
}

func (v NullableOAuthSignInRequest) IsSet() bool {
	return v.isSet
}

func (v *NullableOAuthSignInRequest) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableOAuthSignInRequest(val *OAuthSignInRequest) *NullableOAuthSignInRequest {
	return &NullableOAuthSignInRequest{value: val, isSet: true}
}

func (v NullableOAuthSignInRequest) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableOAuthSignInRequest) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


