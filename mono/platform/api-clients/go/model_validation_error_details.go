/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
)

// checks if the ValidationErrorDetails type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ValidationErrorDetails{}

// ValidationErrorDetails struct for ValidationErrorDetails
type ValidationErrorDetails struct {
	FieldErrors *map[string][]string `json:"field_errors,omitempty"`
}

// NewValidationErrorDetails instantiates a new ValidationErrorDetails object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewValidationErrorDetails() *ValidationErrorDetails {
	this := ValidationErrorDetails{}
	return &this
}

// NewValidationErrorDetailsWithDefaults instantiates a new ValidationErrorDetails object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewValidationErrorDetailsWithDefaults() *ValidationErrorDetails {
	this := ValidationErrorDetails{}
	return &this
}

// GetFieldErrors returns the FieldErrors field value if set, zero value otherwise.
func (o *ValidationErrorDetails) GetFieldErrors() map[string][]string {
	if o == nil || IsNil(o.FieldErrors) {
		var ret map[string][]string
		return ret
	}
	return *o.FieldErrors
}

// GetFieldErrorsOk returns a tuple with the FieldErrors field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *ValidationErrorDetails) GetFieldErrorsOk() (*map[string][]string, bool) {
	if o == nil || IsNil(o.FieldErrors) {
		return nil, false
	}
	return o.FieldErrors, true
}

// HasFieldErrors returns a boolean if a field has been set.
func (o *ValidationErrorDetails) HasFieldErrors() bool {
	if o != nil && !IsNil(o.FieldErrors) {
		return true
	}

	return false
}

// SetFieldErrors gets a reference to the given map[string][]string and assigns it to the FieldErrors field.
func (o *ValidationErrorDetails) SetFieldErrors(v map[string][]string) {
	o.FieldErrors = &v
}

func (o ValidationErrorDetails) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ValidationErrorDetails) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.FieldErrors) {
		toSerialize["field_errors"] = o.FieldErrors
	}
	return toSerialize, nil
}

type NullableValidationErrorDetails struct {
	value *ValidationErrorDetails
	isSet bool
}

func (v NullableValidationErrorDetails) Get() *ValidationErrorDetails {
	return v.value
}

func (v *NullableValidationErrorDetails) Set(val *ValidationErrorDetails) {
	v.value = val
	v.isSet = true
}

func (v NullableValidationErrorDetails) IsSet() bool {
	return v.isSet
}

func (v *NullableValidationErrorDetails) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableValidationErrorDetails(val *ValidationErrorDetails) *NullableValidationErrorDetails {
	return &NullableValidationErrorDetails{value: val, isSet: true}
}

func (v NullableValidationErrorDetails) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableValidationErrorDetails) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


