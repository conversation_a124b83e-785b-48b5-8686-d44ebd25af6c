/*
CRM API

Complete API specification for the CRM system

API version: 1.0.0
Contact: <EMAIL>
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package crmclient

import (
	"encoding/json"
	"bytes"
	"fmt"
)

// checks if the CompanyCreate type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &CompanyCreate{}

// CompanyCreate struct for CompanyCreate
type CompanyCreate struct {
	Name string `json:"name"`
	Website *string `json:"website,omitempty"`
	Phone *string `json:"phone,omitempty"`
	Address *string `json:"address,omitempty"`
	Notes *string `json:"notes,omitempty"`
	CompanyStatusId *string `json:"company_status_id,omitempty"`
}

type _CompanyCreate CompanyCreate

// NewCompanyCreate instantiates a new CompanyCreate object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewCompanyCreate(name string) *CompanyCreate {
	this := CompanyCreate{}
	this.Name = name
	return &this
}

// NewCompanyCreateWithDefaults instantiates a new CompanyCreate object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewCompanyCreateWithDefaults() *CompanyCreate {
	this := CompanyCreate{}
	return &this
}

// GetName returns the Name field value
func (o *CompanyCreate) GetName() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Name
}

// GetNameOk returns a tuple with the Name field value
// and a boolean to check if the value has been set.
func (o *CompanyCreate) GetNameOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Name, true
}

// SetName sets field value
func (o *CompanyCreate) SetName(v string) {
	o.Name = v
}

// GetWebsite returns the Website field value if set, zero value otherwise.
func (o *CompanyCreate) GetWebsite() string {
	if o == nil || IsNil(o.Website) {
		var ret string
		return ret
	}
	return *o.Website
}

// GetWebsiteOk returns a tuple with the Website field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyCreate) GetWebsiteOk() (*string, bool) {
	if o == nil || IsNil(o.Website) {
		return nil, false
	}
	return o.Website, true
}

// HasWebsite returns a boolean if a field has been set.
func (o *CompanyCreate) HasWebsite() bool {
	if o != nil && !IsNil(o.Website) {
		return true
	}

	return false
}

// SetWebsite gets a reference to the given string and assigns it to the Website field.
func (o *CompanyCreate) SetWebsite(v string) {
	o.Website = &v
}

// GetPhone returns the Phone field value if set, zero value otherwise.
func (o *CompanyCreate) GetPhone() string {
	if o == nil || IsNil(o.Phone) {
		var ret string
		return ret
	}
	return *o.Phone
}

// GetPhoneOk returns a tuple with the Phone field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyCreate) GetPhoneOk() (*string, bool) {
	if o == nil || IsNil(o.Phone) {
		return nil, false
	}
	return o.Phone, true
}

// HasPhone returns a boolean if a field has been set.
func (o *CompanyCreate) HasPhone() bool {
	if o != nil && !IsNil(o.Phone) {
		return true
	}

	return false
}

// SetPhone gets a reference to the given string and assigns it to the Phone field.
func (o *CompanyCreate) SetPhone(v string) {
	o.Phone = &v
}

// GetAddress returns the Address field value if set, zero value otherwise.
func (o *CompanyCreate) GetAddress() string {
	if o == nil || IsNil(o.Address) {
		var ret string
		return ret
	}
	return *o.Address
}

// GetAddressOk returns a tuple with the Address field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyCreate) GetAddressOk() (*string, bool) {
	if o == nil || IsNil(o.Address) {
		return nil, false
	}
	return o.Address, true
}

// HasAddress returns a boolean if a field has been set.
func (o *CompanyCreate) HasAddress() bool {
	if o != nil && !IsNil(o.Address) {
		return true
	}

	return false
}

// SetAddress gets a reference to the given string and assigns it to the Address field.
func (o *CompanyCreate) SetAddress(v string) {
	o.Address = &v
}

// GetNotes returns the Notes field value if set, zero value otherwise.
func (o *CompanyCreate) GetNotes() string {
	if o == nil || IsNil(o.Notes) {
		var ret string
		return ret
	}
	return *o.Notes
}

// GetNotesOk returns a tuple with the Notes field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyCreate) GetNotesOk() (*string, bool) {
	if o == nil || IsNil(o.Notes) {
		return nil, false
	}
	return o.Notes, true
}

// HasNotes returns a boolean if a field has been set.
func (o *CompanyCreate) HasNotes() bool {
	if o != nil && !IsNil(o.Notes) {
		return true
	}

	return false
}

// SetNotes gets a reference to the given string and assigns it to the Notes field.
func (o *CompanyCreate) SetNotes(v string) {
	o.Notes = &v
}

// GetCompanyStatusId returns the CompanyStatusId field value if set, zero value otherwise.
func (o *CompanyCreate) GetCompanyStatusId() string {
	if o == nil || IsNil(o.CompanyStatusId) {
		var ret string
		return ret
	}
	return *o.CompanyStatusId
}

// GetCompanyStatusIdOk returns a tuple with the CompanyStatusId field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *CompanyCreate) GetCompanyStatusIdOk() (*string, bool) {
	if o == nil || IsNil(o.CompanyStatusId) {
		return nil, false
	}
	return o.CompanyStatusId, true
}

// HasCompanyStatusId returns a boolean if a field has been set.
func (o *CompanyCreate) HasCompanyStatusId() bool {
	if o != nil && !IsNil(o.CompanyStatusId) {
		return true
	}

	return false
}

// SetCompanyStatusId gets a reference to the given string and assigns it to the CompanyStatusId field.
func (o *CompanyCreate) SetCompanyStatusId(v string) {
	o.CompanyStatusId = &v
}

func (o CompanyCreate) MarshalJSON() ([]byte, error) {
	toSerialize,err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o CompanyCreate) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["name"] = o.Name
	if !IsNil(o.Website) {
		toSerialize["website"] = o.Website
	}
	if !IsNil(o.Phone) {
		toSerialize["phone"] = o.Phone
	}
	if !IsNil(o.Address) {
		toSerialize["address"] = o.Address
	}
	if !IsNil(o.Notes) {
		toSerialize["notes"] = o.Notes
	}
	if !IsNil(o.CompanyStatusId) {
		toSerialize["company_status_id"] = o.CompanyStatusId
	}
	return toSerialize, nil
}

func (o *CompanyCreate) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"name",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err;
	}

	for _, requiredProperty := range(requiredProperties) {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varCompanyCreate := _CompanyCreate{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varCompanyCreate)

	if err != nil {
		return err
	}

	*o = CompanyCreate(varCompanyCreate)

	return err
}

type NullableCompanyCreate struct {
	value *CompanyCreate
	isSet bool
}

func (v NullableCompanyCreate) Get() *CompanyCreate {
	return v.value
}

func (v *NullableCompanyCreate) Set(val *CompanyCreate) {
	v.value = val
	v.isSet = true
}

func (v NullableCompanyCreate) IsSet() bool {
	return v.isSet
}

func (v *NullableCompanyCreate) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableCompanyCreate(val *CompanyCreate) *NullableCompanyCreate {
	return &NullableCompanyCreate{value: val, isSet: true}
}

func (v NullableCompanyCreate) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableCompanyCreate) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}


