

# Interaction


## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**id** | **UUID** |  |  [optional] |
|**companyId** | **UUID** |  |  [optional] |
|**contactId** | **UUID** |  |  [optional] |
|**interactionType** | [**InteractionTypeEnum**](#InteractionTypeEnum) |  |  [optional] |
|**notes** | **String** |  |  [optional] |
|**interactionDatetime** | **OffsetDateTime** |  |  [optional] |
|**createdBy** | **UUID** |  |  [optional] |
|**createdAt** | **OffsetDateTime** |  |  [optional] |
|**updatedAt** | **OffsetDateTime** |  |  [optional] |



## Enum: InteractionTypeEnum

| Name | Value |
|---- | -----|
| EMAIL | &quot;email&quot; |
| PHONE | &quot;phone&quot; |
| MEETING | &quot;meeting&quot; |
| DEMO | &quot;demo&quot; |
| PROPOSAL | &quot;proposal&quot; |
| FOLLOW_UP | &quot;follow-up&quot; |
| OTHER | &quot;other&quot; |



