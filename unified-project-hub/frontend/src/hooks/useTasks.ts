import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface TaskFilters {
  page?: number;
  limit?: number;
  status?: string;
  project_id?: string;
  assignee_id?: string;
  reviewer_id?: string;
  priority_level?: string;
  effort_estimate?: string;
  search?: string;
  due_date_from?: string;
  due_date_to?: string;
  is_overdue?: boolean;
  show_completed?: boolean;
  parent_task_id?: string;
  sort_by?: string;
  sort_order?: string;
  group_by?: string;
}

interface TasksResponse {
  tasks: any[];
  grouped_tasks?: Record<string, any[]>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  filters: any;
  sorting: any;
  grouping: any;
}

export function useTasks(filters: TaskFilters = {}) {
  const { token } = useAuth();
  const [data, setData] = useState<TasksResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTasks = async () => {
    if (!token) return;

    try {
      setLoading(true);
      setError(null);

      const queryParams = new URLSearchParams();
      
      // Add all filters to query params
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '' && value !== 'all') {
          queryParams.append(key, String(value));
        }
      });

      const response = await fetch(`/api/tasks?${queryParams.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.status === 'success') {
        setData(result.data);
      } else {
        throw new Error(result.message || 'Failed to fetch tasks');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching tasks:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTasks();
  }, [token, JSON.stringify(filters)]);

  const refetch = () => {
    fetchTasks();
  };

  return {
    data,
    loading,
    error,
    refetch,
  };
}

export function useTaskActions() {
  const { token } = useAuth();

  const updateTaskStatus = async (taskId: string, status: string, reason?: string) => {
    if (!token) throw new Error('No authentication token');

    const response = await fetch(`/api/tasks/${taskId}/status`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status, reason }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.status !== 'success') {
      throw new Error(result.message || 'Failed to update task status');
    }

    return result.data;
  };

  const updateTaskPriority = async (taskId: string, priority_level: string, reason?: string) => {
    if (!token) throw new Error('No authentication token');

    const response = await fetch(`/api/tasks/${taskId}/priority`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ priority_level, reason }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.status !== 'success') {
      throw new Error(result.message || 'Failed to update task priority');
    }

    return result.data;
  };

  const assignTask = async (taskId: string, assignee_id?: string, reviewer_id?: string) => {
    if (!token) throw new Error('No authentication token');

    const response = await fetch(`/api/tasks/${taskId}/assign`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ assignee_id, reviewer_id }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.status !== 'success') {
      throw new Error(result.message || 'Failed to assign task');
    }

    return result.data;
  };

  const deleteTask = async (taskId: string) => {
    if (!token) throw new Error('No authentication token');

    const response = await fetch(`/api/tasks/${taskId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.status !== 'success') {
      throw new Error(result.message || 'Failed to delete task');
    }

    return result;
  };

  const createTask = async (taskData: any) => {
    if (!token) throw new Error('No authentication token');

    const response = await fetch('/api/tasks', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(taskData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.status !== 'success') {
      throw new Error(result.message || 'Failed to create task');
    }

    return result.data;
  };

  const updateTask = async (taskId: string, taskData: any) => {
    if (!token) throw new Error('No authentication token');

    const response = await fetch(`/api/tasks/${taskId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(taskData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (result.status !== 'success') {
      throw new Error(result.message || 'Failed to update task');
    }

    return result.data;
  };

  return {
    updateTaskStatus,
    updateTaskPriority,
    assignTask,
    deleteTask,
    createTask,
    updateTask,
  };
}
